<?php

namespace App\Commands;

use Exception;
use Config\Services;
use CodeIgniter\CLI\CLI;
use App\Models\BankModel;
use App\Models\SapoModel;
use App\Models\CompanyModel;
use App\Models\HaravanModel;
use App\Models\WebhooksModel;
use App\Models\SmsParserModel;
use App\Actions\PayCodeDetector;
use App\Models\BankAccountModel;
use CodeIgniter\CLI\BaseCommand;
use App\Libraries\RabbitMQClient;
use App\Models\PgTransactionModel;
use PhpAmqpLib\Message\AMQPMessage;
use App\Models\NotificationTelegramModel;
use App\Exceptions\PaymentGatewayException;
use App\Features\PaymentGateway\MpgsProfile;
use App\Models\NotificationLarkMessengerModel;
use App\Actions\OutputDevice\ExecuteOutputDevice;
use App\Actions\Shopify\ExecuteShopifyWebhooksAction;
use App\Features\PaymentGateway\PaymentGatewayFeature;
use App\Actions\Transaction\CreateTransactionQueueAction;
use App\Features\PaymentGateway\Contexts\MpgsCheckoutContext;
use App\Actions\GoogleSheets\ExecuteGoogleSheetsWebhooksAction;
use App\Actions\Merchant\PushMerchantTransactionNotificationAction;
use App\Actions\Mobile\PushMobileTransactionNotificationQueueAction;

class MpgsOrderFulfillmentWorker extends BaseCommand
{
    /**
     * The Command's Group
     *
     * @var string
     */
    protected $group = 'Worker';

    /**
     * The Command's Name
     *
     * @var string
     */
    protected $name = 'worker:mpgs-order-fulfillment';

    /**
     * The Command's Description
     *
     * @var string
     */
    protected $description = '';

    /**
     * The Command's Usage
     *
     * @var string
     */
    protected $usage = 'worker:mpgs-order-fulfillment';

    /**
     * The Command's Arguments
     *
     * @var array
     */
    protected $arguments = [];

    /**
     * The Command's Options
     *
     * @var array
     */
    protected $options = [];

    /**
     * Actually execute a command.
     *
     * @param array $params
     */
    public function run(array $params)
    {
        $rabbitmq = new RabbitMQClient();
        $connected = $rabbitmq->connect();
    
        if (!$connected) {
            CLI::error('Cannot connect to RabbitMQ.');
            return;
        }
    
        $rabbitmq->queueDeclare('mpgs_order_fulfillment');
    
        CLI::write("[*] Waiting for messages. To exit press CTRL+C\n");
    
        $callback = function ($msg) {
            
            CLI::write("[" . date('Y-m-d H:i:s') . "] Received " . $msg->getBody() . "\n");
            $this->debug(sprintf('%s | PID: %s', $msg->getBody(), getmygid()));
            
            $jsonBody = json_decode($msg->getBody(), true);
            
            $rules = [
                'merchant' => ['required', 'string'],
                'order_id' => ['required', 'string'],
                'payment_method' => ['required', 'string', 'in_list[CARD,BANK_TRANSFER]'],
                'transaction_id' => ['permit_empty', 'string']
            ];
            
            $validator = service('validation');
            $validator->setRules($rules);
            
            if (!$validator->run($jsonBody)) {
                $this->logError(sprintf('Invalid message: %s | Raw body: %s', json_encode($validator->getErrors()), $msg->getBody()));
                $msg->ack();
                return;
            }
            
            $merchant = trim($jsonBody['merchant']);
            $paymentMethod = trim($jsonBody['payment_method']);
            
            $db = db_connect();
            
            /** @var MpgsCheckoutContext|null */
            $checkoutContext = null;
            
            try {
                $paymentGatewayFeature = new PaymentGatewayFeature;
                $paymentGatewayFeature->withCheckoutContext($merchant, $paymentMethod, MpgsProfile::class);

                /** @var MpgsCheckoutContext */
                $checkoutContext = $paymentGatewayFeature->checkoutContext();
                $checkoutContext->loadPaymentFields($jsonBody);
                $checkoutContext->loadPgSession();
                
                if (!$checkoutContext->pgSession) {
                    $this->logError('PG session not found', $checkoutContext);
                    $msg->ack();
                    $db->close();
                    return;
                }
                
                if ($checkoutContext->isSessionCancelled()) {
                    $msg->ack();
                    $db->close();
                    return;
                }
                 
                $checkoutContext->loadPgCustomer();
                $checkoutContext->loadPgCard();
                
                if ($checkoutContext->isAgreementPayment()) {
                    return $this->handleAgreementOrderFulfillmentMessage($msg, $checkoutContext);
                } else {
                    return $this->handleOrderFulfillmentMessage($msg, $checkoutContext);
                }
            } catch (\Throwable $e) {
                if ($e instanceof PaymentGatewayException) {
                    $e->forceLogging();
                } else {
                    $this->logError(sprintf('%s | Raw body: %s | %s', $e->getMessage(), $msg->getBody(), $e->getTraceAsString()), $checkoutContext);
                    
                    if ($checkoutContext && $checkoutContext->pgSession) {
                        $checkoutContext->markSessionAsFailed(['error_code' => 500, 'error_desc' => 'Internal server error']);
                    }
                }
                
                $msg->ack();
            }
            
            $db->close();
        };
        
        $rabbitmq->channel->basic_qos(null, 1, false);
        $rabbitmq->channel->basic_consume($rabbitmq->getQueueDeclaration('mpgs_order_fulfillment', 'name'), '', false, false, false, false, $callback);

        try {
            $rabbitmq->channel->consume();
        } catch (\Throwable $e) {
            $this->debug(sprintf('%s | %s', $e->getMessage(), $e->getTraceAsString()));
            CLI::error(sprintf('%s | %s', $e->getMessage(), $e->getTraceAsString()));
        }

        $rabbitmq->close();
    }
    
    protected function handleAgreementOrderFulfillmentMessage(AMQPMessage $msg, MpgsCheckoutContext $checkoutContext)
    {
        $orderAuthenticationStatus = $checkoutContext->getSessionMeta('order_authentication_status');
        
        $orderDetails = $checkoutContext->profile->queryDROrder($checkoutContext->orderId);

        if (!$orderDetails) {
            $msg->ack();
            return $this->reAttempt($checkoutContext);
        }
        
        $pgOrderId = $checkoutContext->profile->syncDROrder($orderDetails, [
            'customer_id' => $checkoutContext->customerId,
            'order_description' => $checkoutContext->orderDescription,
            'order_discount_description' => $checkoutContext->orderDiscountDescription,
            'custom_data' => $checkoutContext->customData,
            'user_agent' => $checkoutContext->pgSession->user_agent,
            'ip_address' => $checkoutContext->pgSession->ip_address
        ]);
        
        if (!$pgOrderId) {
            throw new Exception('PG order insert failed');
        }
        
        $checkoutContext->loadPgOrder();
        $checkoutContext->loadPgTransaction();
        
        if ($checkoutContext->determineIfOrderAuthenticationInitiated()) {
            $orderAuthenticationStatus = 'INITIATED';
            $checkoutContext->markSessionAsPending(['meta' => json_encode(['order_authentication_status' => $orderAuthenticationStatus])]);
        } else if ($checkoutContext->determineIfOrderAuthenticationSucessful()) {
            $orderAuthenticationStatus = 'SUCCESSFUL';
            $checkoutContext->markSessionAsPending(['meta' => json_encode(['order_authentication_status' => $orderAuthenticationStatus])]);
        } else if ($checkoutContext->determineIfOrderAuthenticationFailed()) {
            $orderAuthenticationStatus = 'FAILED';
            $checkoutContext->markSessionAsFailed(['meta' => json_encode(['order_authentication_status' => $orderAuthenticationStatus])]);
        }
        
        CLI::write('orderAuthenticationStatus:' . $orderAuthenticationStatus);
        
        if (is_null($orderAuthenticationStatus)) {
            $msg->ack();
            return $this->reAttempt($checkoutContext);
        }
        
        if ($checkoutContext->isSessionFulfilled()) {
            $msg->ack();
            return;
        }
        
        if ($checkoutContext->isSessionPending() && $orderAuthenticationStatus === 'SUCCESSFUL') {
            $pgAgreementId = $checkoutContext->subscribeToAgreement();
            
            if (!$pgAgreementId) {
                throw new Exception('PG agreement insert failed');
            }
            
            return $this->handleOrderFulfillmentMessage($msg, $checkoutContext);
        }
            
        $msg->ack();
    }
    
    protected function handleOrderFulfillmentMessage(AMQPMessage $msg, MpgsCheckoutContext $checkoutContext)
    {
        $pgOrderId = null;
        $isOrderVerified = false;
        $isOrderCaptured = false;
        $isOrderVoid = false;
        $newDRApprvovedPaymentTransactions = [];
        $newDRApprvovedVoidTransactions = [];
        $drOrder = $checkoutContext->profile->queryDROrder($checkoutContext->orderId);
        
        if ($drOrder) {
            if ($checkoutContext->isOnetimePayment()) {
                $checkoutContext->markSessionAsPending();
            }
            
            $pgOrderId = $checkoutContext->profile->syncDROrder($drOrder, [
                'customer_id' => $checkoutContext->customerId,
                'order_description' => $checkoutContext->orderDescription,
                'order_discount_description' => $checkoutContext->orderDiscountDescription,
                'custom_data' => $checkoutContext->customData,
                'user_agent' => $checkoutContext->pgSession->user_agent,
                'ip_address' => $checkoutContext->pgSession->ip_address
            ]);
            $checkoutContext->loadPgOrder();
            
            if ($checkoutContext->pgOrder && property_exists($drOrder, 'transaction')) {
                foreach ($drOrder->transaction ?? [] as $drTransaction) {
                    if ($checkoutContext->profile->isNewDRApprovedPaymentTransaction($pgOrderId, $drTransaction)) {
                        $newDRApprvovedPaymentTransactions[] = $drTransaction;
                    }
                    
                    if ($checkoutContext->profile->isNewDRVoidPaymentTransaction($pgOrderId, $drTransaction)) {
                        $newDRApprvovedVoidTransactions[] = $drTransaction;
                    }
                }
            }
            
            $isOrderVerified = $checkoutContext->profile->determineIfDROrderVerified($drOrder);
            $isOrderCaptured = $checkoutContext->profile->determineIfDROrderCaptured($drOrder);
            $isOrderVoid = $checkoutContext->profile->determineIfDROrderCancelledByVoidTransaction($drOrder);
        }
        
        if (!$isOrderCaptured && !$isOrderVerified && !$isOrderVoid) {
            try {
                $msg->ack();
            } catch (\Throwable $e) {
                //
            }
            
            return $this->reAttempt($checkoutContext, 5);
        }
        
        if ($isOrderCaptured || $isOrderVoid) {
            try {
                $checkoutContext->markSessionAsFulfilled();
                
                foreach (array_merge($newDRApprvovedPaymentTransactions, $newDRApprvovedVoidTransactions) as $drTransaction) {
                    $checkoutContext->profile->handleNewDRTransactionApproved($pgOrderId, $drTransaction);
                }
            } catch (\Throwable $e) {
                $checkoutContext->markSessionAsFailed(['error_code' => 500, 'error_desc' => 'Failed to handle approved transaction']);
                
                $this->logError(sprintf('Failed to handle new DR approved transaction: %s | DR transaction: %s', $e->getMessage(), json_encode($drTransaction)), $checkoutContext);
            }
        } elseif ($isOrderVerified) {
            try {
                $checkoutContext->storePaymentCard();
                $checkoutContext->markSessionAsFulfilled();
            } catch (\Throwable $e) {
                $checkoutContext->markSessionAsFailed(['error_code' => 500, 'error_desc' => 'Failed to store payment card']);
                
                $this->logError(sprintf('Failed to store payment card: %s', $e->getMessage()), $checkoutContext);
            }
        }
        
        try {
            $msg->ack();
        } catch (\Throwable $e) {
            //
        }
    }
    
    protected function reAttempt(MpgsCheckoutContext $checkoutContext, int $delaySeconds = 0): void
    {
        try {
            $checkoutContext->requestOrderFulfillment($delaySeconds);
        } catch (\Throwable $e) {
            $this->debug(sprintf('Failed to re-attempt: %s | %s', $e->getMessage(), $e->getTraceAsString()), $checkoutContext);
        }
    }
    
    protected function debug(string $message, ?MpgsCheckoutContext $checkoutContext = null): void
    {
        if ($checkoutContext) {
            log_message('error', sprintf(
                '[MPGS_ORDER_FULFILLMENT_WORKER#%s] %s | Payment fields: %s', 
                $checkoutContext->merchant->merchant_id, 
                $message, 
                json_encode($checkoutContext->getPaymentFields())
            ));
        } else {
            log_message('error', sprintf('[MPGS_ORDER_FULFILLMENT_WORKER] %s', $message));
        }
    }
    
    protected function logError(string $message, ?MpgsCheckoutContext $checkoutContext = null): void
    {
        if ($checkoutContext) {
            log_message('error', sprintf(
                '[MPGS_ORDER_FULFILLMENT_WORKER#%s] %s | Fields: %s', 
                $checkoutContext->merchant->merchant_id, 
                $message, 
                json_encode($checkoutContext->getPaymentFields())
            ));
        } else {
            log_message('error', sprintf('[MPGS_ORDER_FULFILLMENT_WORKER] %s', $message));
        }
    }
}
