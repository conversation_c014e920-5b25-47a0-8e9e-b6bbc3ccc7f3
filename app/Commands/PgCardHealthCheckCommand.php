<?php

namespace App\Commands;

use App\Features\PaymentGateway\MpgsProfile;
use App\Features\PaymentGateway\PaymentGatewayFeature;
use App\Models\CompanySubscriptionModel;
use App\Models\PgAgreementModel;
use App\Models\PgCardModel;
use App\Models\PgProfileModel;
use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;

class PgCardHealthCheckCommand extends BaseCommand
{
    /**
     * The Command's Group
     *
     * @var string
     */
    protected $group = 'Payment Gateway';

    /**
     * The Command's Name
     *
     * @var string
     */
    protected $name = 'pg:card-health-check';

    /**
     * The Command's Description
     *
     * @var string
     */
    protected $description = '';

    /**
     * The Command's Usage
     *
     * @var string
     */
    protected $usage = 'pg:card-health-check';

    /**
     * The Command's Arguments
     *
     * @var array
     */
    protected $arguments = [];

    /**
     * The Command's Options
     *
     * @var array
     */
    protected $options = [];

    /**
     * Actually execute a command.
     *
     * @param array $params
     */
    public function run(array $params)
    {
        $pgCardModel = model(PgCardModel::class);
        $pgAgreementModel = model(PgAgreementModel::class);
        
        $pgCards = $pgCardModel->where('status', 'VALID')->get()->getResult();
        
        foreach ($pgCards as $pgCard) {
            $paymentGatewayFeature = new PaymentGatewayFeature;
            
            $pgProfile = model(PgProfileModel::class)->where('id', $pgCard->pg_profile_id)->first();
            
            if (!$pgProfile || !$pgProfile->active) {
                continue;
            }
            
            $pgMerchant = $paymentGatewayFeature->getPgMerchantById($pgCard->pg_merchant_id);
            
            if (!$pgMerchant || !$pgMerchant->active) {
                continue;
            }
            
            $profile = $paymentGatewayFeature->resolveProfileById($pgCard->pg_profile_id);
            
            if ($profile instanceof MpgsProfile) {
                $client = $profile->makeRestApiClient();
                $response = $client->retrieveToken($pgCard->payment_token);
                
                if ($response->getStatusCode() !== 200) {
                    $pgCardModel->where('id', $pgCard->id)->set(['status' => 'INVALID'])->update();
                    $pgCard->status = 'INVALID';
                }
                
                $responseJson = json_decode($response->getBody());
                
                $safeCardUpdateData = [
                    'status' => $response->status ?? $pgCard->status,
                    'last_used_at' => $responseJson->usage->lastUsedTime ?? $pgCard->last_used_at
                ];
                
                try {
                    $updated = $pgCardModel->where('id', $pgCard->id)->set($safeCardUpdateData)->update();
                    
                    if (!$updated) {
                        continue;
                    }
                    
                    if ($safeCardUpdateData['status'] === 'VALID') {
                        continue;
                    }
                    
                    $pgAgreements = $pgAgreementModel->where('pg_card_id', $pgCard->id)->get()->getResult();
                    
                    if (!count($pgAgreements)) {
                        continue;
                    }
                    
                    $pgAgreementModel->whereIn('id', array_column($pgAgreements, 'id'))->set(['auto_renew' => 0])->update();
                    
                    // TODO: notify merchant
                    
                    CLI::write('Turned off auto renew agreement: ' . count($pgAgreements));
                } catch (\Throwable $e) {
                    log_message('error', sprintf('[PaymentGateywayCardHealthCheckCommand] %s | %s', $e->getMessage(), $e->getTraceAsString()));
                    
                    CLI::error($e->getMessage());
                }
            }
        }
    }
}
