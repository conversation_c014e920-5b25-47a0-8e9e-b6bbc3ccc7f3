<?php

namespace App\Models;

use CodeIgniter\Model;

class PgSessionModel extends Model
{
    protected $DBGroup          = 'default';
    protected $table            = 'tb_autopay_pg_session';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $insertID         = 0;
    protected $returnType       = 'object';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = ['queue_name', 'trace_id', 'company_id', 'pg_merchant_id', 'pg_profile_id', 'pg_order_id', 'fields', 'meta', 'status', 'error_code', 'error_desc', 'user_agent', 'ip_address', 'expires_at'];
}
