<?php

namespace App\Models;

use CodeIgniter\Model;

class PgAgreementModel extends Model
{
    protected $DBGroup          = 'default';
    protected $table            = 'tb_autopay_pg_agreement';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $insertID         = 0;
    protected $returnType       = 'object';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = ['company_id', 'pg_merchant_id', 'pg_customer_id', 'pg_profile_id', 'pg_order_id', 'pg_card_id', 'agreement_name', 'agreement_id', 'type', 'status', 'active', 'auto_renew', 'amount_variability', 'amount_per_payment', 'min_amount_per_payment', 'max_amount_per_payment', 'start_date', 'expiry_date', 'next_due_date', 'payment_frequency', 'minimum_days_between_payments', 'created_at', 'updated_at'];
}
