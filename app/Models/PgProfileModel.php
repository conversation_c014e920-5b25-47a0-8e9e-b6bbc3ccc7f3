<?php

namespace App\Models;

use CodeIgniter\Model;

class PgProfileModel extends Model
{
    protected $DBGroup          = 'default';
    protected $table            = 'tb_autopay_pg_profile';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $insertID         = 0;
    protected $returnType       = 'object';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = [
        'pg_merchant_id', 
        'pg_payment_method_id',
        'type', 
        'bank_account_id', 
        'napas_profile_id', 
        'mpgs_profile_id',
        'default', 
        'active',
        'company_id',
    ];
    
    public function getMpgsProfile(int $profileId): ?object
    {
        return model(PgMpgsProfileModel::class)->where('pg_profile_id', $profileId)->first();
    }
}
