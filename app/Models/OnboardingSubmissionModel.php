<?php

namespace App\Models;

use CodeIgniter\Model;

class OnboardingSubmissionModel extends Model
{
    protected $table = 'tb_autopay_pg_onboarding_submissions';

    protected $returnType = 'object';

    protected $allowedFields = [
        'company_id',
        'payment_method',
        'status',
        'onboard_data',
        'submitted_at',
        'reviewed_at',
        'current_step',
    ];

    public function createOnboardingRecord($companyId, $paymentMethod, $initialStep = 0)
    {
        $data = [
            'company_id' => $companyId,
            'payment_method' => $paymentMethod,
            'status' => 'pending',
            'onboard_data' => json_encode([]),
            'current_step' => $initialStep,
        ];

        return $this->insert($data);
    }

    public function getByCompanyAndMethod($companyId, $feature)
    {
        return $this->where([
            'company_id' => $companyId,
            'payment_method' => $feature,
        ])->first();
    }

    public function updateOnboardData($companyId, $feature, $data)
    {
        if ($existing = $this->getByCompanyAndMethod($companyId, $feature)) {
            $currentData = json_decode($existing->onboard_data, true) ?: [];
            $mergedData = array_merge($currentData, $data);

            return $this->update($existing->id, [
                'onboard_data' => json_encode($mergedData)
            ]);
        }

        $id = $this->createOnboardingRecord($companyId, $feature);

        return $this->update($id, [
            'onboard_data' => json_encode($data)
        ]);
    }

    public function submitOnboarding($companyId, $paymentMethod)
    {
        return $this->where([
            'company_id' => $companyId,
            'payment_method' => $paymentMethod
        ])->set([
            'status' => 'submitted',
            'submitted_at' => date('Y-m-d H:i:s')
        ])->update();
    }

    public function updateStatus($companyId, $paymentMethod, $status)
    {
        $data = ['status' => $status];

        if ($status === 'approved' || $status === 'rejected') {
            $data['reviewed_at'] = date('Y-m-d H:i:s');
        }

        return $this->where([
            'company_id' => $companyId,
            'payment_method' => $paymentMethod
        ])->set($data)->update();
    }

    public function setCurrentStep($companyId, $paymentMethod, $step)
    {
        if (! $existing = $this->getByCompanyAndMethod($companyId, $paymentMethod)) {
            $this->createOnboardingRecord($companyId, $paymentMethod, $step);

            return true;
        }

        return $this->update($existing->id, [
            'current_step' => $step,
        ]);
    }

    public function getCurrentStep($companyId, $paymentMethod)
    {
        $existing = $this->getByCompanyAndMethod($companyId, $paymentMethod);

        return $existing->current_step ?? 0;
    }

    public function getOnboardData($companyId, $paymentMethod)
    {
        if (! $existing = $this->getByCompanyAndMethod($companyId, $paymentMethod)) {
            return [];
        }

        $data = json_decode($existing->onboard_data, true);

        return is_array($data) ? $data : [];
    }

    public function getOrCreate($companyId, $paymentMethod, $initialStep = 0)
    {
        if ($existing = $this->getByCompanyAndMethod($companyId, $paymentMethod)) {
            return $existing;
        }

        $id = $this->createOnboardingRecord($companyId, $paymentMethod, $initialStep);

        return $this->find($id);
    }
}
