<?php

namespace App\Models;

use CodeIgniter\Model;

class CybersourceMerchantModel extends Model
{
    protected $DBGroup          = 'default';
    protected $table            = 'tb_autopay_cybersource_merchant';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $insertID         = 0;
    protected $returnType       = 'object';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = ['merchant_id', 'active', 'company_id'];
    
    public function restKeys($id = null): Model
    {
        $builder = model(CybersourceMerchantRestKeyModel::class);
        
        if ($id) {
            $builder->where('merchant_id', $id);
        }
        
        return $builder;
    }
}
