<?php

namespace App\Models;

use CodeIgniter\Model;

class PgCustomerModel extends Model
{
    protected $DBGroup          = 'default';
    protected $table            = 'tb_autopay_pg_customer';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $insertID         = 0;
    protected $returnType       = 'object';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = ['company_id', 'pg_merchant_id', 'customer_id'];
}
