<?php

namespace App\Models;

use App\Traits\Models\HasDataTables;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\Model;

class PgBranchModel extends Model
{
    use HasDataTables;

    protected $table = 'tb_autopay_pg_branch';

    protected $returnType = 'object';

    protected $allowedFields = [
        'pg_merchant_id',
        'name',
        'code',
        'active',
        'company_id',
    ];

    protected $companyId = null;

    public function setCompanyId($companyId): self
    {
        $this->companyId = $companyId;

        return $this;
    }

    protected function modifyDatatablesQuery(RequestInterface $request): void
    {
        if ($this->companyId) {
            $this->where('company_id', $this->companyId);
        }
    }

    public function countAll(): int
    {
        return $this
            ->where('company_id', $this->companyId)
            ->countAllResults();
    }
}
