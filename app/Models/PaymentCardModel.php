<?php

namespace App\Models;

use CodeIgniter\Model;

class PaymentCardModel extends Model
{
    protected $DBGroup          = 'default';
    protected $table            = 'tb_autopay_payment_card';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $insertID         = 0;
    protected $returnType       = 'object';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = ['company_id', 'pg_profile_id', 'payment_token', 'card_number', 'card_holder_name', 'card_expiry', 'card_brand', 'card_funding_method', 'meta', 'last_used_at'];
}
