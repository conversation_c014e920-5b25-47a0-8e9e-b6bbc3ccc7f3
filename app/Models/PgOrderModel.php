<?php

namespace App\Models;

use CodeIgniter\Model;

class PgOrderModel extends Model
{
    protected $DBGroup          = 'default';
    protected $table            = 'tb_autopay_pg_order';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $insertID         = 0;
    protected $returnType       = 'object';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = ['company_id', 'pg_merchant_id', 'pg_customer_id', 'pg_profile_id', 'pg_agreement_id', 'order_id', 'order_status', 'authentication_status', 'order_amount', 'order_currency', 'order_description', 'order_invoice_number', 'order_discount_amount', 'order_discount_code', 'order_discount_description', 'order_tax_amount', 'custom_data', 'user_agent', 'ip_address'];
}
