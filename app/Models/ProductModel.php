<?php

namespace App\Models;

use CodeIgniter\Model;
use Config\Billing;

class ProductModel extends Model
{
    protected $table = 'tb_autopay_product';

    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType     = 'object';
    protected $allowedFields = ['name', 'description', 'monthly_transaction_limit', 'bank_account_limit', 'telegram_intergration_limit', 'webhook_intergration_limit', 'monthly_telegram_send_limit', 'monthly_webhooks_send_limit', 'price_monthly', 'price_annually', 'hidden', 'sort_order', 'dedicated_sim', 'tax_rate', 'active', 'sms_allow', 'billing_type', 'channel_partner_id', 'shop_limit'];

    protected $createdField  = 'created_at';

    protected $useSoftDeletes = false;


    protected $useTimestamps     = false;

    protected $validationRules    = [];
    protected $skipValidation     = false;

    protected $order = ['tb_autopay_product.sort_order' => 'asc'];

    protected $builder;

    public function getPlans()
    {
        return $this
            ->select('tb_autopay_product.*')
            ->join('tb_autopay_product_promotions', 'tb_autopay_product_promotions.product_id = tb_autopay_product.id', 'left')
            ->where('tb_autopay_product.hidden', false)
            ->where('tb_autopay_product.channel_partner_id', null)
            ->where('tb_autopay_product_promotions.id IS NULL')
            ->where('tb_autopay_product.shop_limit', null)
            ->orderBy('tb_autopay_product.sort_order', 'asc')
            ->findAll();
    }

    public function getFreePlan()
    {
        return $this
            ->select('tb_autopay_product.*')
            ->join('tb_autopay_product_promotions', 'tb_autopay_product_promotions.product_id = tb_autopay_product.id', 'left')
            ->where('tb_autopay_product.hidden', false)
            ->where('tb_autopay_product.billing_type', 'Free')
            ->where('tb_autopay_product.price_monthly', 0)
            ->where('tb_autopay_product.channel_partner_id', null)
            ->where('tb_autopay_product_promotions.id IS NULL')
            ->where('tb_autopay_product.shop_limit', null)
            ->orderBy('tb_autopay_product.sort_order', 'asc')
            ->first();
    }

    public function getApiPlans()
    {
        return $this
            ->select('tb_autopay_product.*')
            ->where('tb_autopay_product.hidden', false)
            ->where('tb_autopay_product.sms_allow', false)
            ->where('tb_autopay_product.billing_type !=', 'Free')
            ->where('tb_autopay_product.price_monthly !=', 0)
            ->where('tb_autopay_product.channel_partner_id', null)
            ->where('tb_autopay_product.shop_limit', null)
            ->orderBy('tb_autopay_product.sort_order', 'asc')
            ->findAll();
    }

    public function getSmsPlans()
    {
        return $this
            ->select('tb_autopay_product.*')
            ->join('tb_autopay_product_promotions', 'tb_autopay_product_promotions.product_id = tb_autopay_product.id', 'left')
            ->where('tb_autopay_product.id !=', config(Billing::class)->speakerBillingDefaultProductId)
            ->where('tb_autopay_product.hidden', false)
            ->where('tb_autopay_product.sms_allow', true)
            ->where('tb_autopay_product.channel_partner_id', null)
            ->where('tb_autopay_product_promotions.id IS NULL')
            ->where('tb_autopay_product.shop_limit', null)
            ->orderBy('tb_autopay_product.sort_order', 'asc')
            ->findAll();
    }

    public function getPromotionPlans()
    {
        return $this
            ->select([
                'tb_autopay_product.id',
                'tb_autopay_product.name',
                'tb_autopay_product.monthly_transaction_limit',
                'tb_autopay_product_promotions.id as promotion_id',
                'tb_autopay_product_promotions.bank_id',
                'tb_autopay_product_promotions.compare_at_price',
                'tb_autopay_product_promotions.billing_cycle',
                'tb_autopay_product_promotions.start_date',
                'tb_autopay_product_promotions.end_date',
                'tb_autopay_product_promotions.requirements',
                'tb_autopay_product_promotions.supported_account_types',
                'tb_autopay_bank.brand_name',
                'tb_autopay_bank.icon_path',
            ])
            ->join('tb_autopay_product_promotions', 'tb_autopay_product_promotions.product_id = tb_autopay_product.id')
            ->join('tb_autopay_bank', 'tb_autopay_bank.id = tb_autopay_product_promotions.bank_id')
            ->where('tb_autopay_product.hidden', false)
            ->where('tb_autopay_product.billing_type', 'Recurring')
            ->where('tb_autopay_product.active', true)
            ->where('tb_autopay_product.shop_limit', null)
            ->where('tb_autopay_product.channel_partner_id', null)
            ->where('tb_autopay_product_promotions.active', true)
            ->where('tb_autopay_product_promotions.start_date <=', date('Y-m-d'))
            ->where('tb_autopay_product_promotions.end_date >=', date('Y-m-d'))
            ->orderBy('tb_autopay_product.sort_order', 'ASC')
            ->findAll();
    }
}
