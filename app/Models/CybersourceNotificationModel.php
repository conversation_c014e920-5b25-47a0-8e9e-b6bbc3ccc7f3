<?php

namespace App\Models;

use CodeIgniter\Model;

class CybersourceNotificationModel extends Model
{
    protected $DBGroup          = 'default';
    protected $table            = 'tb_autopay_cybersource_notification';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $insertID         = 0;
    protected $returnType       = 'object';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields = [
        'req_card_number',
        'req_locale',
        'signature',
        'req_card_type_selection_indicator',
        'auth_trans_ref_no',
        'payment_token',
        'payment_token_instrument_identifier_id',
        'req_card_expiry_date',
        'auth_cavv_result',
        'merchant_advice_code',
        'card_type_name',
        'reason_code',
        'auth_amount',
        'auth_response',
        'bill_trans_ref_no',
        'payment_account_reference',
        'req_payment_method',
        'request_token',
        'auth_cavv_result_raw',
        'auth_time',
        'req_amount',
        'transaction_id',
        'req_currency',
        'req_card_type',
        'decision',
        'message',
        'signed_field_names',
        'req_transaction_uuid',
        'auth_avs_code',
        'auth_code',
        'payment_token_instrument_identifier_new',
        'req_transaction_type',
        'req_access_key',
        'req_profile_id',
        'req_reference_number',
        'payment_token_instrument_identifier_status',
        'auth_reconciliation_reference_number',
        'signed_date_time',
        'full_payload'
    ];
}
