<?php

namespace App\Models;

use App\Actions\PayCodeDetector;
use CodeIgniter\Model;
use DateTime;

class TransactionsModel extends Model
{
    protected $table = 'tb_autopay_sms_parsed';

    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType     = 'object';
    protected $allowedFields = ['sms_id','gateway','transaction_date', 'account_number','amount_in','amount_out', 'accumulated','transaction_content','reference_number','body','parser_status', 'code','webhooks_success', 'webhooks_failed','sub_account','webhooks_verify_payment','chat_push_message','from_bin', 'from_account_number', 'from_account_name', 'bank_account_id', 'merchant_id'];

    protected $createdField  = 'datecreated';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    protected $useSoftDeletes = true;


    protected $useTimestamps     = false;

    protected $validationRules    = [
        
    ];
    protected $skipValidation     = false;
    
    protected $order = ['tb_autopay_sms_parsed.transaction_date'=>'desc'];
 
    protected $builder;

    private function _getDatatablesQuery($company_id, $bank_account_id = FALSE, $role='User', $configBankSubAccount = 'on', $configPayCode = 'on', $hide_amount_out, $user_id = null, $date_range = null, $transaction_type = null, $bank_sub_account = null, $amount = null) {

        $request = \Config\Services::request();

        if($configBankSubAccount == 'on' && $configPayCode == 'on'){
            $column_order = array(null, 'tb_autopay_sms_parsed.id', 'tb_autopay_bank_account.account_number','tb_autopay_sms_parsed.sub_account', null, 'tb_autopay_sms_parsed.accumulated', null,'tb_autopay_sms_parsed.transaction_date', 'tb_autopay_sms_parsed.code', 'tb_autopay_sms_parsed.transaction_content','tb_autopay_sms_parsed.webhooks_verify_payment', null, null);
        }elseif ($configBankSubAccount == 'on'){
            $column_order = array(null, 'tb_autopay_sms_parsed.id', 'tb_autopay_bank_account.account_number','tb_autopay_sms_parsed.sub_account', null, 'tb_autopay_sms_parsed.accumulated', null, 'tb_autopay_sms_parsed.transaction_date', 'tb_autopay_sms_parsed.transaction_content','tb_autopay_sms_parsed.webhooks_verify_payment', null, null);
        }elseif ($configPayCode == 'on'){
            $column_order = array(null, 'tb_autopay_sms_parsed.id', 'tb_autopay_bank_account.account_number', null,'tb_autopay_sms_parsed.accumulated', null, 'tb_autopay_sms_parsed.transaction_date','tb_autopay_sms_parsed.code', 'tb_autopay_sms_parsed.transaction_content', 'tb_autopay_sms_parsed.webhooks_verify_payment', null, null);
        }else {
            $column_order = array(null, 'tb_autopay_sms_parsed.id', 'tb_autopay_bank_account.account_number', null,'tb_autopay_sms_parsed.accumulated', null, 'tb_autopay_sms_parsed.transaction_date', 'tb_autopay_sms_parsed.transaction_content', 'tb_autopay_sms_parsed.webhooks_verify_payment', null, null);
        }

        $column_search = array('tb_autopay_sms_parsed.id','tb_autopay_bank.brand_name','tb_autopay_sms_parsed.account_number','tb_autopay_sms_parsed.sub_account','tb_autopay_sms_parsed.transaction_content','tb_autopay_sms_parsed.code','tb_autopay_sms_parsed.amount_out','tb_autopay_sms_parsed.amount_in','tb_autopay_sms_parsed.accumulated','tb_autopay_sms_parsed.reference_number', "DATE_FORMAT(tb_autopay_sms_parsed.transaction_date, '%H:%i:%s %d-%m-%Y')");

       
        $this->builder = $this->db->table($this->table);

        $this->builder->select("tb_autopay_sms_parsed.id ,tb_autopay_bank.brand_name, tb_autopay_bank_account.account_number,tb_autopay_bank_account.account_holder_name,tb_autopay_bank_account.label, DATE_FORMAT(tb_autopay_sms_parsed.transaction_date, '%H:%i:%s %d-%m-%Y') as transaction_date, tb_autopay_sms_parsed.amount_out,tb_autopay_sms_parsed.amount_in,tb_autopay_sms_parsed.accumulated,tb_autopay_sms_parsed.transaction_content,tb_autopay_sms_parsed.code,tb_autopay_sms_parsed.reference_number,tb_autopay_bank_account.company_id, tb_autopay_sms_parsed.webhooks_success,tb_autopay_sms_parsed.webhooks_failed,tb_autopay_sms_parsed.sub_account,tb_autopay_bank.icon_path,tb_autopay_sms_parsed.webhooks_verify_payment,tb_autopay_sms_parsed.chat_push_message,tb_autopay_sms_parsed.datecreated");

        $this->builder->join("tb_autopay_bank","tb_autopay_bank.brand_name=tb_autopay_sms_parsed.gateway");
        $this->builder->join("tb_autopay_bank_account","tb_autopay_bank_account.id=tb_autopay_sms_parsed.bank_account_id");
        
        $this->builder->where("tb_autopay_sms_parsed.parser_status","Success");

        if($hide_amount_out)
            $this->builder->where("tb_autopay_sms_parsed.amount_in>",0);

        if (!empty($date_range)) {
            $date_range = trim($date_range);
    
            if (strpos($date_range, ' - ') !== false) {
                $dates = explode(' - ', $date_range);
                
                if (count($dates) == 2) {
                    $start_date = DateTime::createFromFormat('d-m-Y', trim($dates[0]));
                    $end_date = DateTime::createFromFormat('d-m-Y', trim($dates[1]));
                    
                    if ($start_date && $end_date) {
                        $start_date->setTime(0, 0, 0);
                        $end_date->setTime(23, 59, 59);
                        
                        if ($start_date <= $end_date) {
                            $this->builder->where('tb_autopay_sms_parsed.transaction_date >=', $start_date->format('Y-m-d H:i:s'));
                            $this->builder->where('tb_autopay_sms_parsed.transaction_date <=', $end_date->format('Y-m-d H:i:s'));
                        }
                    }
                }
            } else {
                $single_date = DateTime::createFromFormat('d-m-Y', trim($date_range));
                
                if ($single_date) {
                    $start_date = clone $single_date;
                    $end_date = clone $single_date;
                    
                    $start_date->setTime(0, 0, 0);
                    $end_date->setTime(23, 59, 59);
                    
                    $this->builder->where('tb_autopay_sms_parsed.transaction_date >=', $start_date->format('Y-m-d H:i:s'));
                    $this->builder->where('tb_autopay_sms_parsed.transaction_date <=', $end_date->format('Y-m-d H:i:s'));
                }
            }
        }

        if (!empty($transaction_type)) {
            if ($transaction_type == 'in') {
                $this->builder->where('tb_autopay_sms_parsed.amount_in >', 0);
            } else if ($transaction_type == 'out') {
                $this->builder->where('tb_autopay_sms_parsed.amount_out >', 0);
            }
        }

        // permission for user access bank account
        /*
        // v1 code
        if(!in_array($role,['Admin','SuperAdmin'])) {
            $this->builder->join("tb_autopay_user_permission_bank","tb_autopay_user_permission_bank.bank_account_id=tb_autopay_bank_account.id");
            $session = session();
            $user_session = $session->get('user_logged_in');
 
            $this->builder->where("tb_autopay_user_permission_bank.user_id",$user_session['user_id']);

            $configBankSubAccount = get_configuration('BankSubAccount');
            
            // permission sub account
            if($configBankSubAccount == "on") {
             $this->builder->join("tb_autopay_bank_sub_account","tb_autopay_bank_sub_account.sub_account=tb_autopay_sms_parsed.sub_account");
            $this->builder->join("tb_autopay_user_permission_bank_sub","tb_autopay_user_permission_bank_sub.sub_account_id=tb_autopay_bank_sub_account.id");
            
            $this->builder->groupBy("tb_autopay_sms_parsed.id");

            }

        } */
        
        //v2 code
        if(!in_array($role,['Admin','SuperAdmin'])) { 
            $this->builder->where("tb_autopay_bank_account.id",$bank_account_id);

            //$configBankSubAccount = get_configuration('BankSubAccount');

            // count sub account of this bank
            $bankSubAccountModel = model(BankSubAccountModel::class);
            $count_sub = $bankSubAccountModel->where(['bank_account_id' => $bank_account_id])->countAllResults();

            //
            if($configBankSubAccount == "on" && $count_sub > 0) {
                $this->builder->join("tb_autopay_user_permission_bank","tb_autopay_user_permission_bank.bank_account_id=tb_autopay_sms_parsed.bank_account_id");
                $this->builder->join("tb_autopay_bank_sub_account","tb_autopay_bank_sub_account.sub_account=tb_autopay_sms_parsed.sub_account", 'left');
                $this->builder->join("tb_autopay_user_permission_bank_sub","tb_autopay_user_permission_bank_sub.sub_account_id=tb_autopay_bank_sub_account.id", 'left');
                if ($user_id) {
                    $this->builder->where('tb_autopay_user_permission_bank.user_id', $user_id)
                        ->groupStart()
                            ->where('tb_autopay_user_permission_bank_sub.user_id', $user_id)
                            ->orWhere('tb_autopay_bank_sub_account.id', null)
                        ->groupEnd();
                }
                if(!empty($bank_sub_account) && is_array($bank_sub_account))
                    $this->builder->whereIn('tb_autopay_sms_parsed.sub_account', $bank_sub_account);
                $this->builder->groupBy("tb_autopay_sms_parsed.id");
            }
        } else {
            if($bank_account_id)
                $this->builder->where("tb_autopay_sms_parsed.bank_account_id",$bank_account_id);
            if(!empty($bank_sub_account) && is_array($bank_sub_account))
                $this->builder->whereIn('tb_autopay_sms_parsed.sub_account', $bank_sub_account);
        }

        $this->builder
            ->where("tb_autopay_bank_account.company_id",$company_id)
            ->where("tb_autopay_sms_parsed.deleted_at IS NULL");
       
        if (!empty($amount)) {
            $ranges = explode('-', $amount);
            $min_amount = (int)$ranges[0];
            $max_amount = isset($ranges[1]) ? (int)$ranges[1] : null;
    
            if ($max_amount) {
                $this->builder->groupStart()
                    ->where('(CASE WHEN tb_autopay_sms_parsed.amount_in > 0 
                        THEN tb_autopay_sms_parsed.amount_in 
                        ELSE tb_autopay_sms_parsed.amount_out END) >=', $min_amount)
                    ->where('(CASE WHEN tb_autopay_sms_parsed.amount_in > 0 
                        THEN tb_autopay_sms_parsed.amount_in 
                        ELSE tb_autopay_sms_parsed.amount_out END) <=', $max_amount)
                    ->groupEnd();
            } else {
                $this->builder->where('(CASE WHEN tb_autopay_sms_parsed.amount_in > 0 
                    THEN tb_autopay_sms_parsed.amount_in 
                    ELSE tb_autopay_sms_parsed.amount_out END) >=', $min_amount);
            }
        }

        $i = 0;
     
        foreach ($column_search as $item) {
            if($request->getVar('search')['value'] != NULL) {
                $search_term = str_replace(',', '',trim($request->getVar('search')['value']));
                 
                if($i===0) {
                    $this->builder->groupStart();
                    $this->builder->like($item, $search_term);
                } else {
                    $this->builder->orLike($item, $search_term);
                }
 
                if(count($column_search) - 1 == $i)
                    $this->builder->groupEnd();
            }
            $i++;
        }

        // Per column search
        foreach ($column_search as $key => $item) { 
            
            if($item !=NULL && isset($request->getPost('columns')[$key]['search']['value']) && $request->getPost('columns')[$key]['search']['value'] != "") {
                $this->builder->where($item, $request->getPost('columns')[$key]['search']['value']);
            }
        }
      
        if($request->getVar('order') != NULL && isset($column_order[$request->getVar('order')[0]['column']])  && $column_order[$request->getVar('order')[0]['column']] != NULL) {
            
            $this->builder->orderBy($column_order[$request->getVar('order')[0]['column']], $request->getVar('order')['0']['dir']);

        } else if(isset($this->order)) {

            $order = $this->order;
            $this->builder->orderBy(key($order), $order[key($order)]);

        }
    }

    public function getDatatables($company_id, $bank_account_id = FALSE, $role='User', $configBankSubAccount, $configPayCode, $hide_amount_out, $user_id = null, $date_range = null, $transaction_type = null, $bank_sub_account = null, $amount = null) {
        $request = \Config\Services::request();
        $this->_getDatatablesQuery($company_id, $bank_account_id, $role, $configBankSubAccount, $configPayCode ,$hide_amount_out, $user_id, $date_range, $transaction_type, $bank_sub_account, $amount);
        if($request->getVar('length') != NULL && $request->getVar('length') != -1)
            $this->builder->limit($request->getVar('length'), $request->getVar('start'));
            
        $query = $this->builder->get();

        return $query->getResult();
    }

    public function countTransactionsAll($company_id, $bank_account_id = FALSE, $role='User', $configBankSubAccount, $hide_amount_out, $date_range = null, $transaction_type = null, $bank_sub_account = null, $amount = null) {
     
        $builder = $this->db->table("tb_autopay_sms_parsed");
        $builder->join("tb_autopay_bank_account","tb_autopay_bank_account.id=tb_autopay_sms_parsed.bank_account_id");
        $builder->join("tb_autopay_bank","tb_autopay_bank.brand_name=tb_autopay_sms_parsed.gateway");

        $builder->where("tb_autopay_bank_account.company_id",$company_id);
        $builder->where("tb_autopay_sms_parsed.deleted_at IS NULL");

        if (!empty($date_range)) {
            $date_range = trim($date_range);
    
            if (strpos($date_range, ' - ') !== false) {
                $dates = explode(' - ', $date_range);
                
                if (count($dates) == 2) {
                    $start_date = DateTime::createFromFormat('d-m-Y', trim($dates[0]));
                    $end_date = DateTime::createFromFormat('d-m-Y', trim($dates[1]));
                    
                    if ($start_date && $end_date) {
                        $start_date->setTime(0, 0, 0);
                        $end_date->setTime(23, 59, 59);
                        
                        if ($start_date <= $end_date) {
                            $this->builder->where('tb_autopay_sms_parsed.transaction_date >=', $start_date->format('Y-m-d H:i:s'));
                            $this->builder->where('tb_autopay_sms_parsed.transaction_date <=', $end_date->format('Y-m-d H:i:s'));
                        }
                    }
                }
            } else {
                $single_date = DateTime::createFromFormat('d-m-Y', trim($date_range));
                
                if ($single_date) {
                    $start_date = clone $single_date;
                    $end_date = clone $single_date;
                    
                    $start_date->setTime(0, 0, 0);
                    $end_date->setTime(23, 59, 59);
                    
                    $this->builder->where('tb_autopay_sms_parsed.transaction_date >=', $start_date->format('Y-m-d H:i:s'));
                    $this->builder->where('tb_autopay_sms_parsed.transaction_date <=', $end_date->format('Y-m-d H:i:s'));
                }
            }
        }

        if (!empty($transaction_type)) {
            if ($transaction_type == 'in') {
                $builder->where('tb_autopay_sms_parsed.amount_in >', 0);
            } else if ($transaction_type == 'out') {
                $builder->where('tb_autopay_sms_parsed.amount_out >', 0);
            }
        }

        if (!empty($amount)) {
            $ranges = explode('-', $amount);
            $min_amount = (int)$ranges[0];
            $max_amount = isset($ranges[1]) ? (int)$ranges[1] : null;
    
            if ($max_amount) {
                $builder->groupStart()
                    ->where('(CASE WHEN tb_autopay_sms_parsed.amount_in > 0 
                        THEN tb_autopay_sms_parsed.amount_in 
                        ELSE tb_autopay_sms_parsed.amount_out END) >=', $min_amount)
                    ->where('(CASE WHEN tb_autopay_sms_parsed.amount_in > 0 
                        THEN tb_autopay_sms_parsed.amount_in 
                        ELSE tb_autopay_sms_parsed.amount_out END) <=', $max_amount)
                    ->groupEnd();
            } else {
                $builder->where('(CASE WHEN tb_autopay_sms_parsed.amount_in > 0 
                    THEN tb_autopay_sms_parsed.amount_in 
                    ELSE tb_autopay_sms_parsed.amount_out END) >=', $min_amount);
            }
        }

        if($hide_amount_out)
            $builder->where("tb_autopay_sms_parsed.amount_in>",0);


        if(!in_array($role,['Admin','SuperAdmin'])) { 
            $builder->where("tb_autopay_sms_parsed.bank_account_id",$bank_account_id);

            // count sub account of this bank
            $bankSubAccountModel = model(BankSubAccountModel::class);
            $count_sub = $bankSubAccountModel->where(['bank_account_id' => $bank_account_id])->countAllResults();

            //
            if($configBankSubAccount == "on" && $count_sub > 0) {

                $builder->join("tb_autopay_bank_sub_account","tb_autopay_bank_sub_account.sub_account=tb_autopay_sms_parsed.sub_account");
                $builder->join("tb_autopay_user_permission_bank_sub","tb_autopay_user_permission_bank_sub.sub_account_id=tb_autopay_bank_sub_account.id");
               // $builder->groupBy("tb_autopay_sms_parsed.id");
            }
        } else {
            if($bank_account_id)
                $builder->where("tb_autopay_bank_account.id",$bank_account_id);
            if(!empty($bank_sub_account) && is_array($bank_sub_account))
                $builder->whereIn('tb_autopay_sms_parsed.sub_account', $bank_sub_account);
        }

        //if($bank_account_id)
         //   $builder->where("tb_autopay_bank_account.id",$bank_account_id);
       
        return $builder->countAllResults();
    }

    public function countTransactionsFiltered($company_id, $bank_account_id = FALSE, $role, $configBankSubAccount, $configPayCode, $hide_amount_out, $user_id = null, $date_range = null, $transaction_type = null, $bank_sub_account = null, $amount = null) {
        $this->_getDatatablesQuery($company_id, $bank_account_id, $role, $configBankSubAccount, $configPayCode ,$hide_amount_out, $user_id, $date_range, $transaction_type, $bank_sub_account, $amount);

        return $this->builder->countAllResults();
    }

    public function getRecentTransactions($company_id, $limit=10, $start_date = FALSE, $amount_in_only=FALSE) {
        $builder = $this->db->table($this->table);
        $builder->select("tb_autopay_sms_parsed.id, tb_autopay_bank_account.account_number, tb_autopay_sms_parsed.transaction_date, tb_autopay_sms_parsed.amount_out,tb_autopay_sms_parsed.amount_in,tb_autopay_sms_parsed.accumulated,tb_autopay_sms_parsed.transaction_content,tb_autopay_sms_parsed.reference_number, tb_autopay_sms_parsed.code,tb_autopay_sms_parsed.webhooks_success,tb_autopay_sms_parsed.webhooks_failed,tb_autopay_bank_account.company_id,tb_autopay_sms_parsed.sub_account,tb_autopay_sms_parsed.datecreated,tb_autopay_sms_parsed.gateway,tb_autopay_bank_account.bank_id");

        $builder->join("tb_autopay_bank_account","tb_autopay_bank_account.id=tb_autopay_sms_parsed.bank_account_id", "left");
        $builder->where([
            'tb_autopay_sms_parsed.parser_status' => 'Success',
            'tb_autopay_bank_account.company_id' => $company_id,
            'tb_autopay_sms_parsed.deleted_at' => NULL,
        ]);

        if($start_date)
            $builder->where("tb_autopay_sms_parsed.transaction_date >=", $start_date);
        if($amount_in_only)
            $builder->where("tb_autopay_sms_parsed.amount_in>",0);
        $builder->orderBy('tb_autopay_sms_parsed.id', 'desc');

        return $builder->get($limit)->getResult();
    }


    function sum_income_by_account_number($company_id,$bank_account_id=FALSE,$type,$start_date=FALSE,$end_date=FALSE) {
        $builder = $this->db->table($this->table);
        $builder->join("tb_autopay_bank_account","tb_autopay_bank_account.id=tb_autopay_sms_parsed.bank_account_id", "left");
        if($start_date === FALSE)
            $start_date = date('Y-m-d 00:00:00');

        if($end_date === FALSE) 
            $end_date = date('Y-m-d 23:59:59');
        
        if($type == "in")
            $builder->select("sum(tb_autopay_sms_parsed.amount_in) as `sum`");
        else if($type == "out")
            $builder->select("sum(tb_autopay_sms_parsed.amount_out) as `sum`");
        else
            return FALSE;

        $builder->where("tb_autopay_bank_account.company_id", $company_id);

        if($bank_account_id)
            $builder->where('tb_autopay_sms_parsed.bank_account_id',$bank_account_id);

        $builder->where('tb_autopay_sms_parsed.transaction_date>=',$start_date);
        $builder->where('tb_autopay_sms_parsed.transaction_date<=',$end_date);
        $builder->where('tb_autopay_sms_parsed.deleted_at', NULL);

        $query = $builder->get();
        return $query->getRow();
    }

    public function get_account_balance($bank_account_id, $datetime=FALSE) {
        if($datetime == FALSE)
            $datetime = date("Y-m-d 00:00:00");

        $builder = $this->db->table($this->table);
        $first_transaction = $builder->where(['bank_account_id' => $bank_account_id, 'transaction_date<=' => $datetime,'accumulated!=' => NULL,'accumulated!='=>'', 'deleted_at' => NULL])->orderBy('transaction_date','DESC')->orderBy('id', 'desc')->limit(1)->get()->getRow();

        if(is_object($first_transaction))
            return $first_transaction->accumulated;
        else
            return FALSE; 
    }

    public function get_account_credit($bank_account_id, $start_date, $end_date) {
    
        $builder = $this->db->table($this->table);
        $result = $builder->select("sum(amount_in) as total")->where(['bank_account_id' => $bank_account_id, 'transaction_date>=' => $start_date,'transaction_date<=' => $end_date,'amount_in>'=>0,'deleted_at' => NULL])->get()->getRow();

        if(is_object($result))
            return $result->total;
        else
            return FALSE; 
    }

    public function get_account_debit($bank_account_id, $start_date, $end_date) {
    
        $builder = $this->db->table($this->table);
        $result = $builder->select("sum(amount_out) as total")->where(['bank_account_id' => $bank_account_id, 'transaction_date>=' => $start_date,'transaction_date<=' => $end_date,'amount_out>'=>0,'deleted_at' => NULL])->get()->getRow();

        
        if(is_object($result))
            return $result->total;
        else
            return FALSE; 
    }


    public function addTransactionInDemo($account_number, $amount_in, $transaction_content) {
         
        $accounts_demo = ['*************','*************'];
        $sub_accounts_demo = ['VCB0011ABC001', 'VCB0011ABC002', 'VCB0011ABC003', 'VCB0011ABC004', 'VCB0011ABC005'];

        $sim = "**********";

        // random reference_number
        $reference_number = rand(100000,999999) . '.' . date("dmy") . '.' . date("his");

        // calculate accumulated
        $transactionsModel = model(TransactionsModel::class);

        $last_transaction  = $transactionsModel->where(['account_number' => $account_number])->orderBy('id','desc')->get()->getRow();

        
        if(!is_object($last_transaction))
            $last_accumulated = 0;
        else
            $last_accumulated = $last_transaction->accumulated;

        $accumulated = $last_accumulated + $amount_in;

        //SD TK ********** +3,342,000VND luc 12-02-2023 20:36:53. SD 26,223,981,662VND. Ref MBVCB.**********.Anh Hien 198HVT.CT tu ************* TRAN THI THUY LINH toi...
        $body = "SD TK " . $account_number . " +" . number_format($amount_in) . 'VND luc ' . date("d-m-Y H:i:s", strtotime("14 seconds ago")) . ". SD " . number_format($accumulated) . "VND. Ref " . $reference_number . "." . $transaction_content;

        $post_data = ["from" => "Vietcombank", "body" => $body, "to" => $sim];

        
        // Start the request
        $curl = curl_init();

        curl_setopt($curl, CURLOPT_RETURNTRANSFER, TRUE);
        curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, 5); 
        curl_setopt($curl, CURLOPT_TIMEOUT, 8); //timeout in seconds
        curl_setopt($curl, CURLOPT_URL, base_url('coreapi/sms/create'));
        curl_setopt($curl, CURLOPT_USERAGENT, "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/108.0.0.0 Safari/537.36");
        curl_setopt($curl, CURLOPT_POST, TRUE);
        curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($post_data));
        curl_setopt($curl, CURLOPT_HTTPHEADER, ['Content-Type:application/json']);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, FALSE);
        
        $response = curl_exec($curl);

        return TRUE;

    }

    public function getExitingSmsTransactionVcb($account_number, $content, $date, $amount, $type) {
        helper(['general']);
        
        // Get reference number from content.
        //  MBVCB.**********.Ma so 104905.CT tu ************* SON THI CAM HONG toi0331000417458 CTY TNHH LUU TRU SO
        // signature in sms: date, amount, content
        // sms string from ibanking

        // Replacing multiple spaces with a single space
        $content = preg_replace('!\s+!', ' ', $content);

        $content_to_sms = "Ref " . $content;
        $content_to_sms = str_replace("{", " ", $content_to_sms);
        $content_to_sms = str_replace("}", " ", $content_to_sms);
        $content_to_sms = str_replace("!", "]", $content_to_sms);
        $content_to_sms = substr($content_to_sms, 0, 50);

        $account_to_sms = "SD TK " . $account_number;

       /* $date_to_sms = date_create_from_format( "Y-m-d", $date);

        var_dump($content_to_sms);
        if(!$date_to_sms)
            return FALSE;

        $date_to_sms = "luc " . date_format($date_to_sms,'d-m-Y'); */

        $date_to_sms = "luc " . date("d-m-Y" , strtotime($date));

        if(!$date_to_sms)
            return FALSE;

        
        if($type == "credit")
            $amount_to_sms = "+". number_format($amount). "VND";
        else if($type == "debit")
            $amount_to_sms = "-". number_format($amount). "VND";

        $db = db_connect();

        $sql =   "select * from tb_autopay_sms where `from`='Vietcombank' and body like '".$account_to_sms."%' and body like '%" . $content_to_sms . "%' and body like '%" . $amount_to_sms . "%' and body like '%" . $date_to_sms . "%'";

        //var_dump($sql);

        $query = $db->query($sql);
        $results =  $query->getResult();

        $smsParserModel = slavable_model(SmsParserModel::class, 'Coreapi');

        if(is_array($results) && count($results) == 1) {

            $trans_details = $smsParserModel->where(['sms_id' => $results[0]->id])->get()->getRow();
            if(is_object($trans_details))
                return $results[0];
            else
                return FALSE;
        }
        else {
            return FALSE;
        }
        
      
        
    }

    // Dùng kiểm tra lặp trước khi insert giao dịch sms. Check trong tb_autopay_sms_parsed với source là "IBanking"
    public function getExitingIbankingTransactionVcb($account_number, $content, $date_time, $amount_in, $amount_out) {

        // find existing transaction

        $smsParserModel = model(SmsParserModel::class);
        $ibankingModel = model(IbankingModel::class);

        $start_date_compare = date("Y-m-d H:i:s", strtotime($date_time . ' -1 day'));
        $end_date_compare = date("Y-m-d H:i:s", strtotime($date_time . ' +1 day'));

        // if(substr($content, -3) == "...")
        //     $content = substr($content,0, -3);
        // check in tb_autopay_sms_parsed
        // $results = $smsParserModel->where(['account_number' => $account_number, 'gateway' => 'Vietcombank', "source" => "IBanking", "amount_in" => $amount_in, "amount_out"=>$amount_out, 'transaction_date>' => $start_date_compare, 'transaction_date<' => $end_date_compare])->like(["transaction_content" => $content])->get()->getResult();

        // check in tb_autopay_ibanking
        // Replacing multiple spaces with a single space
        $content = preg_replace('!\s+!', ' ', $content);

        if(substr($content, -3) == "...")
            $content = substr($content,0, -3);


        if($amount_in > 0) {
            $type = "credit";
            $amount = $amount_in;
        }
        else {
            $amount = $amount_out;
            $type = "debit";

        }

        $where = ['account_number' => $account_number, 'gateway' => 'Vietcombank', "amount" => $amount, 'type' => $type, 'transaction_date>' => $start_date_compare, 'transaction_date<' => $end_date_compare];
 
        $results = $ibankingModel->where($where)->like("transaction_content", $content)->get()->getResult();

        if(is_array($results) && count($results) == 1)  
            return $results[0];      
        else {
            return FALSE;
        }
    }

    public function addTransactionDemo2($bank_account_id, $amount_in, $transaction_content) {

     
     
        // calculate accumulated
        $transactionsModel = model(TransactionsModel::class);
        $bankAccountModel = model(BankAccountModel::class);

        $bank_account_details = $bankAccountModel->select("tb_autopay_bank.brand_name,tb_autopay_bank_account.id, tb_autopay_bank_account.company_id, tb_autopay_bank_account.account_number, tb_autopay_bank_account.bank_id")->join("tb_autopay_bank","tb_autopay_bank_account.bank_id=tb_autopay_bank.id")->where(['tb_autopay_bank_account.id' => $bank_account_id])->get()->getRow();
        if(!is_object($bank_account_details))
            return FALSE;
        $last_transaction  = $transactionsModel->where(['bank_account_id' => $bank_account_details->id])->orderBy('id','desc')->get()->getRow();
        
        if(!is_object($last_transaction))
            $last_accumulated = 0;
        else
            $last_accumulated = $last_transaction->accumulated;

        $accumulated = $last_accumulated + $amount_in;

        $reference_number = "";
        if($bank_account_details->brand_name == "Vietcombank")
            $reference_number = rand(100000,999999) . '.' . date("dmy") . '.' . date("his");

        $smsParserModel = model(SmsParserModel::class);

        $configurationModel = model(ConfigurationModel::class);
        $companyModel = model(CompanyModel::class);

        $code = PayCodeDetector::getCode($transaction_content, $bank_account_details->company_id);

        $parser_result = $smsParserModel->insert([
            'gateway' => $bank_account_details->brand_name,
            'account_number' => $bank_account_details->account_number,
            'amount_in' =>  $amount_in,
            'transaction_content' =>  $transaction_content,
            'reference_number' => $reference_number,
            'code' => $code,
            'transaction_date' => date("Y-m-d H:i:s"),
            'source' => 'SMS',
            'accumulated' => $accumulated,
            'parser_status' => 'Success',
            'bank_account_id' => $bank_account_id
        ]);
  
        // webhooks & telegram queue
        if(is_numeric($parser_result)) {

            $transaction_details = $smsParserModel->find($parser_result);
            if(is_object($transaction_details) && is_numeric($transaction_details->account_number)) {

                // check company and subscription status
                $company_check = $companyModel->select("tb_autopay_company.id")
                    ->join("tb_autopay_company_subscription","tb_autopay_company.id=tb_autopay_company_subscription.company_id")
                    ->join("tb_autopay_bank_account","tb_autopay_bank_account.company_id=tb_autopay_company.id")
                    ->where(['tb_autopay_bank_account.id' => $bank_account_id,'tb_autopay_company.status' => 'Active', 'tb_autopay_company.active' => 1])->get()->getResult();

                if(count($company_check) == 1) {

                    // webhooks
                    $webhooksModel = model(WebhooksModel::class);
                    $webhooksModel->doWebhooks($transaction_details->account_number, $parser_result);
                    
                    // sapo
                    $sapoModel = model(SapoModel::class);
                    $sapoModel->doWebhooks($transaction_details->account_number, $parser_result);

                    // haravan
                    $haravanModel = model(HaravanModel::class);
                    $haravanModel->doWebhooks($transaction_details->account_number, $parser_result);


                    // telegram queue
                    $notificationTelegramModel = model(NotificationTelegramModel::class);
                    $notificationTelegramModel->checkAndAddQueue($parser_result);
                    
                    // lark messenger queue
                    $notificationLarkMessengerModel = model(NotificationLarkMessengerModel::class);
                    $notificationLarkMessengerModel->checkAndAddQueue($parser_result);

                    //counter
                    $counterModel = model(CounterModel::class);
                    $counterModel->transaction($company_check[0]->id,FALSE,$transaction_details->amount_in, $transaction_details->amount_out);

                    $bankAccountModel = model(BankAccountModel::class);
                    //$bankAccountModel->set(['last_transaction' => $transaction_details->transaction_date, 'accumulated' => $transaction_details->accumulated])->where(['account_number' => $transaction_details->account_number])->update();

                    $last_trans = $transactionsModel->where([
                        'account_number' => $transaction_details->account_number, 
                        'accumulated!=' => '',
                        'bank_account_id' => $bank_account_id,
                    ])->orderBy('transaction_date', 'desc')->orderBy('id', 'desc')->first();
                    
                    if(is_object($last_trans))
                        $bankAccountModel->set(['last_transaction' => $last_trans->transaction_date, 'accumulated' => $last_trans->accumulated])->where(['id' => $transaction_details->bank_account_id])->update();
                }
            } else {
                return FALSE;
            }
        }
        return TRUE;
    }

    public function getQueryByBankAccount($companyId, $bankAccountId)
    {
        return $this
            ->select('tb_autopay_sms_parsed.id, tb_autopay_sms_parsed.sms_id, tb_autopay_sms_parsed.source')
            ->join('tb_autopay_bank_account', 'tb_autopay_bank_account.id = tb_autopay_sms_parsed.bank_account_id')
            ->where('tb_autopay_bank_account.company_id', $companyId)
            ->where('tb_autopay_sms_parsed.deleted_at', null)
            ->where('tb_autopay_sms_parsed.bank_account_id', $bankAccountId);
    }


    protected function _handleDateRange($date_range)
    {
        if (empty($date_range)) {
            return;
        }

        $date_range = trim($date_range);
        
        if (strpos($date_range, ' - ') !== false) {
            $dates = explode(' - ', $date_range);
            
            if (count($dates) == 2) {
                $start_date = DateTime::createFromFormat('d-m-Y', trim($dates[0]));
                $end_date = DateTime::createFromFormat('d-m-Y', trim($dates[1]));
                
                if ($start_date && $end_date) {
                    $start_date->setTime(0, 0, 0);
                    $end_date->setTime(23, 59, 59);
                    
                    if ($start_date <= $end_date) {
                        $this->where('tb_autopay_sms_parsed.transaction_date >=', $start_date->format('Y-m-d H:i:s'));
                        $this->where('tb_autopay_sms_parsed.transaction_date <=', $end_date->format('Y-m-d H:i:s'));
                    }
                }
            }
        } else {
            $single_date = DateTime::createFromFormat('d-m-Y', trim($date_range));
            
            if ($single_date) {
                $start_date = clone $single_date;
                $end_date = clone $single_date;
                
                $start_date->setTime(0, 0, 0);
                $end_date->setTime(23, 59, 59);
                
                $this->where('tb_autopay_sms_parsed.transaction_date >=', $start_date->format('Y-m-d H:i:s'));
                $this->where('tb_autopay_sms_parsed.transaction_date <=', $end_date->format('Y-m-d H:i:s'));
            }
        }
    }

    private function _getTransactionsByBankIdQuery($bankId, $account_number, $sub_account_number, $serial_number, $amount, $date_range)
    {
        $request = \Config\Services::request();

        $column_order = array('tb_autopay_sms_parsed.id', 'tb_autopay_bank_account.account_number','tb_autopay_sms_parsed.sub_account', 'tb_autopay_sms_parsed.amount_in', 'tb_autopay_sms_parsed.transaction_date', 'tb_autopay_output_device.serial_number', 'tb_autopay_sms_parsed.transaction_content', null, null);

        $column_search = array('tb_autopay_sms_parsed.id', 'tb_autopay_bank_account.account_number', 'tb_autopay_sms_parsed.sub_account','tb_autopay_sms_parsed.amount_in', "DATE_FORMAT(tb_autopay_sms_parsed.transaction_date, '%H:%i:%s %d-%m-%Y')",'tb_autopay_output_device.serial_number','tb_autopay_sms_parsed.transaction_content');

        $this->builder = $this->db->table($this->table);

        $this->builder->select("tb_autopay_sms_parsed.id, tb_autopay_bank.brand_name, tb_autopay_bank_account.account_number, DATE_FORMAT(tb_autopay_sms_parsed.transaction_date, '%H:%i:%s %d-%m-%Y') as transaction_date, tb_autopay_sms_parsed.amount_in,tb_autopay_sms_parsed.transaction_content, tb_autopay_bank_account.company_id, tb_autopay_sms_parsed.sub_account,tb_autopay_bank.icon_path, tb_autopay_output_device.serial_number, tb_autopay_bank_account.account_holder_name");

        $this->builder->join("tb_autopay_bank","tb_autopay_bank.brand_name=tb_autopay_sms_parsed.gateway");
        $this->builder->join("tb_autopay_bank_account","tb_autopay_bank_account.id=tb_autopay_sms_parsed.bank_account_id");
        $this->builder->join("tb_autopay_output_device","tb_autopay_bank_account.company_id = tb_autopay_output_device.company_id");
        $this->builder->where("tb_autopay_sms_parsed.parser_status","Success");
        $this->builder->where("tb_autopay_bank_account.bank_id", $bankId);

        if($account_number){
            $this->builder->whereIn("tb_autopay_bank_account.id", $account_number);
        }

        if($sub_account_number && is_string($sub_account_number)){
            $sub_account_number_array = array_filter(array_map('trim', explode(',', $sub_account_number)));
            $this->builder->whereIn("tb_autopay_sms_parsed.sub_account", $sub_account_number_array);
        }
        if($serial_number){
            $this->builder->where("tb_autopay_output_device.serial_number", $serial_number);
        }

        if (!empty($amount)) {
            $ranges = explode('-', $amount);
            $min_amount = (int)$ranges[0];
            $max_amount = isset($ranges[1]) ? (int)$ranges[1] : null;
        
            if ($max_amount !== null) {
                $this->builder->where('tb_autopay_sms_parsed.amount_in >=', $min_amount)
                              ->where('tb_autopay_sms_parsed.amount_in <=', $max_amount);
            } else {
                $this->builder->where('tb_autopay_sms_parsed.amount_in >=', $min_amount);
            }
        }        

        if($date_range){
            $this->_handleDateRange($date_range);
        }

        $i = 0;
     
        foreach ($column_search as $item) {
            if($request->getVar('search')['value'] != NULL) {
                $search_term = str_replace(',', '',trim($request->getVar('search')['value']));
                 
                if($i===0) {
                    $this->builder->groupStart();
                    $this->builder->like($item, $search_term);
                } else {
                    $this->builder->orLike($item, $search_term);
                }
 
                if(count($column_search) - 1 == $i)
                    $this->builder->groupEnd();
            }
            $i++;
        }

        // Per column search
        foreach ($column_search as $key => $item) { 
            
            if($item !=NULL && isset($request->getPost('columns')[$key]['search']['value']) && $request->getPost('columns')[$key]['search']['value'] != "") {
                $this->builder->where($item, $request->getPost('columns')[$key]['search']['value']);
            }
        }
      
        if($request->getVar('order') != NULL && isset($column_order[$request->getVar('order')[0]['column']])  && $column_order[$request->getVar('order')[0]['column']] != NULL) {
            
            $this->builder->orderBy($column_order[$request->getVar('order')[0]['column']], $request->getVar('order')['0']['dir']);

        } else if(isset($this->order)) {

            $order = $this->order;
            $this->builder->orderBy(key($order), $order[key($order)]);

        }

        $this->builder->groupBy("tb_autopay_sms_parsed.id");

    }

    public function getTransactionsByBankId($bankId, $account_number, $sub_account_number = NULL, $serial_number = NULL, $amount = NULL, $date_range = NULL)
    {
        $request = \Config\Services::request();
        $this->_getTransactionsByBankIdQuery($bankId, $account_number, $sub_account_number, $serial_number, $amount, $date_range);

        if($request->getVar('length') != NULL && $request->getVar('length') != -1)
            $this->builder->limit($request->getVar('length'), $request->getVar('start'));
        
        $query = $this->builder->get();
        return $query->getResult();
    }

    public function getTransactionsByBankIdCountAll($bankId)
    {
        $builder = $this->db->table($this->table);
        $builder->select('tb_autopay_sms_parsed.id');
        $builder->join("tb_autopay_bank","tb_autopay_bank.brand_name=tb_autopay_sms_parsed.gateway");
        $builder->join("tb_autopay_bank_account","tb_autopay_bank_account.id=tb_autopay_sms_parsed.bank_account_id");
        $builder->join("tb_autopay_output_device","tb_autopay_bank_account.company_id = tb_autopay_output_device.company_id");
        $builder->where("tb_autopay_sms_parsed.parser_status","Success");
        $builder->where("tb_autopay_bank_account.bank_id", $bankId);

        $builder->groupBy("tb_autopay_sms_parsed.id");
        return $builder->countAllResults();
    }

    public function getTransactionsByBankIdCountFiltered($bankId, $account_number, $sub_account_number = NULL, $serial_number = NULL, $amount = NULL, $date_range = NULL)
    {
        $this->_getTransactionsByBankIdQuery($bankId, $account_number, $sub_account_number, $serial_number, $amount, $date_range);
        return $this->builder->countAllResults();
    }

}
