<?php

namespace App\Models;

use CodeIgniter\Model;

class InvoiceModel extends Model
{
    protected $table = 'tb_autopay_invoice';

    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType     = 'object';
    protected $allowedFields = ['status','type','payment_method','company_id','physical_invoice_id','date','duedate','paybefore', 'datepaid','subtotal','credit', 'total', 'tax','tax_rate','vat_invoice_requested_at', 'customer_info_id'];

    protected $createdField  = 'created_at';

    protected $useSoftDeletes = false;


    protected $useTimestamps     = false;

    protected $validationRules    = [
        
    ];
    protected $skipValidation     = false;
    
    protected $order = ['id'=>'desc'];
 
    protected $builder;


    private function _getDatatablesQuery($company_id) {

        $request = \Config\Services::request();
  
        $column_order = array(null,'id','type','status','total','date',null);
        $column_search = array('id','type','status','total','date');
 
        $this->builder = $this->db->table($this->table);

        $this->builder->select("id, type, status, total, date, duedate, paybefore, datepaid");

        $this->builder->where("company_id",$company_id);

     
        $i = 0;
     
        foreach ($column_search as $item) {
            if($request->getVar('search')['value'] != NULL) {
                $search_term = trim($request->getVar('search')['value']);
                 
                if($i===0) {
                    $this->builder->groupStart();
                    $this->builder->like($item, $search_term);
                } else {
                    $this->builder->orLike($item, $search_term);
                }
 
                if(count($column_search) - 1 == $i)
                    $this->builder->groupEnd();
            }
            $i++;
        }

        // Per column search
        foreach ($column_search as $key => $item) { 
            
            if($item !=NULL && isset($request->getPost('columns')[$key]['search']['value']) && $request->getPost('columns')[$key]['search']['value'] != "") {
                $this->builder->where($item, $request->getPost('columns')[$key]['search']['value']);
            }
        }
      
        if($request->getVar('order') != NULL && isset($column_order[$request->getVar('order')[0]['column']])  && $column_order[$request->getVar('order')[0]['column']] != NULL) {
            
            $this->builder->orderBy($column_order[$request->getVar('order')[0]['column']], $request->getVar('order')['0']['dir']);

        } else if(isset($this->order)) {

            $order = $this->order;
            $this->builder->orderBy(key($order), $order[key($order)]);

        }
    }

    public function getDatatables($company_id) {
        $request = \Config\Services::request();
        $this->_getDatatablesQuery($company_id);
        if($request->getVar('length') != NULL && $request->getVar('length') != -1)
            $this->builder->limit($request->getVar('length'), $request->getVar('start'));
            
        $query = $this->builder->get();

        return $query->getResult();
    }

    public function countAll($company_id) {
     
        $builder = $this->db->table($this->table);

        if($company_id)
            $builder->where("company_id",$company_id);
 
       
        return $builder->countAllResults();
    }

    public function countFiltered($company_id) {
        $this->_getDatatablesQuery($company_id);
        if($company_id)
            $this->builder->where("company_id",$company_id);
         
        return $this->builder->countAllResults();
    }

}