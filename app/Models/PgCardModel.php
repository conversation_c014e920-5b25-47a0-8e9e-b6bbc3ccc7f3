<?php

namespace App\Models;

use CodeIgniter\Model;

class PgCardModel extends Model
{
    protected $DBGroup          = 'default';
    protected $table            = 'tb_autopay_pg_card';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $insertID         = 0;
    protected $returnType       = 'object';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = ['hash_id', 'company_id', 'pg_merchant_id', 'pg_profile_id', 'pg_order_id', 'pg_customer_id', 'payment_token', 'status', 'card_number', 'card_holder_name', 'card_expiry', 'card_funding_method', 'active', 'card_brand', 'meta', 'last_used_at', 'created_at', 'updated_at'];
    
    protected $beforeInsert = ['hashId'];
    
    protected function hashId(array $data)
    {
        $data['data']['hash_id'] = hash('md5', sprintf('%s|%s|%s|%s', $data['data']['company_id'], $data['data']['pg_merchant_id'], $data['data']['payment_token'], strtotime('now')));
        
        return $data;
    }
}
