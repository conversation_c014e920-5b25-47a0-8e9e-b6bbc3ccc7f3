<?php

namespace App\Models;

use CodeIgniter\Model;

class CybersourceProfileKeyModel extends Model
{
    protected $DBGroup          = 'default';
    protected $table            = 'tb_autopay_cybersource_profile_key';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $insertID         = 0;
    protected $returnType       = 'object';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = ['profile_id', 'name', 'access_key', 'secret_key', 'signature_method', 'date_created', 'date_expires'];
}
