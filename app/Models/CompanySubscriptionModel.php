<?php

namespace App\Models;

use CodeIgniter\Model;

class CompanySubscriptionModel extends Model
{
    protected $table = 'tb_autopay_company_subscription';

    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType     = 'object';
    protected $allowedFields = ['company_id','order_id','plan_id', 'begin_date','end_date','first_payment', 'recurring_payment','billing_cycle','auto_renew', 'is_trial', 'status', 'monthly_transaction_limit','bank_account_limit','telegram_intergration_limit','webhook_intergration_limit','monthly_telegram_send_limit','monthly_webhooks_send_limit','notes','shop_limit', 'allow_exceed_limit', 'pg_agreement_id'];

    protected $createdField  = 'created_at';

    protected $useSoftDeletes = false;


    protected $useTimestamps     = false;

    protected $validationRules    = [
        
    ];
    protected $skipValidation     = false;
    
    protected $order = ['id'=>'desc'];
 
    protected $builder;

    protected $afterUpdate = ['afterUpdateBankBonus'];

    protected function afterUpdateBankBonus(array $data)
    {
        if (isset($data['id']) && $data['id']) {
            $companySubscriptionBuilder = $this->db->table('tb_autopay_company_subscription');
            $companySubscriptionBuilderInfo = $companySubscriptionBuilder->select(['id', 'company_id', 'monthly_transaction_limit', 'plan_id'])
                ->where('id', $data['id'])->get()->getRow();

            if (!$companySubscriptionBuilderInfo) {
                return $data;
            }

            $builder = $this->db->table('tb_autopay_bank_bonus');
            $totalBonusForCompany = $builder->selectSum('bank_bonus')
                ->where(['company_id' => $companySubscriptionBuilderInfo->company_id])
                ->get()
                ->getRow();
            $totalBonusForCompany = $totalBonusForCompany ? $totalBonusForCompany->bank_bonus : 0;

            $configBankBonus = config(\Config\BankBonus::class);

            if (!$configBankBonus->maxBankBonus) {
                return $data;
            }

            $bankBonusLimit = $configBankBonus->maxBankBonus;

            if ($totalBonusForCompany > $bankBonusLimit) {
                $totalBonusForCompany = $bankBonusLimit;
            }

            $newBonusTransactions = $companySubscriptionBuilderInfo->monthly_transaction_limit + $totalBonusForCompany;

            $this->where(['id' => $companySubscriptionBuilderInfo->id])
                ->set(['monthly_transaction_limit' => $newBonusTransactions])
                ->update();
        }

        return $data;
    }

}