<?php

namespace App\Models;

use CodeIgniter\Model;

class CybersourceProfileModel extends Model
{
    protected $DBGroup          = 'default';
    protected $table            = 'tb_autopay_cybersource_profile';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $insertID         = 0;
    protected $returnType       = 'object';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = ['company_id', 'profile_id', 'name', 'integration_method', 'bank_account_id', 'active'];
    
    public function keys(int $id): Model
    {
        $model = model(CybersourceProfileKeyModel::class);
        
        return $model->where('profile_id', $id);
    }
    
    public function activeKeys(int $id): Model
    {
        return $this->keys($id)->where('date_expires >', date('Y-m-d H:i:s'));
    }
}
