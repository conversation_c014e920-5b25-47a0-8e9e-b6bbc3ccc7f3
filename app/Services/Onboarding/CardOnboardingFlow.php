<?php

namespace App\Services\Onboarding;

class CardOnboardingFlow implements OnboardingFlowInterface
{
    public function getFeatureKey(): string
    {
        return 'card';
    }

    public function getAllowedSteps(): array
    {
        return [1, 2, 3, 4, 5];
    }

    public function getViewForStep(int $stepNumber): string
    {
        switch ($stepNumber) {
            case 1:
                return 'onboarding/card/step1_legal_info';
            case 2:
                return 'onboarding/card/step2_legal_representative';
            case 3:
                return 'onboarding/card/step3_bank_account';
            case 4:
                return 'onboarding/card/step4_upload_documents';
            case 5:
                return 'onboarding/complete';
            default:
                return 'onboarding/complete';
        }
    }

    public function getPageTitle(int $stepNumber): string
    {
        $prefix = '[Thanh toán thẻ] ';

        $titles = [
            1 => $prefix . 'Thông tin pháp lý',
            2 => $prefix . 'Người đại diện',
            3 => $prefix . 'Tài khoản ngân hàng',
            4 => $prefix . 'Upload hồ sơ',
        ];

        return $titles[$stepNumber] ?? ($prefix . 'Hoàn thành');
    }
}
