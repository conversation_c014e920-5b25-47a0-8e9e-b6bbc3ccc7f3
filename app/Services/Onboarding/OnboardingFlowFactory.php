<?php

namespace App\Services\Onboarding;

class OnboardingFlowFactory
{
    public static function make(?string $feature): OnboardingFlowInterface
    {
        switch ($feature) {
            case 'napas_qr':
                return new NapasQrOnboardingFlow();
            case 'card':
                return new CardOnboardingFlow();
            default:
                return new CardOnboardingFlow();
        }
    }
}


