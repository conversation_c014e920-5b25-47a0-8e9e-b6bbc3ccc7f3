<!doctype html>
<html lang="vi">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="color-scheme" content="light dark">
    <meta name="format-detection" content="telephone=no">
    <meta name="csrf-token" content="<?= csrf_hash() ?>">

    <link rel="stylesheet" href="<?= base_url('assets/font/bootstrap-icons.css') ?>">
    <link href="<?= base_url('dist/css/light.css') ?>" rel="stylesheet">
    <link rel="stylesheet" href="<?= base_url('assets/notyf/notyf.min.css') ?>">
    <link rel="shortcut icon" href="<?= base_url('assets/images/favicon.png') ?>" type="image/x-icon" />
    <link href="https://fonts.googleapis.com/css2?family=Be+Vietnam+Pro:wght@300;400;500;600&display=swap" rel="stylesheet">

    <title><?= isset($page_title) ? esc($page_title) . ' - ' : '' ?>SePay Onboarding</title>

    <style>
        html,
        body {
            height: 100%;
            font-family: 'Be Vietnam Pro', sans-serif;
        }

        .onboarding-wrapper {
            min-height: 100vh;
            display: flex;
            background-color: #f8f9fa;
        }

        .sidebar {
            width: 320px;
            background: white;
            border-right: 1px solid #e9ecef;
            padding: 1.5rem 0;
            position: relative;
        }

        .sidebar-logo {
            padding: 0 1.5rem 1.5rem;
            border-bottom: 1px solid #e9ecef;
            margin-bottom: 1.5rem;
            text-align: center;
        }

        .sidebar-logo img {
            height: 32px;
        }

        .step-indicator {
            padding: 0 1.5rem;
        }

        .step-item {
            display: flex;
            align-items: start;
            padding: 1rem 0;
            position: relative;
        }

        .step-item:not(:last-child)::after {
            content: '';
            position: absolute;
            left: 15px;
            top: 48px;
            width: 2px;
            height: 4rem;
            background: #e9ecef;
        }

        .step-item.active::after {
            background: var(--bs-primary);
        }

        .step-number {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 14px;
            margin-right: 1rem;
            background: #e9ecef;
            color: #6c757d;
            border: 2px solid transparent;
            transition: all 0.2s ease;
        }

        .step-item.active .step-number {
            background: var(--bs-primary);
            color: white;
            border-color: var(--bs-primary);
            transform: scale(1.1);
        }

        .step-content {
            flex: 1;
        }

        .step-title {
            font-weight: 600;
            font-size: 14px;
            color: #212529;
            margin: 0;
        }

        .step-item.active .step-title {
            color: var(--bs-primary);
            font-weight: 600;
        }

        .step-title a {
            color: inherit;
            transition: color 0.2s ease;
        }

        .step-title a:hover {
            color: var(--bs-primary);
        }

        .step-description {
            font-size: 12px;
            color: #6c757d;
            margin: 0;
            margin-top: 2px;
        }

        .main-content {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem;
        }

        .content-container {
            width: 100%;
            max-width: 600px;
        }

        @media (max-width: 768px) {
            .onboarding-wrapper {
                flex-direction: column;
            }

            .sidebar {
                display: none;
            }

            .step-indicator {
                display: flex;
                overflow-x: auto;
                padding: 0 1rem;
            }

            .step-item {
                flex-shrink: 0;
                padding: 0.5rem 1rem;
            }

            .step-item::after {
                display: none;
            }

            .main-content {
                width: 100%;
                flex: 1;
                display: flex;
                flex-direction: column;
                align-items: start;
                justify-content: flex-start;
                padding: 1rem;
            }

            .mobile-logo {
                margin-bottom: 2rem;
                margin-top: 1rem;
            }

            .content-container {
                margin: 0;
                padding: 0;
            }

            .action-buttons {
                position: fixed;
                bottom: 0;
                left: 0;
                right: 0;
                z-index: 1030;
                background: white;
                padding: 1rem;
            }

            .action-buttons .btn {
                min-height: 36px;
            }

            .mobile-progress {
                position: fixed;
                bottom: 67px;
                left: 0;
                right: 0;
                z-index: 1030;
            }

            .action-buttons button[type="submit"] {
                width: 100%;
            }

            .action-buttons .btn .btn-text {
                display: none;
            }

            .action-buttons .btn .me-2 {
                margin-right: 0 !important;
            }

            .main-content .content-container {
                padding-bottom: 5rem;
            }
        }
    </style>
</head>

<body>
    <div class="onboarding-wrapper">
        <div class="sidebar">
            <div class="sidebar-logo">
                <a href="<?= base_url('/') ?>">
                    <img src="<?= base_url('assets/images/logo/sepay-blue-359x116.png') ?>" alt="SePay" style="height: 40px;">
                </a>
            </div>

            <div class="step-indicator">
                <div class="step-item <?= isset($current_step_number) && $current_step_number >= 1 ? ($current_step_number == 1 ? 'active' : 'completed') : '' ?>">
                    <div class="step-number">
                        <?php if (isset($record_current_step) && $record_current_step > 1): ?>
                            <i class="bi bi-check fs-3"></i>
                        <?php else: ?>
                            1
                        <?php endif; ?>
                    </div>
                    <div class="step-content">
                        <?php if (isset($record_current_step) && $record_current_step >= 1): ?>
                            <a href="<?= base_url('onboarding?feature=' . ($onboarding_feature ?? 'card') . '&step=1') ?>" class="step-title text-decoration-none">Thông tin công ty</a>
                        <?php else: ?>
                            <div class="step-title">Thông tin công ty</div>
                        <?php endif; ?>
                        <div class="step-description">Thông tin pháp lý doanh nghiệp</div>
                    </div>
                </div>

                <div class="step-item <?= isset($current_step_number) && $current_step_number >= 2 ? ($current_step_number == 2 ? 'active' : 'completed') : '' ?>">
                    <div class="step-number">
                        <?php if (isset($record_current_step) && $record_current_step > 2): ?>
                            <i class="bi bi-check fs-3"></i>
                        <?php else: ?>
                            2
                        <?php endif; ?>
                    </div>
                    <div class="step-content">
                        <?php if (isset($record_current_step) && $record_current_step >= 2): ?>
                            <a href="<?= base_url('onboarding?feature=' . ($onboarding_feature ?? 'card') . '&step=2') ?>" class="step-title text-decoration-none">Người đại diện</a>
                        <?php else: ?>
                            <div class="step-title">Người đại diện</div>
                        <?php endif; ?>
                        <div class="step-description">Thông tin người đại diện pháp luật</div>
                    </div>
                </div>

                <div class="step-item <?= isset($current_step_number) && $current_step_number >= 3 ? ($current_step_number == 3 ? 'active' : 'completed') : '' ?>">
                    <div class="step-number">
                        <?php if (isset($record_current_step) && $record_current_step > 3): ?>
                            <i class="bi bi-check fs-3"></i>
                        <?php else: ?>
                            3
                        <?php endif; ?>
                    </div>
                    <div class="step-content">
                        <?php if (isset($record_current_step) && $record_current_step >= 3): ?>
                            <a href="<?= base_url('onboarding?feature=' . ($onboarding_feature ?? 'card') . '&step=3') ?>" class="step-title text-decoration-none">Tài khoản ngân hàng</a>
                        <?php else: ?>
                            <div class="step-title">Tài khoản ngân hàng</div>
                        <?php endif; ?>
                        <div class="step-description">Thông tin tài khoản nhận tiền</div>
                    </div>
                </div>

                <div class="step-item <?= isset($current_step_number) && $current_step_number >= 4 ? ($current_step_number == 4 ? 'active' : 'completed') : '' ?>">
                    <div class="step-number">
                        <?php if (isset($record_current_step) && $record_current_step > 4): ?>
                            <i class="bi bi-check fs-3"></i>
                        <?php else: ?>
                            4
                        <?php endif; ?>
                    </div>
                    <div class="step-content">
                        <?php if (isset($record_current_step) && $record_current_step >= 4): ?>
                            <a href="<?= base_url('onboarding?feature=' . ($onboarding_feature ?? 'card') . '&step=4') ?>" class="step-title text-decoration-none">Tài liệu KYC</a>
                        <?php else: ?>
                            <div class="step-title">Tài liệu KYC</div>
                        <?php endif; ?>
                        <div class="step-description">Upload hồ sơ pháp lý</div>
                    </div>
                </div>
            </div>
        </div>

        <main class="main-content">
            <div class="mobile-logo d-block d-md-none">
                <img src="<?= base_url('assets/images/logo/sepay-blue-359x116.png') ?>" alt="SePay" style="height: 36px;">
            </div>

            <div class="content-container">
                <div class="mb-3">
                    <a href="<?= base_url('paymentmethods') ?>">
                        <i class="bi bi-arrow-left me-2"></i>Quay lại Cổng thanh toán
                    </a>
                </div>
                <?= $this->renderSection('content') ?>
            </div>

            <div class="mobile-progress d-block d-md-none">
                <div class="progress" style="height: 4px; border-radius: 0;">
                    <div class="progress-bar bg-primary" role="progressbar"
                        style="width: <?= isset($current_step_number) ? ($current_step_number * 20) : 20 ?>%"
                        aria-valuenow="<?= isset($current_step_number) ? ($current_step_number * 20) : 20 ?>"
                        aria-valuemin="0"
                        aria-valuemax="100">
                    </div>
                </div>
            </div>
        </main>
    </div>
    <script src="<?= base_url('assets/js/bootstrap.bundle.min.js') ?>"></script>
    <script src="<?= base_url('assets/js/jquery-3.5.1.js') ?>"></script>
    <script src="<?= base_url('assets/notyf/notyf.min.js') ?>"></script>

    <?= $this->renderSection('scripts') ?>
</body>

</html>