<?= $this->extend('layouts/onboarding') ?>

<?= $this->section('content') ?>

<link rel="stylesheet" href="<?= base_url('assets/filepond/filepond.min.css') ?>">
<style>
    .filepond label {
        font-size: 0.875rem;
    }
</style>
<div class="mb-4">
    <h2 class="mb-2">Upload hồ sơ KYC</h2>
    <p class="text-muted">Tải lên các giấy tờ pháp lý để hoàn tất thủ tục</p>
</div>

<form id="upload-documents-form">
    <div class="alert alert-warning mb-4 bg-opacity-10 bg-warning">
        <div class="alert-message">
            <i class="bi bi-exclamation-triangle me-2"></i>
            <strong>Lưu ý quan trọng:</strong>
            <ul class="mb-0 mt-2">
                <li>Ảnh chụp rõ n<PERSON>, đầy đủ 4 góc</li>
                <li>Kh<PERSON>ng bị mờ, không bị che khuất</li>
                <li>File không quá 5MB mỗi file</li>
                <li>Đ<PERSON>nh dạng: PDF, JPG, PNG</li>
            </ul>
        </div>
    </div>

    <div class="border-bottom mb-4">
        <div class="mb-4">
            <h5>Giấy tờ doanh nghiệp</h5>
        </div>

        <div class="mb-4">
            <label for="business_registration" class="form-label mb-0">Giấy chứng nhận đăng ký doanh nghiệp <span class="text-danger">*</span></label>
            <div class="form-text mb-2">Công chứng trong vòng 6 tháng</div>
            <input type="file" class="filepond" id="business_registration" name="business_registration" data-field="business_registration">
        </div>

        <div class="mb-4">
            <label for="company_charter" class="form-label">Điều lệ của tổ chức <span class="text-danger">*</span> <i class="bi bi-info-circle text-muted ms-1" data-bs-toggle="tooltip" data-bs-placement="top" title="Dấu treo công ty"></i></label>
            <input type="file" class="filepond" id="company_charter" name="company_charter" data-field="company_charter">
        </div>
    </div>

    <div class="border-bottom mb-4">
        <div class="mb-4">
            <h5>Giấy tờ người đại diện</h5>
        </div>

        <div class="mb-4">
            <label for="legal_rep_id" class="form-label mb-0">CCCD Người đại diện pháp luật <span class="text-danger">*</span></label>
            <div class="form-text mb-2">Công chứng trong vòng 6 tháng</div>
            <input type="file" class="filepond" id="legal_rep_id" name="legal_rep_id" data-field="legal_rep_id">
        </div>

        <div class="mb-4">
            <label for="director_appointment" class="form-label">QĐ bổ nhiệm/Hợp đồng thuê Giám đốc/Tổng Giám đốc <i class="bi bi-info-circle text-muted ms-1" data-bs-toggle="tooltip" data-bs-placement="top" title="Có dấu treo công ty, còn hiệu lực"></i></label>
            <input type="file" class="filepond" id="director_appointment" name="director_appointment" data-field="director_appointment">
        </div>
    </div>

    <div class="border-bottom mb-4">
        <div class="mb-4">
            <h5>Kế toán trưởng/ Giám đốc tài chính</h5>
        </div>
        <div class="mb-4">
            <label for="cfo_appointment" class="form-label">QĐ bổ nhiệm/HĐ thuê Kế toán trưởng/Giám đốc tài chính <span class="text-danger">*</span> <i class="bi bi-info-circle text-muted ms-1" data-bs-toggle="tooltip" data-bs-placement="top" title="Dấu treo công ty"></i></label>
            <input type="file" class="filepond" id="cfo_appointment" name="cfo_appointment" data-field="cfo_appointment">
        </div>

        <div class="mb-4">
            <label for="cfo_id" class="form-label mb-0">CCCD Kế toán trưởng/Giám đốc tài chính <span class="text-danger">*</span></label>
            <div class="form-text mb-2">Công chứng trong vòng 6 tháng</div>
            <input type="file" class="filepond" id="cfo_id" name="cfo_id" data-field="cfo_id">
        </div>
    </div>

    <div>
        <div class="mb-4">
            <h5>Chủ sở hữu hưởng lợi</h5>
        </div>
        <div class="mb-4">
            <label for="beneficial_owner_id" class="form-label mb-0">CCCD chủ sở hữu hưởng lợi chiếm trên 25% <span class="text-danger">*</span></label>
            <div class="form-text mb-2">Công chứng trong vòng 6 tháng</div>
            <input type="file" class="filepond" id="beneficial_owner_id" name="beneficial_owner_id" data-field="beneficial_owner_id">
        </div>
    </div>

    <div class="mb-4">
        <div class="form-check">
            <input class="form-check-input" type="checkbox" id="confirm_documents" name="confirm_documents" <?= ($current_step_data['confirm_documents'] ?? '') === '1' ? 'checked' : '' ?> required>
            <label class="form-check-label" for="confirm_documents">
                Tôi xác nhận các giấy tờ đã tải lên là chính xác, đầy đủ và hợp lệ
            </label>
        </div>
    </div>

    <div class="action-buttons d-flex justify-content-start mt-4 gap-3">
        <?php if (isset($back_url) && $back_url): ?>
            <a href="<?= esc($back_url) ?>" class="d-flex align-items-center justify-content-center btn btn-outline-secondary">
                <i class="bi bi-arrow-left me-2"></i><span class="btn-text">Quay lại</span>
            </a>
        <?php else: ?>
            <a href="<?= base_url('paymentmethods') ?>" class="d-flex align-items-center justify-content-center btn btn-outline-secondary">
                <i class="bi bi-arrow-left me-2"></i><span class="btn-text">Quay lại</span>
            </a>
        <?php endif; ?>
        <button type="submit" class="btn btn-primary">
            Hoàn tất đăng ký
        </button>
    </div>
</form>
<?= $this->endSection() ?>


<?= $this->section('scripts') ?>
<script src="<?= base_url('assets/filepond/filepond.min.js') ?>"></script>
<script src="https://unpkg.com/filepond-plugin-file-validate-type/dist/filepond-plugin-file-validate-type.js"></script>
<script src="https://cdn.jsdelivr.net/npm/jquery-validation@1.19.5/dist/jquery.validate.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/jquery-validation@1.19.5/dist/additional-methods.min.js"></script>
<script>
    $(document).ready(function() {
        const filepondInputs = {};

        FilePond.registerPlugin(FilePondPluginFileValidateType);

        FilePond.setOptions({
            labelIdle: 'Kéo thả file hoặc <span class="filepond--label-action">Chọn file</span>',
            labelFileWaitingForSize: 'Đang tính toán kích thước',
            labelFileSizeNotAvailable: 'Kích thước không khả dụng',
            labelFileLoading: 'Đang tải',
            labelFileLoadError: 'Lỗi khi tải',
            labelFileProcessing: 'Đang xử lý',
            labelFileProcessingComplete: 'Xử lý hoàn tất',
            labelFileProcessingAborted: 'Đã hủy xử lý',
            labelFileProcessingError: 'Lỗi khi xử lý',
            labelFileProcessingRevertError: 'Lỗi khi hoàn tác',
            labelFileRemoveError: 'Lỗi khi xóa',
            labelTapToCancel: 'Nhấn để hủy',
            labelTapToRetry: 'Nhấn để thử lại',
            labelTapToUndo: 'Nhấn để hoàn tác',
            labelButtonRemoveItem: 'Xóa',
            labelButtonAbortItemLoad: 'Hủy',
            labelButtonRetryItemLoad: 'Thử lại',
            labelButtonAbortItemProcessing: 'Hủy',
            labelButtonUndoItemProcessing: 'Hoàn tác',
            labelButtonRetryItemProcessing: 'Thử lại',
            labelButtonProcessItem: 'Tải lên',
            maxFileSize: '5MB',
            acceptedFileTypes: ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf'],
            allowMultiple: false,
            server: null,
            credits: false,
        });

        $('.filepond').each(function() {
            const pond = FilePond.create(this);
            const fieldName = $(this).attr('name');
            filepondInputs[fieldName] = pond;
        });

        $.validator.addMethod('fileRequired', function(value, element) {
            const fieldName = $(element).attr('name');
            const pond = filepondInputs[fieldName];
            return pond && pond.getFiles().length > 0;
        }, 'Vui lòng tải lên file này.');

        const validator = $('#upload-documents-form').validate({
            rules: {
                business_registration: {
                    fileRequired: true
                },
                company_charter: {
                    fileRequired: true
                },
                legal_rep_id: {
                    fileRequired: true
                },

                cfo_appointment: {
                    fileRequired: true
                },
                cfo_id: {
                    fileRequired: true
                },
                beneficial_owner_id: {
                    fileRequired: true
                },
                confirm_documents: {
                    required: true
                }
            },
            messages: {
                business_registration: 'Vui lòng tải lên Giấy chứng nhận đăng ký doanh nghiệp.',
                company_charter: 'Vui lòng tải lên Điều lệ của tổ chức.',
                legal_rep_id: 'Vui lòng tải lên CCCD Người đại diện pháp luật.',

                cfo_appointment: 'Vui lòng tải lên QĐ bổ nhiệm/HĐ thuê Kế toán trưởng.',
                cfo_id: 'Vui lòng tải lên CCCD Kế toán trưởng/Giám đốc tài chính.',
                beneficial_owner_id: 'Vui lòng tải lên CCCD chủ sở hữu hưởng lợi.',
                confirm_documents: 'Vui lòng xác nhận tính chính xác của giấy tờ.'
            },
            errorClass: 'text-danger',
            validClass: 'is-valid',
            errorElement: 'div',
            errorPlacement: function(error, element) {
                error.addClass('invalid-feedback');

                if (element.hasClass('filepond--browser')) {
                    error.insertAfter(element.closest('.mb-4').find('.filepond--root'));
                } else if (element.attr('type') === 'checkbox') {
                    error.insertAfter(element.closest('.form-check'));
                } else {
                    error.insertAfter(element);
                }
            },
            highlight: function(element, errorClass, validClass) {
                if ($(element).hasClass('filepond')) {
                    $(element).closest('.mb-3').find('.filepond--panel-root').css('border-color', '#dc3545');
                }
            },
            unhighlight: function(element, errorClass, validClass) {
                if ($(element).hasClass('filepond')) {
                    $(element).closest('.mb-3').find('.filepond--panel-root').css('border-color', '#198754');
                }
            },
            submitHandler: function(form) {
                const formData = {};
                formData['confirm_documents'] = $('#confirm_documents').is(':checked') ? '1' : '0';

                Object.keys(filepondInputs).forEach(fieldName => {
                    const pond = filepondInputs[fieldName];
                    if (pond.getFiles().length > 0) {
                        formData[fieldName] = pond.getFiles()[0].file.name;
                    }
                });

                $(form).find('button[type="submit"]').prop('disabled', true).html('<i class="bi bi-hourglass-split me-2"></i>Đang xử lý...');
                
                completeOnboarding(formData, filepondInputs);
            }
        });

        Object.keys(filepondInputs).forEach(fieldName => {
            const pond = filepondInputs[fieldName];
            pond.on('addfile', () => {
                $(`input[name="${fieldName}"]`).valid();
            });
            pond.on('removefile', () => {
                $(`input[name="${fieldName}"]`).valid();
            });
        });

        const tooltipTriggerList = document.querySelectorAll('[data-bs-toggle="tooltip"]');
        const tooltipList = [...tooltipTriggerList].map(tooltipTriggerEl => new bootstrap.Tooltip(tooltipTriggerEl));

    });
</script>

<?= view('onboarding/shared_scripts') ?>

<?= $this->endSection() ?>