<?= $this->extend('layouts/onboarding') ?>

<?= $this->section('content') ?>
<div class="mb-4">
    <h2 class="mb-2">Thông tin pháp lý công ty</h2>
    <p class="text-muted">Vui lòng cung cấp thông tin pháp lý chính xác của doanh nghiệp</p>
</div>

<form id="legal-info-form">
    <div class="d-flex flex-column gap-2 mb-3">
        <label for="company_name" class="form-label mb-0">Tên công ty <span class="text-danger">*</span></label>
        <div class="form-text mt-0">Tên công ty như trong giấy phép kinh doanh</div>
        <input type="text" class="form-control" id="company_name" name="company_name" value="<?= esc($current_step_data['company_name'] ?? '') ?>" required>
    </div>

    <div class="row g-3 mb-3">
        <div class="col-md-6">
            <label for="tax_code" class="form-label">Mã số thuế <span class="text-danger">*</span></label>
            <input type="text" class="form-control" id="tax_code" name="tax_code" pattern="[0-9-]+" value="<?= esc($current_step_data['tax_code'] ?? '') ?>" required>
        </div>

        <div class="col-md-6">
            <label for="rep_phone" class="form-label">Số điện thoại <span class="text-danger">*</span></label>
            <input type="tel" class="form-control" id="rep_phone" name="rep_phone" pattern="[0-9]+" value="<?= esc($current_step_data['rep_phone'] ?? '') ?>" required>
        </div>
    </div>

    <div class="d-flex flex-column gap-2 mb-3">
        <label for="address" class="form-label mb-0">Địa chỉ trụ sở <span class="text-danger">*</span></label>
        <div class="form-text mt-0">Địa chỉ đầy đủ bao gồm số nhà, đường, phường/xã, quận/huyện, tỉnh/thành phố</div>
        <textarea class="form-control" id="address" name="address" rows="2" required><?= esc($current_step_data['address'] ?? '') ?></textarea>
    </div>

    <div class="action-buttons d-flex justify-content-start mt-4 gap-3">
        <?php if (isset($back_url) && $back_url): ?>
            <a href="<?= esc($back_url) ?>" class="d-flex align-items-center justify-content-center btn btn-outline-secondary">
                <i class="bi bi-arrow-left me-2"></i><span class="btn-text">Quay lại</span>
            </a>
        <?php else: ?>
            <a href="<?= base_url('paymentmethods') ?>" class="d-flex align-items-center justify-content-center btn btn-outline-secondary">
                <i class="bi bi-arrow-left me-2"></i><span class="btn-text">Quay lại</span>
            </a>
        <?php endif; ?>
        <button type="submit" class="d-flex align-items-center justify-content-center btn btn-primary">
            Tiếp tục <i class="bi bi-arrow-right ms-2"></i>
        </button>
    </div>
</form>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script src="https://cdn.jsdelivr.net/npm/jquery-validation@1.19.5/dist/jquery.validate.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/jquery-validation@1.19.5/dist/additional-methods.min.js"></script>
<script>
    $(() => {
        $.validator.addMethod('taxCode', function(value, element) {
            return this.optional(element) || /^[0-9-]+$/.test(value);
        }, 'Mã số thuế chỉ được chứa số và dấu gạch ngang.');

        $.validator.addMethod('phoneNumber', function(value, element) {
            return this.optional(element) || /^[0-9]+$/.test(value);
        }, 'Số điện thoại chỉ được chứa các chữ số.');

        const validator = $('#legal-info-form').validate({
            rules: {
                company_name: {
                    required: true,
                    minlength: 3,
                    maxlength: 255
                },
                tax_code: {
                    required: true,
                    taxCode: true,
                    minlength: 10,
                    maxlength: 15
                },
                rep_phone: {
                    required: true,
                    phoneNumber: true,
                    minlength: 10,
                    maxlength: 11
                },
                address: {
                    required: true,
                    minlength: 10,
                    maxlength: 500
                }
            },
            messages: {
                company_name: {
                    required: 'Vui lòng nhập tên công ty.',
                    minlength: 'Tên công ty phải có ít nhất 3 ký tự.',
                    maxlength: 'Tên công ty không được vượt quá 255 ký tự.'
                },
                tax_code: {
                    required: 'Vui lòng nhập mã số thuế.',
                    minlength: 'Mã số thuế phải có ít nhất 10 ký tự.',
                    maxlength: 'Mã số thuế không được vượt quá 15 ký tự.'
                },
                rep_phone: {
                    required: 'Vui lòng nhập số điện thoại.',
                    minlength: 'Số điện thoại phải có ít nhất 10 chữ số.',
                    maxlength: 'Số điện thoại không được vượt quá 11 chữ số.'
                },
                address: {
                    required: 'Vui lòng nhập địa chỉ trụ sở.',
                    minlength: 'Địa chỉ phải có ít nhất 10 ký tự.',
                    maxlength: 'Địa chỉ không được vượt quá 500 ký tự.'
                }
            },
            errorClass: 'text-danger',
            validClass: 'is-valid',
            errorElement: 'div',
            errorPlacement: function(error, element) {
                error.addClass('invalid-feedback');
                error.insertAfter(element);
            },
            highlight: function(element, errorClass, validClass) {
                $(element).addClass('is-invalid').removeClass(validClass);
            },
            unhighlight: function(element, errorClass, validClass) {
                $(element).removeClass('is-invalid').addClass(validClass);
            },
            submitHandler: function(form) {
                saveStepAndRedirect(1, getFormData('legal-info-form'));
            }
        });

        $('#tax_code').on('input', function() {
            this.value = this.value.replace(/[^0-9-]/g, '');
        });

        $('#rep_phone').on('input', function() {
            this.value = this.value.replace(/[^0-9]/g, '');
        });
    })
</script>

<?= view('onboarding/shared_scripts') ?>
<?= $this->endSection() ?>