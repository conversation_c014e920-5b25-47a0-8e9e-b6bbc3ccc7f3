<?= $this->extend('layouts/onboarding') ?>

<?= $this->section('content') ?>
<div class="mb-4">
    <h2 class="mb-2">Ngư<PERSON>i đại diện pháp luật</h2>
    <p class="text-muted">Thông tin người có quyền đại diện cho doanh nghiệp</p>
</div>

<form id="representative-form">
    <div class="mb-3">
        <label for="rep_fullname" class="form-label">Họ và tên <span class="text-danger">*</span></label>
        <input type="text" class="form-control" id="rep_fullname" name="rep_fullname" value="<?= esc($current_step_data['rep_fullname'] ?? '') ?>" required>
    </div>
    <div class="row g-3 mb-3">
        <div class="col-md-6">
            <label for="rep_id_number" class="form-label">Số CCCD/CMND <span class="text-danger">*</span></label>
            <input type="text" class="form-control" id="rep_id_number" name="rep_id_number" pattern="[0-9]+" value="<?= esc($current_step_data['rep_id_number'] ?? '') ?>" required>
        </div>
        <div class="col-md-6">
            <label for="rep_position" class="form-label">Chức vụ <span class="text-danger">*</span></label>
            <select class="form-select" id="rep_position" name="rep_position" required>
                <option value="">Chọn chức vụ</option>
                <option value="ceo" <?= ($current_step_data['rep_position'] ?? '') === 'ceo' ? 'selected' : '' ?>>Giám đốc</option>
                <option value="chairman" <?= ($current_step_data['rep_position'] ?? '') === 'chairman' ? 'selected' : '' ?>>Chủ tịch HĐQT</option>
                <option value="general_director" <?= ($current_step_data['rep_position'] ?? '') === 'general_director' ? 'selected' : '' ?>>Tổng giám đốc</option>
                <option value="owner" <?= ($current_step_data['rep_position'] ?? '') === 'owner' ? 'selected' : '' ?>>Chủ sở hữu</option>
                <option value="other" <?= ($current_step_data['rep_position'] ?? '') === 'other' ? 'selected' : '' ?>>Khác</option>
            </select>
        </div>
    </div>

    <div class="action-buttons d-flex justify-content-start mt-4 gap-3">
        <?php if (isset($back_url) && $back_url): ?>
            <a href="<?= esc($back_url) ?>" class="d-flex align-items-center justify-content-center btn btn-outline-secondary">
                <i class="bi bi-arrow-left me-2"></i><span class="btn-text">Quay lại</span>
            </a>
        <?php else: ?>
            <a href="<?= base_url('paymentmethods') ?>" class="d-flex align-items-center justify-content-center btn btn-outline-secondary">
                <i class="bi bi-arrow-left me-2"></i><span class="btn-text">Quay lại</span>
            </a>
        <?php endif; ?>
        <button type="submit" class="btn btn-primary d-flex align-items-center justify-content-center">
            Tiếp tục <i class="bi bi-arrow-right ms-2"></i>
        </button>
    </div>
</form>


<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script src="https://cdn.jsdelivr.net/npm/jquery-validation@1.19.5/dist/jquery.validate.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/jquery-validation@1.19.5/dist/additional-methods.min.js"></script>
<script>
    $(() => {
        $.validator.addMethod('idNumber', function(value, element) {
            return this.optional(element) || /^[0-9]+$/.test(value);
        }, 'Số CCCD/CMND chỉ được chứa các chữ số.');

        const validator = $('#representative-form').validate({
            rules: {
                rep_fullname: {
                    required: true,
                    minlength: 2,
                    maxlength: 100
                },
                rep_id_number: {
                    required: true,
                    idNumber: true,
                    minlength: 9,
                    maxlength: 12
                },
                rep_position: {
                    required: true
                }
            },
            messages: {
                rep_fullname: {
                    required: 'Vui lòng nhập họ và tên.',
                    minlength: 'Họ và tên phải có ít nhất 2 ký tự.',
                    maxlength: 'Họ và tên không được vượt quá 100 ký tự.'
                },
                rep_id_number: {
                    required: 'Vui lòng nhập số CCCD/CMND.',
                    minlength: 'Số CCCD/CMND phải có ít nhất 9 chữ số.',
                    maxlength: 'Số CCCD/CMND không được vượt quá 12 chữ số.'
                },
                rep_position: {
                    required: 'Vui lòng chọn chức vụ.'
                }
            },
            errorClass: 'text-danger',
            validClass: 'is-valid',
            errorElement: 'div',
            errorPlacement: function(error, element) {
                error.addClass('invalid-feedback');
                error.insertAfter(element);
            },
            highlight: function(element, errorClass, validClass) {
                $(element).addClass('is-invalid').removeClass(validClass);
            },
            unhighlight: function(element, errorClass, validClass) {
                $(element).removeClass('is-invalid').addClass(validClass);
            },
            submitHandler: function(form) {
                saveStepAndRedirect(2, getFormData('representative-form'));
            }
        });

        $('#rep_id_number').on('input', function() {
            this.value = this.value.replace(/[^0-9]/g, '');
        });
    });
</script>

<?= view('onboarding/shared_scripts') ?>
<?= $this->endSection() ?>