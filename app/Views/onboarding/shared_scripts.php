<script>
    function saveStepAndRedirect(stepNumber, data, customRedirect = null) {
        const button = $('button[type="submit"], #continue-btn');
        const originalText = button.html();
        const feature = getUrlParameter('feature') || 'card';

        $.ajax({
            url: '<?= base_url("onboarding/saveStep") ?>',
            method: 'POST',
            beforeSend: function() {
                button.prop('disabled', true).find('i').replaceWith('<span class="spinner-border spinner-border-sm ms-2 align-middle" role="status" aria-hidden="true"></span>');
            },
            data: {
                '<?= csrf_token() ?>': '<?= csrf_hash() ?>',
                feature: feature,
                step: stepNumber,
                data: data,
            },
            success: function(response) {
                if (response.success) {
                    if (customRedirect) {
                        window.location.href = customRedirect;
                    } else {
                        window.location.href = response.redirect_url;
                    }
                } else {
                    notyf.error(response.message || 'Có lỗi xảy ra');
                    button.prop('disabled', false).find('span').replaceWith(`<i class="bi bi-arrow-right ms-2"></i>`);
                }
            },
            error: function(xhr, status, error) {
                notyf.error('Có lỗi xảy ra khi lưu dữ liệu');
                button.prop('disabled', false).find('span').replaceWith(`<i class="bi bi-arrow-right ms-2"></i>`);
            }
        });
    }

    function completeOnboarding(formData = null, filepondInputsParam = null) {
        const feature = getUrlParameter('feature') || 'card';
        
        const data = new FormData();
        data.append('<?= csrf_token() ?>', '<?= csrf_hash() ?>');
        data.append('feature', feature);
        
        if (formData && filepondInputsParam) {
            Object.keys(filepondInputsParam).forEach(fieldName => {
                const pond = filepondInputsParam[fieldName];
                if (pond && pond.getFiles && pond.getFiles().length > 0) {
                    data.append(fieldName, pond.getFiles()[0].file);
                }
            });
            
            if (formData.confirm_documents) {
                data.append('confirm_documents', formData.confirm_documents);
            }
        }

        $.ajax({
            url: '<?= base_url("onboarding/completeOnboarding") ?>',
            method: 'POST',
            data: data,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    window.location.href = response.redirect_url;
                } else {
                    notyf.error(response.message || 'Có lỗi xảy ra');
                    $('button[type="submit"]').prop('disabled', false).html('Hoàn tất đăng ký');
                }
            },
            error: function(xhr, status, error) {
                notyf.error('Có lỗi xảy ra khi hoàn thành onboarding');
                $('button[type="submit"]').prop('disabled', false).html('Hoàn tất đăng ký');
            }
        });
    }

    function getFormData(formId) {
        try {
            const form = document.getElementById(formId);
            if (!form) {
                return {};
            }

            const formData = new FormData(form);
            const data = {};

            for (let [key, value] of formData.entries()) {
                if (data[key]) {
                    if (Array.isArray(data[key])) {
                        data[key].push(value);
                    } else {
                        data[key] = [data[key], value];
                    }
                } else {
                    data[key] = value;
                }
            }

            return data;
        } catch (error) {
            return {};
        }
    }

    function getUrlParameter(name) {
        name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');
        var regex = new RegExp('[\\?&]' + name + '=([^&#]*)');
        var results = regex.exec(location.search);
        return results === null ? '' : decodeURIComponent(results[1].replace(/\+/g, ' '));
    }
</script>