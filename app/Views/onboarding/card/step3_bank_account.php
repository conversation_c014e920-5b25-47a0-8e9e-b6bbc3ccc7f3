<?php

use App\Models\BankModel;

$banks = model(BankModel::class)->findAll();
?>

<?= $this->extend('layouts/onboarding') ?>

<?= $this->section('content') ?>
<link href="<?= base_url('assets/tom-select/tom-select.bootstrap5.css') ?>" rel="stylesheet">

<div class="mb-4">
    <h2 class="mb-2">Tài khoản nhận tiền</h2>
    <p class="text-muted">Thông tin tài khoản ngân hàng để nhận tiền thanh toán</p>
</div>

<form id="bank-account-form">
    <div class="mb-3">
        <label for="bank_name" class="form-label">Ngân hàng <span class="text-danger">*</span></label>
        <select class="form-select" id="bank_name" name="bank_name" required>
            <option value="">Chọn ngân hàng</option>
            <?php foreach ($banks as $bank): ?>
                <option value="<?= $bank->id ?>" <?= ($current_step_data['bank_name'] ?? '') === $bank->id ? 'selected' : '' ?>><?= $bank->name ?></option>
            <?php endforeach; ?>
        </select>
        <div class="invalid-feedback"></div>
    </div>

    <div class="row g-3 mb-3">
        <div class="col-md-6">
            <label for="account_number" class="form-label">Số tài khoản <span class="text-danger">*</span></label>
            <input type="text" class="form-control" id="account_number" name="account_number" pattern="[0-9]+" value="<?= esc($current_step_data['account_number'] ?? '') ?>" required>
            <div class="invalid-feedback"></div>
        </div>

        <div class="col-md-6">
            <label for="account_holder" class="form-label">Chủ tài khoản <span class="text-danger">*</span></label>
            <input type="text" class="form-control" id="account_holder" name="account_holder" value="<?= esc($current_step_data['account_holder'] ?? '') ?>" required>
            <div class="invalid-feedback"></div>
        </div>
    </div>

    <div class="mb-3">
        <label for="branch_name" class="form-label">Chi nhánh</label>
        <input type="text" class="form-control" id="branch_name" name="branch_name" placeholder="Ví dụ: Chi nhánh Hà Nội" value="<?= esc($current_step_data['branch_name'] ?? '') ?>">
        <div class="invalid-feedback"></div>
    </div>

    <div class="mb-3">
        <div class="alert alert-warning">
            <div class="alert-message">
                <i class="bi bi-exclamation-triangle me-2"></i>
                <strong>Lưu ý quan trọng:</strong>
                <ul class="mb-0 mt-2">
                    <li>Tài khoản phải thuộc sở hữu của doanh nghiệp</li>
                    <li>Tên chủ tài khoản phải trùng khớp với tên công ty đã khai báo</li>
                    <li>Tài khoản phải đang hoạt động và có thể nhận tiền</li>
                </ul>
            </div>
        </div>
    </div>

    <div class="mb-3">
        <div class="form-check">
            <input class="form-check-input" type="checkbox" id="confirm_account" name="confirm_account" <?= ($current_step_data['confirm_account'] ?? '') === '1' ? 'checked' : '' ?> required>
            <label class="form-check-label" for="confirm_account">
                Tôi xác nhận thông tin tài khoản trên là chính xác và thuộc sở hữu của doanh nghiệp
            </label>
        </div>
        <div class="invalid-feedback"></div>
    </div>

    <div class="action-buttons d-flex justify-content-start mt-4 gap-3">
        <?php if (isset($back_url) && $back_url): ?>
            <a href="<?= esc($back_url) ?>" class="d-flex align-items-center justify-content-center btn btn-outline-secondary">
                <i class="bi bi-arrow-left me-2"></i><span class="btn-text">Quay lại</span>
            </a>
        <?php else: ?>
            <a href="<?= base_url('paymentmethods') ?>" class="d-flex align-items-center justify-content-center btn btn-outline-secondary">
                <i class="bi bi-arrow-left me-2"></i><span class="btn-text">Quay lại</span>
            </a>
        <?php endif; ?>
        <button type="submit" class="d-flex align-items-center justify-content-center btn btn-primary">
            Tiếp tục <i class="bi bi-arrow-right ms-2"></i>
        </button>
    </div>
</form>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script src="<?= base_url('assets/tom-select/tom-select.complete.min.js') ?>"></script>
<script src="https://cdn.jsdelivr.net/npm/jquery-validation@1.19.5/dist/jquery.validate.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/jquery-validation@1.19.5/dist/additional-methods.min.js"></script>

<script>
    $(() => {
        $.validator.addMethod('accountNumber', function(value, element) {
            return this.optional(element) || /^[0-9]+$/.test(value);
        }, 'Số tài khoản chỉ được chứa các chữ số.');

        const validator = $('#bank-account-form').validate({
            rules: {
                bank_name: {
                    required: true
                },
                account_number: {
                    required: true,
                    accountNumber: true,
                    minlength: 8,
                    maxlength: 20
                },
                account_holder: {
                    required: true,
                    minlength: 2,
                    maxlength: 100
                },
                confirm_account: {
                    required: true
                },
                branch_name: {
                    minlength: 2,
                    maxlength: 100
                }
            },
            messages: {
                bank_name: {
                    required: 'Vui lòng chọn ngân hàng.'
                },
                account_number: {
                    required: 'Vui lòng nhập số tài khoản.',
                    minlength: 'Số tài khoản phải có ít nhất 8 chữ số.',
                    maxlength: 'Số tài khoản không được vượt quá 20 chữ số.'
                },
                account_holder: {
                    required: 'Vui lòng nhập tên chủ tài khoản.',
                    minlength: 'Tên chủ tài khoản phải có ít nhất 2 ký tự.',
                    maxlength: 'Tên chủ tài khoản không được vượt quá 100 ký tự.'
                },
                confirm_account: {
                    required: 'Vui lòng xác nhận thông tin tài khoản.'
                },
                branch_name: {
                    minlength: 'Tên chi nhánh phải có ít nhất 2 ký tự.',
                    maxlength: 'Tên chi nhánh không được vượt quá 100 ký tự.'
                }
            },
            errorClass: 'text-danger',
            validClass: 'is-valid',
            errorElement: 'div',
            errorPlacement: function(error, element) {
                error.addClass('invalid-feedback');
                if (element.attr('type') === 'checkbox') {
                    error.insertAfter(element.closest('.form-check'));
                } else {
                    error.insertAfter(element);
                }
            },
            highlight: function(element, errorClass, validClass) {
                $(element).addClass('is-invalid').removeClass(validClass);
            },
            unhighlight: function(element, errorClass, validClass) {
                $(element).removeClass('is-invalid').addClass(validClass);
            },
            submitHandler: function(form) {
                saveStepAndRedirect(3, getFormData('bank-account-form'));
            }
        });

        $('#account_number').on('input', function() {
            this.value = this.value.replace(/[^0-9]/g, '');
        });

        const tomSelectBank = new TomSelect('#bank_name', {
            placeholder: 'Chọn ngân hàng',
            searchField: ['text', 'value'],
            allowEmptyOption: false,
            create: false,
            render: {
                option: function(data, escape) {
                    return '<div>' + escape(data.text) + '</div>';
                },
                item: function(data, escape) {
                    return '<div>' + escape(data.text) + '</div>';
                }
            }
        });
    });
</script>

<?= view('onboarding/shared_scripts') ?>
<?= $this->endSection() ?>
