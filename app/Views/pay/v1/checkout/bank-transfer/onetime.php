<?php

use App\Features\PaymentGateway\Contexts\BankTransferCheckoutContext;

/** @var BankTransferCheckoutContext $context **/

?>

<?= $this->extend('pay/v1/layout') ?>

<?= $this->section('content') ?>
<style>
.form-check:has(input[name="branch_profile"]:checked) {
    --bs-border-opacity: 1;
    border-color: rgba(var(--bs-primary-rgb), var(--bs-border-opacity)) !important;
}
</style>

<div class="card mx-auto" style="width: 44rem; max-width: 100%;">
    <div class="card-body py-5">
        <div class="mx-auto text-primary mb-2" style="width: 24px; height: 24px;">
            <div style="position: relative; top: 50%; left: 50%; width: 100%; height: 100%;">
                <?php for ($i = 0; $i < 12; $i++): ?>
                    <div class="animate-spinner rounded-lg"></div>
                <?php endfor; ?>
            </div>
        </div>
        
        <p class="text-primary fw-bold text-lg title text-center"><PERSON>ang đợi thanh toán...</p>
        <p class="text-muted text-center message">Vui lòng quét mã QR bên dưới để tiến hành thanh toán đơn hàng.</p>
        
        <div class="<?= count($context->pgBranchProfiles) > 1 ? '' : 'd-none' ?>">
            <p class="mb-2 fw-bold">Chọn tài khoản ngân hàng nhận thanh toán:</p>
            <div class="row g-2 w-100">
                <?php foreach ($context->pgBranchProfiles as $index => $branchProfile): ?>
                    <div class="col">
                        <label class="form-check d-flex align-items-center gap-3 border p-3 rounded-3 shadow d-flex h-100 form-check-label" for="branch_profile_<?= $index ?>">
                            <div class="my-auto">
                                <input class="form-check-input m-0" type="radio" name="branch_profile" id="branch_profile_<?= $index ?>" value="<?= esc($index) ?>" <?= $index === 0 ? 'checked' : '' ?> style="width: 16px; height: 16px;">
                            </div>
                            <div class="">
                                <img src="<?= base_url(sprintf('assets/images/banklogo/%s', $branchProfile->bank_account->bank_icon_path)) ?>" alt="<?= esc($branchProfile->bank_account->bank_short_name) ?>" class="me-2" style="width: 24px; height: 24px; vertical-align: middle;">
                                <span class="text-black fw-semibold"><?= esc($branchProfile->bank_account->bank_short_name) ?></span>
                            </div>
                        </label>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
        
        <div class="row mt-1 w-100 g-4">
            <div class="col-sm-6 d-flex flex-column justify-content-center align-items-center">
                <ul class="list-group list-group-flush w-100">
                    <li class="list-group-item px-0">
                        <span class="d-flex align-items-start gap-2">
                            <img src="<?= base_url(sprintf('assets/images/banklogo/%s', $context->pgBranchProfiles[0]->bank_account->bank_icon_path)) ?>" alt="<?= esc($context->pgBranchProfiles[0]->bank_account->bank_short_name) ?>" style="height: 24px; width: 24px;">
                            <span><?= esc($context->pgBranchProfiles[0]->bank_account->bank_full_name) ?></span>
                        </span>
                    </li>
                    <li class="list-group-item px-0">
                        <span class="text-muted">Số tài khoản</span><br>
                        <span class="account-number"><?= esc($context->pgBranchProfiles[0]->qrcode['account_number']) ?></span>
                    </li>
                    <li class="list-group-item px-0">
                        <span class="text-muted">Tên thụ hưởng</span><br>
                        <span class="holder_name"><?= esc($context->pgBranchProfiles[0]->bank_account->account_holder_name ?? $context->pgBranchProfiles[0]->va->sub_holder_name) ?></span>
                    </li>
                    <li class="list-group-item px-0">
                        <span class="text-muted">Số tiền</span><br>
                        <span class="text-primary" class="amount"><?= esc(number_format($fields['order_amount'], 0, '.', '.')) ?> <?= esc($fields['currency']) ?></span>
                    </li>
                    <li class="list-group-item px-0">
                        <span class="text-muted">Nội dung chuyển khoản</span><br>
                        <span class="remark">
                            <?php if (property_exists($context->pgBranchProfiles[0], 'va') && $context->pgBranchProfiles[0]->va->acc_type === 'Virtual'): ?>
                                TKP<?= esc($context->pgBranchProfiles[0]->va->sub_account) ?>
                            <?php endif ?>
                            
                            <?= esc($fields['order_id']) ?>
                        </span>
                    </li>
                </ul>
                <div class="alert alert-warning mb-0 mt-2 w-100">
                    <div class="alert-message">
                        <i class="bi bi-info-circle me-1"></i>
                        Vui lòng chuyển khoản đúng nội dung để hệ thống tự động xác nhận.
                    </div>
                </div>
            </div>
            <div class="col-sm-6 d-flex flex-column justify-content-center align-items-center">
                <img src="<?= $context->pgBranchProfiles[0]->qrcode['url'] ?>" class="w-100 qrcode" />
                <small class="text-muted text-center">
                    Sử dụng ứng dụng ngân hàng/ví điện tử để quét mã QR và thanh toán nhanh chóng.
                </small>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    const BRANCH_PROFILES = <?= json_encode($context->pgBranchProfiles) ?>;
    const ORDER_ID = `<?= $fields['order_id'] ?>`;
    const OPERATION = `<?= $fields['operation'] ?>`;
    const ORDER_STATUS = `<?= $context->pgOrder ? $context->pgOrder->order_status : '' ?>`
    
    $('input[name="branch_profile"]').on('change', function() {
        const selectedIndex = parseInt($(this).val());
        const selectedProfile = BRANCH_PROFILES[selectedIndex];

        const bankIcon = selectedProfile.bank_account.bank_icon_path;
        const bankName = selectedProfile.bank_account.bank_full_name;
        $('.list-group-item:first-child img').attr('src', `<?= base_url('assets/images/banklogo') ?>/${bankIcon}`);
        $('.list-group-item:first-child img').attr('alt', bankName);
        $('.list-group-item:first-child span span').text(bankName);

        $('.account-number').text(selectedProfile.qrcode.account_number);
        $('.holder-name').text(selectedProfile.qrcode.holder_name);
        $('.remark').text(selectedProfile.qrcode.remark);
        $('.qrcode').attr('src', selectedProfile.qrcode.url);
    });
    
    function sync() {
        const searchParams = new URLSearchParams(window.location.search);
        
        $.ajax({
            method: 'POST',
            url: `<?= base_url('pay/v1/checkout/sync') ?>?` + searchParams,
            success: (response) => {
                if (response.session_status === 'FULFILLED') {
                    if (SUCCESS_URL) {
                        window.location.href = SUCCESS_URL;
                    } else {
                        if (OPERATION === 'VERIFY') {
                            displaySuccessCard('Xác thực thành công', 'Phiên xác thực đã hoàn tất.')
                        } else {
                            displaySuccessCard('Thanh toán thành công', 'Đơn hàng của bạn đã hoàn tất thanh toán.')
                        }
                    }
                    
                    return;
                }
                
                if (response.session_status === 'FAILED') {
                    handleErrorPayment({ cause: 'SERVER_FAILED' });
                    return;
                }
                
                setTimeout(() => sync(), 3000);
            },
            error: (err) => {
                if (err.status === 404) {
                    handleErrorPayment({ cause: 'INVALID_REQUEST' });
                } else {
                    handleErrorPayment({ cause: 'SERVER_FAILED' });
                }
            }
        });
    }
</script>
<?= $this->endSection() ?>