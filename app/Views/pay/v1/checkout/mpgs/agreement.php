<?php

/** @var MpgsCheckoutContext $context **/

// $invalid_session = is_object($pg_order) && !$is_initiate_authentication_process && !$is_fulfilled_checkout;
$invalid_session = false;
$current_url = str_replace('/index.php', '', current_url(true)->setQuery(http_build_query($_GET))->__toString());

$verifyFields = [
    'merchant' => $fields['merchant'],
    'payment_method' => $fields['payment_method'],
    'currency' => $fields['currency'],
    'order_id' => $verify_order_id,
    'order_amount' => 0,
    'operation' => 'VERIFY',
    'customer_id' => $fields['customer_id'],
    'order_description' => 'Thêm phương thức thanh toán',
    'success_url' => $current_url . '&step=2',
    'cancel_url' => $current_url,
];

$GLOBALS['invalid_session'] = $invalid_session;

?>

<?= $this->extend('pay/v1/layout') ?>

<?= $this->section('content') ?>
    <?php if (!$context->isInitiateOrderAuthenticationProcess() && !$context->pgSession): ?>
        <div style="max-width: 100%; width: 520px;" class="mx-auto">
            <div class="d-flex align-items-center justify-content-center text-center mb-4">
                <img src="https://sepay.vn//assets/img/logo/sepay-blue-154x50.png" class="d-inline-block" style="height: 30px;">
            </div>
            
            <div class="card mx-auto mt-2 mb-0">
                <div class="row mx-0 border-bottom rounded-top overflow-hidden">
                    <div class="col-6 border-top p-3 border-4 text-primary border-primary">
                        <i class="bi bi-1-circle-fill fs-1"></i>
                        <p class="mb-0 mt-2">Thêm thẻ tín dụng<br>/ghi nợ</p>
                    </div>
                    <div class="col-6 border-top p-3 border-4 <?=  $step >= 2 ? 'text-primary border-primary' : '' ?>">
                        <i class="bi bi-2-circle-fill fs-1"></i>
                        <p class="mb-0 mt-2">Thanh toán <br>& đăng ký gia hạn</p>
                    </div>
                </div>
            </div>
            <div class="card mx-auto rounded-0" data-state="init">
                <div class="card-body">
                    <?php if ($step == 2): ?>
                        <?php if ($fields['order_description']): ?>
                        <div class="alert alert-info">
                            <div class="alert-message">
                                <i class="bi bi-info-circle-fill text-info me-1"></i> <?= esc($fields['order_description']) ?>
                            </div>
                        </div>
                        <?php endif ?>
                        <table class="table fs-5">
                            <tr>
                                <td class="">Tên gói đăng ký</td>
                                <td class="text-end text-dark"><?= $fields['agreement_name'] ?></td>
                            </tr>
                            <tr>
                                <td class="">Chu kỳ thanh toán</td>
                                <td class="text-end text-dark"><?= $context->getAgreementPaymentFrequencyLabel() ?></td>
                            </tr>
                            <tr>
                                <td>Số tiền thanh toán kỳ tiếp theo</td>
                                <td class="text-end"><?= number_format($fields['agreement_amount_per_payment']) ?> <?= $context->getCurrencyLabel() ?></td>
                            </tr>
                            <?php if (isset($fields['placeholder_order_amount']) && $fields['placeholder_order_amount'] > 0): ?>
                                <tr>
                                    <td class="">Số tiền tạm ứng<br><small>(sẽ hoàn lại sau khi đăng ký)</small></td>
                                    <td class="text-end"><?= number_format($fields['placeholder_order_amount']) ?> <?= $context->getCurrencyLabel() ?></td>
                                </tr>
                            <?php else: ?>
                            <tr>
                                <td class="">Số tiền thanh toán hôm nay</td>
                                <td class="text-end text-primary fw-bold"><?= number_format($fields['order_amount']) ?> <?= $context->getCurrencyLabel() ?></td>
                            </tr>
                            <?php endif ?>
                            <?php if (isset($fields['order_discount_amount'])): ?>
                                <tr>
                                    <td class="">Giảm giá</td>
                                    <td class="text-end"><?= number_format($fields['order_discount_amount']) ?> <?= $context->getCurrencyLabel() ?><br><small><?= esc($fields['order_discount_description']) ?></small></td>
                                </tr>
                            <?php endif ?>
                        </table>
                    <?php endif ?>

                    <?php if ($step === 1): ?>
                        <p>Thêm thẻ tín dụng/ghi nợ để cho phép <b><?= esc($pg_merchant->name) ?></b> thực hiện các khoản thanh toán định kỳ trong tương lai.</p>

                        <div class="alert alert-info">
                            <div class="alert-message">
                                <span class="badge bg-primary me-1">Miễn phí</span> Hệ thống sẽ thực hiện giao dịch 0đ để xác thực thẻ hợp lệ.
                            </div>
                        </div>
                    <?php endif ?>

                    <div>
                        <?php if (count($context->pgCards)): ?>
                            <?php foreach ($context->pgCards as $card): ?>
                                <div class="d-flex align-items-center gap-3 border rounded-lg text-left p-3 mb-2">
                                    <div style="width: 3rem; flex: none;">
                                        <?php if ($card->card_brand === 'VISA'): ?>
                                            <svg viewBox="0 0 24 16" fill="none" xmlns="http://www.w3.org/2000/svg" role="presentation" focusable="false" class="p-Logo p-Logo--md p-CardBrandIcon"><g><rect stroke="#DDD" fill="#FFF" x=".25" y=".25" width="23.5" height="15.5" rx="2"></rect><path d="M2.788 5.914A7.201 7.201 0 0 0 1 5.237l.028-.125h2.737c.371.013.672.125.77.519l.595 2.836.182.854 1.666-4.21h1.799l-2.674 6.167H4.304L2.788 5.914Zm7.312 5.37H8.399l1.064-6.172h1.7L10.1 11.284Zm6.167-6.021-.232 1.333-.153-.066a3.054 3.054 0 0 0-1.268-.236c-.671 0-.972.269-.98.531 0 .29.365.48.96.762.98.44 1.435.979 1.428 1.681-.014 1.28-1.176 2.108-2.96 2.108-.764-.007-1.5-.158-1.898-.328l.238-1.386.224.099c.553.23.917.328 1.596.328.49 0 1.015-.19 1.022-.604 0-.27-.224-.466-.882-.769-.644-.295-1.505-.788-1.491-1.674C11.878 5.84 13.06 5 14.74 5c.658 0 1.19.138 1.526.263Zm2.26 3.834h1.415c-.07-.308-.392-1.786-.392-1.786l-.12-.531c-.083.23-.23.604-.223.59l-.68 1.727Zm2.1-3.985L22 11.284h-1.575s-.154-.71-.203-.926h-2.184l-.357.926h-1.785l2.527-5.66c.175-.4.483-.512.889-.512h1.316Z" fill="#1434CB"></path></g></svg>
                                        <?php elseif ($card->card_brand === 'MASTERCARD'): ?>
                                            <svg viewBox="0 0 24 16" fill="none" xmlns="http://www.w3.org/2000/svg" role="presentation" focusable="false" class="p-Logo p-Logo--md p-CardBrandIcon"><rect fill="#252525" height="16" rx="2" width="24"></rect><circle cx="9" cy="8" fill="#eb001b" r="5"></circle><circle cx="15" cy="8" fill="#f79e1b" r="5"></circle><path d="M12 4c1.214.912 2 2.364 2 4s-.786 3.088-2 4c-1.214-.912-2-2.364-2-4s.786-3.088 2-4z" fill="#ff5f00"></path></svg>
                                        <?php elseif ($card->card_brand === 'JCB'): ?>
                                            <svg viewBox="0 0 24 16" fill="none" xmlns="http://www.w3.org/2000/svg" role="presentation" focusable="false" class="p-Logo p-Logo--md p-CardBrandIcon p-CardBrandIcon--visible"><path d="M0 16h4.8c1.092 0 2.4-1.195 2.4-2.133V0H2.4C1.308 0 0 1.195 0 3.2V16Z" fill="#047AB1"></path><path d="M2.724 10.816c-.922 0-1.838-.115-2.724-.341V9.3c.687.378 1.473.591 2.28.619.924 0 1.44-.576 1.44-1.365V5.333H6v3.222c0 1.258-.744 2.261-3.276 2.261Z" fill="#fff"></path><path d="M8.4 16h4.8c1.092 0 2.4-1.195 2.4-2.133V0h-4.8C9.708 0 8.4 1.195 8.4 3.2V16Z" fill="#D42D06"></path><path d="M8.4 6.08c.696-.597 1.896-.97 3.84-.885 1.056.042 2.16.32 2.16.32v1.184a5.313 5.313 0 0 0-2.076-.608C10.848 5.973 9.948 6.709 9.948 8c0 1.29.9 2.027 2.376 1.92a5.387 5.387 0 0 0 2.076-.619v1.174s-1.104.288-2.16.33c-1.944.086-3.144-.288-3.84-.885V6.08Z" fill="#fff"></path><path d="M16.8 16h4.8c1.092 0 2.4-1.195 2.4-2.133V0h-4.8c-1.092 0-2.4 1.195-2.4 3.2V16Z" fill="#67B637"></path><path d="M22.8 9.28c0 .853-.744 1.387-1.74 1.387H16.8V5.333h3.876l.276.011c.876.043 1.524.501 1.524 1.29 0 .62-.444 1.153-1.248 1.28v.033C22.116 8 22.8 8.5 22.8 9.28Zm-3.06-3.104a1.226 1.226 0 0 0-.156-.01h-1.44v1.343h1.596c.3-.064.552-.309.552-.672a.657.657 0 0 0-.552-.661Zm.18 2.176a1.16 1.16 0 0 0-.192-.01h-1.584v1.46h1.584l.192-.02a.716.716 0 0 0 .552-.715c0-.374-.24-.64-.552-.715Z" fill="#fff"></path></svg>
                                        <?php else: ?>
                                            <div class="border border-slate-100 px-3 py-2 rounded-lg">UNK</div> 
                                        <?php endif ?>
                                    </div>
                                    <div>
                                        <p class="mb-0 font-medium font-monospace"><?= str_replace('x', '&bull;', $card->card_number) ?></p>
                                        <p class="mb-0 text-sm font-monospace"><?= $card->card_holder_name ?></p>
                                        <p class="mb-0 font-medium text-xs mt-1">Hết hạn <?= substr($card->card_expiry, 0, 2) . '/' . substr($card->card_expiry, 2, 4) ?></p>
                                    </div>
                                    <?php if ($step == 1): ?>
                                    <div class="ms-auto">
                                        <button class="btn btn-outline-danger btn-sm rounded d-flex align-items-center gap-2" id="btn-revoke-card" data-card-id="<?= $card->hash_id ?>" type="button">
                                            <span class="spinner-border spinner-border-sm d-none" role="status" aria-hidden="true"></span>
                                            <i class="bi bi-x-lg"></i> Gỡ
                                        </button>
                                    </div>
                                    <?php endif ?>
                                </div>
                            <?php endforeach; ?>
                        <?php endif ?>
                    </div>

                    <?php if ($step == 1): ?>
                    <form action="<?= base_url('pay/v1/checkout/init') ?>" method="POST" id="form-add-payment-method" class="mb-0">
                        <?php foreach ($verifyFields as $field => $value): ?>
                            <input type="hidden" name="<?= $field ?>" value="<?= $value ?>">
                        <?php endforeach; ?>
                        <input type="hidden" name="signature" value="<?= $context->signFields($verifyFields) ?>">
                        <?php if (count($context->pgCards) > 0): ?>
                            <button type="submit" class="btn btn-light w-100" id="btn-add-payment-method"><i class="bi bi-credit-card"></i> Thay đổi thẻ tín dụng/ghi nợ</button>
                        <?php else: ?>
                            <button type="submit" class="btn btn-primary btn-lg btn-block w-100" id="btn-add-payment-method"><i class="bi bi-credit-card"></i> Thêm thẻ tín dụng/ghi nợ</button>
                        <?php endif ?>

                        <div class="mt-2 d-flex align-items-center justify-content-between">
                            <div>
                                <p class="mb-1 text-sm">Chúng tôi chấp nhận</p>
                                <div class="d-flex align-items-center gap-2">
                                    <svg viewBox="0 0 24 16" style="height: 1.2rem" fill="none" xmlns="http://www.w3.org/2000/svg" role="presentation" focusable="false" class="p-Logo p-Logo--md p-CardBrandIcon"><g><rect stroke="#DDD" fill="#FFF" x=".25" y=".25" width="23.5" height="15.5" rx="2"></rect><path d="M2.788 5.914A7.201 7.201 0 0 0 1 5.237l.028-.125h2.737c.371.013.672.125.77.519l.595 2.836.182.854 1.666-4.21h1.799l-2.674 6.167H4.304L2.788 5.914Zm7.312 5.37H8.399l1.064-6.172h1.7L10.1 11.284Zm6.167-6.021-.232 1.333-.153-.066a3.054 3.054 0 0 0-1.268-.236c-.671 0-.972.269-.98.531 0 .29.365.48.96.762.98.44 1.435.979 1.428 1.681-.014 1.28-1.176 2.108-2.96 2.108-.764-.007-1.5-.158-1.898-.328l.238-1.386.224.099c.553.23.917.328 1.596.328.49 0 1.015-.19 1.022-.604 0-.27-.224-.466-.882-.769-.644-.295-1.505-.788-1.491-1.674C11.878 5.84 13.06 5 14.74 5c.658 0 1.19.138 1.526.263Zm2.26 3.834h1.415c-.07-.308-.392-1.786-.392-1.786l-.12-.531c-.083.23-.23.604-.223.59l-.68 1.727Zm2.1-3.985L22 11.284h-1.575s-.154-.71-.203-.926h-2.184l-.357.926h-1.785l2.527-5.66c.175-.4.483-.512.889-.512h1.316Z" fill="#1434CB"></path></g></svg>
                                    <svg viewBox="0 0 24 16" style="height: 1.2rem" fill="none" xmlns="http://www.w3.org/2000/svg" role="presentation" focusable="false" class="p-Logo p-Logo--md p-CardBrandIcon"><rect fill="#252525" height="16" rx="2" width="24"></rect><circle cx="9" cy="8" fill="#eb001b" r="5"></circle><circle cx="15" cy="8" fill="#f79e1b" r="5"></circle><path d="M12 4c1.214.912 2 2.364 2 4s-.786 3.088-2 4c-1.214-.912-2-2.364-2-4s.786-3.088 2-4z" fill="#ff5f00"></path></svg>
                                    <svg viewBox="0 0 24 16" style="height: 1.2rem" fill="none" xmlns="http://www.w3.org/2000/svg" role="presentation" focusable="false" class="p-Logo p-Logo--md p-CardBrandIcon p-CardBrandIcon--visible"><path d="M0 16h4.8c1.092 0 2.4-1.195 2.4-2.133V0H2.4C1.308 0 0 1.195 0 3.2V16Z" fill="#047AB1"></path><path d="M2.724 10.816c-.922 0-1.838-.115-2.724-.341V9.3c.687.378 1.473.591 2.28.619.924 0 1.44-.576 1.44-1.365V5.333H6v3.222c0 1.258-.744 2.261-3.276 2.261Z" fill="#fff"></path><path d="M8.4 16h4.8c1.092 0 2.4-1.195 2.4-2.133V0h-4.8C9.708 0 8.4 1.195 8.4 3.2V16Z" fill="#D42D06"></path><path d="M8.4 6.08c.696-.597 1.896-.97 3.84-.885 1.056.042 2.16.32 2.16.32v1.184a5.313 5.313 0 0 0-2.076-.608C10.848 5.973 9.948 6.709 9.948 8c0 1.29.9 2.027 2.376 1.92a5.387 5.387 0 0 0 2.076-.619v1.174s-1.104.288-2.16.33c-1.944.086-3.144-.288-3.84-.885V6.08Z" fill="#fff"></path><path d="M16.8 16h4.8c1.092 0 2.4-1.195 2.4-2.133V0h-4.8c-1.092 0-2.4 1.195-2.4 3.2V16Z" fill="#67B637"></path><path d="M22.8 9.28c0 .853-.744 1.387-1.74 1.387H16.8V5.333h3.876l.276.011c.876.043 1.524.501 1.524 1.29 0 .62-.444 1.153-1.248 1.28v.033C22.116 8 22.8 8.5 22.8 9.28Zm-3.06-3.104a1.226 1.226 0 0 0-.156-.01h-1.44v1.343h1.596c.3-.064.552-.309.552-.672a.657.657 0 0 0-.552-.661Zm.18 2.176a1.16 1.16 0 0 0-.192-.01h-1.584v1.46h1.584l.192-.02a.716.716 0 0 0 .552-.715c0-.374-.24-.64-.552-.715Z" fill="#fff"></path></svg>
                                </div>
                            </div>
                            <div>
                                <p class="mb-0 text-sm">Bảo mật thanh toán</p>
                                <div class="d-flex align-items-center gap-1 text-success">
                                    <i class="bi bi-shield-lock-fill fs-4"></i>
                                    <span class="fw-bold">3DSecure</span>
                                </div>
                            </div>
                        </div>
                        
                        <?php if (count($context->pgCards) > 0): ?>
                            <div class="mt-4">
                                <p class="mb-1 text-center">hoặc không cần thay đổi</p>
                                <a href="<?= str_replace('&step=1', '', $current_url . '&step=2') ?>" class="btn btn-primary btn-block btn-lg w-100" id="btn-change-payment-method">Tiếp tục thanh toán <i class="bi bi-arrow-right"></i></a>
                            </div>
                        <?php endif ?>
                    </form>
                    <?php endif ?>
                    
                    <?php if (count($context->pgCards)): ?>
                        <?php
                        
                        $checkoutFields = array_merge($context->getPaymentFields(), ['card_id' => $context->pgCards[0]->hash_id]);
                        $checkoutFields['transaction_id'] = sprintf('INIT-%s', date('YmdHis'));
                        unset($checkoutFields['signature']);
                        
                        ?>
                        
                        <form action="<?= base_url('pay/v1/checkout/init') ?>" method="POST" id="form-subscribe" class="mt-4 mb-0 <?= $step == 2 ? 'd-block' : 'd-none' ?>">
                            <p class="mt-2 mb-1">Bằng việc chấp nhận đăng ký, bạn cho phép <b><?= esc($pg_merchant->name) ?></b> tính phí thẻ của bạn cho các khoản thanh toán trong tương lai theo các điều khoản trên.</p>
                            <?php foreach ($checkoutFields as $field => $value): ?>
                                <input type="hidden" name="<?= $field ?>" value="<?= $value ?>">
                            <?php endforeach; ?>
                            <input type="hidden" name="signature" value="<?= $context->signFields($checkoutFields) ?>">
                            <div class="mt-2 d-flex flex-column w-100 align-items-center justify-content-between">
                                <p class="mb-2 text-sm text-success">Bảo mật thanh toán với <i class="bi bi-shield-lock-fill"></i> 3DSecure</p>
                                <button type="submit" class="btn btn-primary mb-0 btn-block btn-lg w-100">Tiến hành thanh toán & đăng ký</button>
                                <a href="<?= str_replace('step=2', '', $current_url) ?>" class="text-muted text-center mt-3"><i class="bi bi-arrow-left"></i> Thay đổi thẻ</a>
                            </div>
                        </form>
                    <?php endif ?>
                </div>
            </div>
        </div>
    <?php elseif ($context->isInitiateOrderAuthenticationProcess() || ($context->pgSession && is_null($order_authentication_status))): ?>
        <?= component('pay/v1/_components/pending-card', [
            'heading' => 'Đang khởi tạo phiên...',
        ]) ?>
        
        <?php if (is_object($authenticate_3ds2)): ?>
            <div id="initiate3dsSimpleRedirect" xmlns="http://www.w3.org/1999/html">
                <iframe id="methodFrame" name="methodFrame" height="100" width="200" style="display: none"></iframe> 
                <form id="initiate3dsSimpleRedirectForm" method="POST" action="<?= $authenticate_3ds2->methodUrl ?>" target="methodFrame">
                    <input type="hidden" name="threeDSMethodData" value="<?= $authenticate_3ds2->methodPostData ?>" />
                </form>
                <script id="initiate-authentication-script"> var e=document.getElementById("initiate3dsSimpleRedirectForm"); if (e) { e.submit(); if (e.parentNode !== null) { e.parentNode.removeChild(e); } } </script> 
            </div>
        <?php endif ?>
    <?php elseif ($context->isSessionPending()): ?>
        <?= component('pay/v1/_components/pending-card', [
            'heading' => 'Đang xác thực...',
        ]) ?>
        
        <?php if (is_string($authenticate_3ds2)): ?>
            <div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; z-index: 100;" id="iframe">
                <?= $authenticate_3ds2 ?>
            </div>
        <?php endif ?>
    <?php endif ?>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    const IS_INITIATE_ORDER_AUTHENTICATION_PROCESS = <?= $context->isInitiateOrderAuthenticationProcess() ? 'true' : 'false' ?>;
    const REPLACE_FIELDS = <?= json_encode($fields) ?>
      
    $(document).ready(() => {
        if (IS_INITIATE_ORDER_AUTHENTICATION_PROCESS) {
            replaceFields()
            sync()
            return;
        }
        
        if (SESSION_STATUS === 'PENDING') {
            sync();
            return;
        }
    })
    
    $('#btn-revoke-card').on('click', () => {
        const cardId = $('#btn-revoke-card').data('card-id');
        
        if (!cardId) return

        $('#btn-revoke-card').prop('disabled', true)
        $('#btn-revoke-card [role="status"]').removeClass('d-none')
        $('#btn-revoke-card i').addClass('d-none')
        
        $.ajax({
            url: `<?= base_url('pay/v1/checkout/revokePaymentCard') ?>`,
            method: 'POST',
            data: $('#form-subscribe').serialize(),
            success: (res) => {
                window.location.reload()
            },
            error: (err) => {
                $('#btn-revoke-card').prop('disabled', false)
                $('#btn-revoke-card [role="status"]').addClass('d-none')
                $('#btn-revoke-card i').removeClass('d-none')
                notyf.error('Đã có lỗi xảy ra, vui lòng liên hệ nhà bán hàng để được hỗ trợ.')
            }
        })
    });
    
    function replaceFields() {
        const url = new URL(window.location);
        
        Object.keys(REPLACE_FIELDS).forEach(key => {
            url.searchParams.set(key, REPLACE_FIELDS[key])
        });
        
        window.history.replaceState({}, '', url);
    }
    
    function confirmAuthenticationInitiated()
    {
        const searchParams = new URLSearchParams(window.location.search);
        
        $.ajax({
            method: 'POST',
            url: `<?= base_url('pay/v1/checkout/confirm') ?>?` + searchParams,
            success: (response) => {
                if (response.status === 'AUTHENTICATION_INITIATED' && response.authentication_initiated) {
                    window.location.reload()
                } else {
                    setTimeout(() => confirmAuthenticationInitiated(), 3000)
                }
            },
            error: (err) => {
                alert('Lỗi')
                clearInterval(confirmInitiateAuthenticationInterval)
            }
        });
    }
    
    function confirmOrder() {
        const searchParams = new URLSearchParams(window.location.search);
        
        $.ajax({
            method: 'POST',
            url: `<?= base_url('pay/v1/checkout/confirm') ?>?` + searchParams,
            success: (response) => {
                if (response.fulfilled) {
                    if (SUCCESS_URL) {
                        window.location.href = SUCCESS_URL;
                    } else {
                        displaySuccessCard('Thanh toán thành công', 'Bạn đã thanh toán thành công đơn hàng.')
                    }
                } else if (!response.fulfilled && response.status === 'AUTHENTICATION_UNSUCCESSFUL') {
                    alert('Đã hủy')
                } else {
                    setTimeout(() => confirmOrder(), 3000)
                }
            },
            error: (err) => {
                alert('Lỗi')
                console.log(err);
            }
        });
    }
    
    function sync() {
        const searchParams = new URLSearchParams(window.location.search);
        
        $.ajax({
            method: 'POST',
            url: `<?= base_url('pay/v1/checkout/sync') ?>?` + searchParams,
            success: (response) => {
                console.log(response)
                
                if (response.session_status === 'FULFILLED') {
                    if (SUCCESS_URL) {
                        window.location.href = SUCCESS_URL;
                    } else {
                        if (OPERATION === 'VERIFY') {
                            displaySuccessCard('Xác thực thành công', 'Phiên xác thực đã hoàn tất.')
                        } else {
                            displaySuccessCard('Thanh toán thành công', 'Đơn hàng của bạn đã hoàn tất thanh toán.')
                        }
                    }
                    
                    return;
                }
                
                if (IS_INITIATE_ORDER_AUTHENTICATION_PROCESS && response.session_status === 'PENDING' && response.order_status === 'AUTHENTICATION_INITIATED') {
                    window.location.reload()
                }
                
                if (response.session_status === 'FAILED') {
                    displayErrorCard('Lỗi không xác định', 'Đã có lỗi xảy ra trong quá trình thực hiện, vui lòng liên hệ nhà bán hàng để được hỗ trợ.')
                    return;
                }
                
                setTimeout(() => sync(), 3000);
            },
            error: (err) => {
                alert('Lỗi!!!')
                console.log(err)
                displayErrorCard('Lỗi không xác định', 'Đã có lỗi xảy ra trong quá trình thực hiện, vui lòng liên hệ nhà bán hàng để được hỗ trợ.')
                return;
            }
        });
    }
</script>
<?= $this->endSection() ?>
