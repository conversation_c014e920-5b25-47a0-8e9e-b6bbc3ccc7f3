<?php

/** @var MpgsCheckoutContext $context **/

$invalid_session = !$fields['order_id'] || !$fields['session_id'] || !$fields['success_indicator'];

$GLOBALS['invalid_session'] = $invalid_session;

?>

<?= $this->extend('pay/v1/layout') ?>

<?= $this->section('content') ?>
    <?php if (!isset($context->pgSession)): ?>
        <?= component('pay/v1/_components/error-card', [
            'heading' => 'Phiên không khả dụng', 
            'message' => 'Phiên không tồn tại hoặc đã bị hết hạn',
            'trace_id' => $fields['trace_id'] ?? null,
            'cancel_url' => $fields['cancel_url'] ?? null
        ]) ?>
    <?php elseif (isset($context->pgSession) && $context->pgSession->status === 'CANCELLED'): ?>
        <?= component('pay/v1/_components/error-card', [
            'heading' => 'Phiên đã bị hủy', 
            'message' => 'Nếu có gì đó không đúng? Vui lòng liên hệ nhà bán hàng để được hỗ trợ',
            'trace_id' => $fields['trace_id'] ?? null,
            'cancel_url' => $fields['cancel_url'] ?? null
        ]) ?>
    <?php elseif (($context->pgSession && $context->pgSession->status === 'PENDING') || $context->pgOrder): ?>
        <?= component('pay/v1/_components/pending-card', [
            'heading' => 'Đang xác thực...', 
        ]) ?>
    <?php elseif ($context->pgSession && $context->pgSession->status === 'INITIATED'): ?>
        <?= component('pay/v1/_components/pending-card', [
            'heading' => !isset($_SERVER['HTTP_REFERER']) ? 'Đang xác thực...' : 'Đang khởi tạo phiên...',
        ]) ?>
    <?php endif ?>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script src="https://test-gateway.mastercard.com/static/checkout/checkout.min.js" 
    data-error="handleErrorPayment" 
    data-cancel="handleCancelPayment" 
    data-beforeRedirect="handleBeforeRedirect"
    data-complete="handleCompletePayment">
</script>
<script>
    const SESSION_ID = `<?= $fields['session_id'] ?>`;
    const ORDER_ID = `<?= $fields['order_id'] ?>`;
    const SUCCESS_INDICATOR = `<?= $fields['success_indicator'] ?>`;
    const OPERATION = `<?= $fields['operation'] ?>`;
    const ORDER_STATUS = `<?= $context->pgOrder ? $context->pgOrder->order_status : '' ?>`
    
    $(document).ready(() => {
        if (SESSION_STATUS === 'INITIATED' && document.referrer.includes(window.location.hostname)) {
            Checkout.configure({
                session: {
                    id: SESSION_ID,
                }
            });
            
            setTimeout(() => {
                Checkout.showPaymentPage();
            }, 1000);
            
            return
        }
        
        if (SESSION_STATUS === 'PENDING' || ORDER_STATUS) {
            sync();
            return
        }
    })
    
    function handleCompletePayment(mpgs) {
        if (mpgs.resultIndicator != SUCCESS_INDICATOR) {
            handleErrorPayment({ cause: 'INVALID_REQUEST' })
            return;
        }

        sync();
    }
    
    function handleErrorPayment(error) {   
        if (ERROR_URL) {
            const url = new URL(ERROR_URL)
            const params = new URLSearchParams(ERROR_URL);
            params.set('cause', error.cause);
            
            window.location.href = url.origin + url.pathname + '?' + params.toString();
            return;
        }
        
        let message;
        
        if (error.cause === 'INVALID_REQUEST') {
            message = 'Yêu cầu không hợp lệ.';
        } else if (error.cause === 'SERVER_BUSY') {
            message = 'Hệ thống đang bận.';
        } else if (error.cause === 'SERVER_FAILED') {
            message = 'Đã có lỗi xảy ra trong quá trình thanh toán.';
        } else if (error.cause === 'CONSTRAINT_VIOLATION') {
            message = 'Yêu cầu vi phạm ràng buộc.';
        } else if (error.cause === 'REQUEST_REJECTED') {
            message = 'Yêu cầu bị từ chối.';
        } else {
            message = 'Lỗi không xác định.'
        }
        
        displayErrorCard('Đã có lỗi xảy ra', message)
    }
    
    function handleCancelPayment() {
        displayLoadingCard();
      
        const params = new URLSearchParams(window.location.search);
        const fields = {};
        params.forEach((value, key) => {
            fields[key] = value;
        })
        
        navigator.sendBeacon(`<?= base_url('pay/v1/checkout/cancel') ?>`, JSON.stringify(fields));
        
        SESSION_STATUS = 'CANCELLED';
        
        if (CANCEL_URL) {
            const url = new URL(CANCEL_URL)
            window.location.href = url.toString();
            
            return
        }
        
        displayErrorCard('Phiên đã bị hủy', OPERATION === 'VERIFY' ?  'Phiên xác thực đã bị hủy bỏ.' : 'Phiên thanh toán đã bị hủy bỏ.')
    }
    
    function sync() {
        const searchParams = new URLSearchParams(window.location.search);
        
        $.ajax({
            method: 'POST',
            url: `<?= base_url('pay/v1/checkout/sync') ?>?` + searchParams,
            success: (response) => {
                if (response.session_status === 'FULFILLED') {
                    if (SUCCESS_URL) {
                        window.location.href = SUCCESS_URL;
                    } else {
                        if (OPERATION === 'VERIFY') {
                            displaySuccessCard('Xác thực thành công', 'Phiên xác thực đã hoàn tất.')
                        } else {
                            displaySuccessCard('Thanh toán thành công', 'Đơn hàng của bạn đã hoàn tất thanh toán.')
                        }
                    }
                    
                    return;
                }
                
                if (response.session_status === 'FAILED') {
                    handleErrorPayment({ cause: 'SERVER_FAILED' });
                    return;
                }
                
                setTimeout(() => sync(), 3000);
            },
            error: (err) => {
                if (err.status === 404) {
                    handleErrorPayment({ cause: 'INVALID_REQUEST' });
                } else {
                    handleErrorPayment({ cause: 'SERVER_FAILED' });
                }
            }
        });
    }
</script>
<?= $this->endSection() ?>