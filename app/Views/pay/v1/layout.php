<?php

$current_url = str_replace('/index.php', '', current_url(true)->setQuery(http_build_query($_GET))->__toString());

$invalid_session = $GLOBALS['invalid_session'] ?? false;

?>

<html>
<head>
    <title><?= isset($title) ? esc($title) . ' | ' : '' ?>Cổng thanh toán trực tuyến Se<PERSON>ay</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <link href="<?= base_url('dist/css/light.css') ?>" rel="stylesheet">
    <link rel="stylesheet" href="<?= base_url('assets/notyf/notyf.min.css') ?>">
    <link rel="shortcut icon" href="<?= base_url('assets/images/favicon.png') ?>" type="image/x-icon" />
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="<?= base_url('assets/font/bootstrap-icons.css') ?>">
        
    <style>
        @keyframes spinner {
            from {
                opacity: 1;
            }
            to {
                opacity: 0.15;
            }
        }
        
        .animate-spinner {
            animation: spinner 1.2s linear infinite;
            background-color: currentColor;
            height: 8%;
            left: -10%;
            position: absolute;
            top: -3.9%;
            width: 24%;
        }
        
        .animate-spinner:first-child {
            animation-delay: -1.2s;
            transform: rotate(0.0001deg) translate(146%);
        }
        
        .animate-spinner:nth-child(2) {
            animation-delay: -1.1s;
            transform: rotate(30deg) translate(146%);
        }
        
        .animate-spinner:nth-child(3) {
            animation-delay: -1s;
            transform: rotate(60deg) translate(146%);
        }
        
        .animate-spinner:nth-child(4) {
            animation-delay: -0.9s;
            transform: rotate(90deg) translate(146%);
        }
        
        .animate-spinner:nth-child(5) {
            animation-delay: -0.8s;
            transform: rotate(120deg) translate(146%);
        }
        
        .animate-spinner:nth-child(6) {
            animation-delay: -0.7s;
            transform: rotate(150deg) translate(146%);
        }
        
        .animate-spinner:nth-child(7) {
            animation-delay: -0.6s;
            transform: rotate(180deg) translate(146%);
        }
        
        .animate-spinner:nth-child(8) {
            animation-delay: -0.5s;
            transform: rotate(210deg) translate(146%);
        }
        
        .animate-spinner:nth-child(9) {
            animation-delay: -0.4s;
            transform: rotate(240deg) translate(146%);
        }
        
        .animate-spinner:nth-child(10) {
            animation-delay: -0.3s;
            transform: rotate(270deg) translate(146%);
        }
        
        .animate-spinner:nth-child(11) {
            animation-delay: -0.2s;
            transform: rotate(300deg) translate(146%);
        }
        
        .animate-spinner:nth-child(12) {
            animation-delay: -0.1s;
            transform: rotate(330deg) translate(146%);
        }
    </style>
</head>
<body class="bg-muted">
    <div class="d-flex justify-content-center align-items-center min-vh-100 py-5 px-3">
        <div class="d-flex flex-column gap-2 w-100">

            <div class="card mx-auto" style="width: 28rem; max-width: 100%; display: none;" data-state="loading">
                <div class="card-body py-5">
                    <div class="mx-auto text-muted mb-2" style="width: 24px; height: 24px;">
                        <div style="position: relative; top: 50%; left: 50%; width: 100%; height: 100%;">
                            <?php for ($i = 0; $i < 12; $i++): ?>
                                <div class="animate-spinner rounded-lg"></div>
                            <?php endfor; ?>
                        </div>
                    </div>
                    <p class="text-muted fw-bold text-lg title text-center mb-0">Đang tải...</p>
                </div>
            </div>
            
            <div class="card mx-auto" style="width: 28rem; max-width: 100%; display: none;" data-state="pending">
                <div class="card-body py-5">
                    <div class="mx-auto text-primary mb-2" style="width: 24px; height: 24px;">
                        <div style="position: relative; top: 50%; left: 50%; width: 100%; height: 100%;">
                            <?php for ($i = 0; $i < 12; $i++): ?>
                                <div class="animate-spinner rounded-lg"></div>
                            <?php endfor; ?>
                        </div>
                    </div>
                    
                    <p class="text-primary fw-bold text-lg title text-center">Đang xử lý...</p>
                    <p class="text-muted text-center message mb-0">Vui lòng giữ cửa sổ này đến khi quá trình hoàn tất</p>
                </div>
            </div>
            
            <div class="card mx-auto" style="width: 28rem; max-width: 100%; display: none;" data-state="error">
                <div class="card-body py-5">
                    <div class="d-flex justify-content-center mb-2">
                        <svg data-testid="geist-icon" stroke-linejoin="round" viewBox="0 0 16 16" class="text-danger" style="width: 24px; height: 24px;"><path fill-rule="evenodd" clip-rule="evenodd" d="M4.3934 0.292893C4.58094 0.105357 4.83529 0 5.10051 0H10.8995C11.1647 0 11.4191 0.105357 11.6066 0.292893L15.7071 4.3934C15.8946 4.58093 16 4.83529 16 5.10051V10.8995C16 11.1647 15.8946 11.4191 15.7071 11.6066L11.6066 15.7071C11.4191 15.8946 11.1647 16 10.8995 16H5.10051C4.83529 16 4.58094 15.8946 4.3934 15.7071L0.292894 11.6066C0.105357 11.4191 0 11.1647 0 10.8995V5.10051C0 4.83529 0.105357 4.58094 0.292893 4.3934L4.3934 0.292893ZM8.75 3.75V4.5V8L8.75 8.75H7.25V8V4.5V3.75H8.75ZM8 12C8.55229 12 9 11.5523 9 11C9 10.4477 8.55229 10 8 10C7.44772 10 7 10.4477 7 11C7 11.5523 7.44772 12 8 12Z" fill="currentColor"></path></svg>
                    </div>
                    
                    <p class="text-danger fw-bold text-lg title text-center">Lỗi không xác định</p>
                    <p class="text-muted text-center message">Đã có lỗi xảy ra trong quá trình thực hiện</p>
                    
                    <?php if ($fields['cancel_url']): ?>
                        <div class="mt-2 d-flex justify-content-center">
                            <a href="<?= $fields['cancel_url'] ?>" class="btn btn-light">
                                <svg data-testid="geist-icon" stroke-linejoin="round" viewBox="0 0 16 16" style="width: 14px; height: 14px;"><path fill-rule="evenodd" clip-rule="evenodd" d="M6.46966 13.7803L6.99999 14.3107L8.06065 13.25L7.53032 12.7197L3.56065 8.75001H14.25H15V7.25001H14.25H3.56065L7.53032 3.28034L8.06065 2.75001L6.99999 1.68935L6.46966 2.21968L1.39644 7.2929C1.00592 7.68342 1.00592 8.31659 1.39644 8.70711L6.46966 13.7803Z" fill="currentColor"></path></svg>
                                Trở về nhà bán hàng
                            </a>
                        </div>
                    <?php endif ?>
                </div>
            </div>
            
            <div class="card mx-auto" style="width: 28rem; max-width: 100%; display: none;" data-state="success">
                <div class="card-body py-5">
                    <div class="d-flex justify-content-center mb-2">
                        <svg data-testid="geist-icon" stroke-linejoin="round" viewBox="0 0 16 16" class="text-success" style="width: 24px; height: 24px;"><path fill-rule="evenodd" clip-rule="evenodd" d="M16 8C16 12.4183 12.4183 16 8 16C3.58172 16 0 12.4183 0 8C0 3.58172 3.58172 0 8 0C12.4183 0 16 3.58172 16 8ZM11.5303 6.53033L12.0607 6L11 4.93934L10.4697 5.46967L6.5 9.43934L5.53033 8.46967L5 7.93934L3.93934 9L4.46967 9.53033L5.96967 11.0303C6.26256 11.3232 6.73744 11.3232 7.03033 11.0303L11.5303 6.53033Z" fill="currentColor"></path></svg>
                    </div>
                    
                    <p class="text-success fw-bold text-lg title text-center">Thành công</p>
                    <p class="text-muted text-center message">Đã thực hiện thành công</p>
                    
                    <?php if ($fields['cancel_url']): ?>
                        <div class="mt-2 d-flex justify-content-center">
                            <a href="<?= $fields['cancel_url'] ?>" class="btn btn-light">
                                <svg data-testid="geist-icon" stroke-linejoin="round" viewBox="0 0 16 16" style="width: 14px; height: 14px;"><path fill-rule="evenodd" clip-rule="evenodd" d="M6.46966 13.7803L6.99999 14.3107L8.06065 13.25L7.53032 12.7197L3.56065 8.75001H14.25H15V7.25001H14.25H3.56065L7.53032 3.28034L8.06065 2.75001L6.99999 1.68935L6.46966 2.21968L1.39644 7.2929C1.00592 7.68342 1.00592 8.31659 1.39644 8.70711L6.46966 13.7803Z" fill="currentColor"></path></svg>
                                Trở về nhà bán hàng
                            </a>
                        </div>
                    <?php endif ?>
                </div>
            </div>
            
            <?php if ($invalid_session): ?>
                
            <?php else: ?>
                <?= $this->renderSection('content') ?>
            <?php endif ?>
            
            <div class="d-flex justify-content-center align-items-center text-muted w-100 text-sm">
                được cung cấp bởi
                <a href="https://sepay.vn" target="_blank"><img src="https://sepay.vn/assets/img/logo/sepay-blue-154x50.png" style="height: 1rem; filter: grayscale(1);" class="ms-2"></a>
            </div>
        </div>
    </div>
    
    <script src="<?= base_url('assets/js/jquery-3.5.1.js') ?>"></script>
    <script src="<?= base_url('assets/notyf/notyf.min.js') ?>"></script>
    
    <script>
        var notyf = new Notyf({
            position: {
                x: 'right',
                y: 'top',
            },
        })
        
        const SUCCESS_URL = `<?= $fields['success_url'] ?>`;
        const ERROR_URL = `<?= $fields['error_url'] ?>`;
        const CANCEL_URL = `<?= $fields['cancel_url'] ?>`;
        const CURRENT_URL = `<?= $current_url ?>`;
        const TRACE_ID = `<?= $fields['trace_id'] ?? '' ?>`;
        const INVALID_SESSION = <?= $invalid_session ? 'true' : 'false' ?>;
        let SESSION_STATUS = `<?= isset($context) && $context->pgSession ? $context->pgSession->status : ''  ?>`;
        let MUST_BE_CONFIRM_TO_EXIT = false;
        
        $(document).ready(() => {
            // if (!TRACE_ID) {
            //   setTimeout(() => displayCard('init'), 300);
            //   return
            // }
          
            // if (INVALID_SESSION || SESSION_STATUS == '') {
            //     setTimeout(() => displayErrorCard('Phiên không khả dụng', 'Phiên thanh toán/xác thực không hợp lệ hoặc đã hết hạn.'), 300)
            // }
        })
        
        function displayErrorCard(title = null, message = null) {
            if (title) $('.card[data-state="error"] .title').text(title);
            if (message) $('.card[data-state="error"] .message').text(message);
            displayCard('error')
        }
        
        function displaySuccessCard(title, message) {
            $('.card[data-state="success"] .title').text(title);
            $('.card[data-state="success"] .message').text(message);
            displayCard('success')
        }
        
        function displayPendingCard(title, message) {
            $('.card[data-state="pending"] .title').text(title);
            $('.card[data-state="pending"] .message').text(message);
            displayCard('pending')
        }
        
        function displayLoadingCard() {
            displayCard('loading')
        }
        
        function displayCard(state) {
            $(`.card:not([data-state="${state}"])`).hide();
            $(`.card[data-state="${state}"]`).show()
        }
    </script>
    <?= $this->renderSection('scripts') ?>
</body>
</html>

