<?= $this->extend('pay/v1/layout') ?>

<?= $this->section('content') ?>
<div class="card mx-auto" style="width: 28rem; max-width: 100%;" data-state="init">
    <div class="card-body">
        <div>
            <p class="mb-2">Gói đăng ký này sẽ không còn bị hủy bỏ. Gói này sẽ được gia hạn vào <?= date('d/m/Y', strtotime($pg_agreement->next_due_date)) ?>.</p>
            <table class="table table-sm fs-5">
                <tr>
                    <td class="py-2 font-medium">Tên gói đăng ký</td>
                    <td class="py-2 text-right"><?= $pg_agreement->agreement_name ?></td>
                </tr>
                
                <tr>
                    <td class="py-2 font-medium">Số tiền</td>
                    <td class="py-2 text-right"><?= number_format($pg_agreement->amount_per_payment) ?> <?= $pg_agreement->order_currency ?></td>
                </tr>
                <tr>
                    <td class="py-2 font-medium">Chu kỳ thanh toán</td>
                    <td class="py-2 text-right"><?= $pg_agreement->payment_frequency ?></td>
                </tr>
            </table>
            
            <div class="mt-3 d-flex align-items-center justify-content-between">
                <?php if ($fields['cancel_url']): ?>
                    <a href="<?= $fields['cancel_url'] ?>" class="btn btn-ghost">
                        <svg data-testid="geist-icon" stroke-linejoin="round" viewBox="0 0 16 16" style="width: 14px; height: 14px;"><path fill-rule="evenodd" clip-rule="evenodd" d="M6.46966 13.7803L6.99999 14.3107L8.06065 13.25L7.53032 12.7197L3.56065 8.75001H14.25H15V7.25001H14.25H3.56065L7.53032 3.28034L8.06065 2.75001L6.99999 1.68935L6.46966 2.21968L1.39644 7.2929C1.00592 7.68342 1.00592 8.31659 1.39644 8.70711L6.46966 13.7803Z" fill="currentColor"></path></svg>
                        Trở về
                    </a>
                <?php endif ?>
                
                <button class="btn btn-primary" id="btn-reactive">Kích hoạt lại</button>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    $('#btn-reactive').on('click', () => {
      const fields = <?= json_encode($fields) ?>
        
        $.ajax({
            url: `<?= base_url('pay/v1/agreement/reactive') ?>`,
            type: 'POST',
            data: fields,
            success: (res) => {
                if (typeof SUCCESS_URL !== 'undefined' && SUCCESS_URL) {
                    window.location.href = SUCCESS_URL;
                } else {
                    
                }
            },
            error: () => {
                if (typeof ERROR_URL !== 'undefined' && ERROR_URL) {
                    window.location.href = ERROR_URL;
                } else {
                    
                }
            }
        })
    })
</script>
<?= $this->endSection() ?>
