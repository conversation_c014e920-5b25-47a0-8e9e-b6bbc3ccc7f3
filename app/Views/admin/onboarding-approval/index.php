<?= $this->extend('layouts/default') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Header -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Duyệt Onboarding</h1>
    </div>

    <!-- Pending Submissions -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <PERSON><PERSON> sơ chờ duyệt 
                        <span class="badge bg-warning"><?= count($pending_submissions) ?></span>
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (empty($pending_submissions)): ?>
                        <div class="text-center py-4">
                            <i class="bi bi-check-circle text-success" style="font-size: 3rem;"></i>
                            <h5 class="mt-3 text-success"><PERSON><PERSON><PERSON><PERSON> c<PERSON> hồ sơ nào chờ duyệt</h5>
                            <p class="text-muted">T<PERSON><PERSON> c<PERSON> hồ sơ đã được xử lý</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>Công ty</th>
                                        <th>Phương thức</th>
                                        <th>Ngày nộp</th>
                                        <th>Bước hiện tại</th>
                                        <th>Thao tác</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($pending_submissions as $submission): ?>
                                        <tr>
                                            <td>
                                                <strong><?= $submission->full_name ?></strong><br>
                                                <small class="text-muted"><?= $submission->short_name ?></small>
                                            </td>
                                            <td>
                                                <?php
                                                $methodNames = [
                                                    'card' => 'Thẻ tín dụng/ghi nợ',
                                                    'napas_qr' => 'QR NAPAS'
                                                ];
                                                ?>
                                                <span class="badge bg-info">
                                                    <?= $methodNames[$submission->payment_method] ?? $submission->payment_method ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?= date('d/m/Y H:i', strtotime($submission->submitted_at)) ?>
                                            </td>
                                            <td>
                                                <span class="badge bg-secondary">
                                                    Bước <?= $submission->current_step ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary btn-view-details" 
                                                            data-company-id="<?= $submission->company_id ?>"
                                                            data-payment-method="<?= $submission->payment_method ?>">
                                                        <i class="bi bi-eye me-1"></i>Xem chi tiết
                                                    </button>
                                                    <button type="button" class="btn btn-sm btn-success btn-approve" 
                                                            data-company-id="<?= $submission->company_id ?>"
                                                            data-payment-method="<?= $submission->payment_method ?>">
                                                        <i class="bi bi-check me-1"></i>Duyệt
                                                    </button>
                                                    <button type="button" class="btn btn-sm btn-danger btn-reject" 
                                                            data-company-id="<?= $submission->company_id ?>"
                                                            data-payment-method="<?= $submission->payment_method ?>">
                                                        <i class="bi bi-x me-1"></i>Từ chối
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Approved/Rejected Submissions -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Hồ sơ đã xử lý</h6>
                </div>
                <div class="card-body">
                    <?php if (empty($approved_submissions)): ?>
                        <div class="text-center py-4">
                            <i class="bi bi-inbox text-muted" style="font-size: 3rem;"></i>
                            <h5 class="mt-3 text-muted">Chưa có hồ sơ nào được xử lý</h5>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>Công ty</th>
                                        <th>Phương thức</th>
                                        <th>Trạng thái</th>
                                        <th>Ngày xử lý</th>
                                        <th>Thao tác</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($approved_submissions as $submission): ?>
                                        <tr>
                                            <td>
                                                <strong><?= $submission->full_name ?></strong><br>
                                                <small class="text-muted"><?= $submission->short_name ?></small>
                                            </td>
                                            <td>
                                                <?php
                                                $methodNames = [
                                                    'card' => 'Thẻ tín dụng/ghi nợ',
                                                    'napas_qr' => 'QR NAPAS'
                                                ];
                                                ?>
                                                <span class="badge bg-info">
                                                    <?= $methodNames[$submission->payment_method] ?? $submission->payment_method ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php if ($submission->status === 'approved'): ?>
                                                    <span class="badge bg-success">Đã duyệt</span>
                                                <?php else: ?>
                                                    <span class="badge bg-danger">Đã từ chối</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?= date('d/m/Y H:i', strtotime($submission->reviewed_at)) ?>
                                            </td>
                                            <td>
                                                <button type="button" class="btn btn-sm btn-outline-primary btn-view-details" 
                                                        data-company-id="<?= $submission->company_id ?>"
                                                        data-payment-method="<?= $submission->payment_method ?>">
                                                    <i class="bi bi-eye me-1"></i>Xem chi tiết
                                                </button>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- View Details Modal -->
<div class="modal fade" id="viewDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Chi tiết hồ sơ Onboarding</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="detailsModalBody">
                <!-- Content will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
            </div>
        </div>
    </div>
</div>

<!-- Reject Modal -->
<div class="modal fade" id="rejectModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Từ chối hồ sơ</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="rejectForm">
                <div class="modal-body">
                    <input type="hidden" name="company_id" id="rejectCompanyId">
                    <input type="hidden" name="payment_method" id="rejectPaymentMethod">
                    
                    <div class="mb-3">
                        <label class="form-label">Lý do từ chối <span class="text-danger">*</span></label>
                        <textarea class="form-control" name="rejection_reason" rows="4" 
                                  placeholder="Nhập lý do từ chối (tối thiểu 10 ký tự)" required></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                    <button type="submit" class="btn btn-danger">Từ chối</button>
                </div>
            </form>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // View Details
    $('.btn-view-details').on('click', function() {
        const companyId = $(this).data('company-id');
        const paymentMethod = $(this).data('payment-method');
        
        loadSubmissionDetails(companyId, paymentMethod);
    });

    // Approve
    $('.btn-approve').on('click', function() {
        const companyId = $(this).data('company-id');
        const paymentMethod = $(this).data('payment-method');
        const $button = $(this);
        
        if (confirm('Bạn có chắc muốn duyệt hồ sơ này?')) {
            $button.prop('disabled', true);
            
            $.ajax({
                url: '<?= base_url('test/approve') ?>',
                method: 'POST',
                data: {
                    '<?= csrf_token() ?>': '<?= csrf_hash() ?>',
                    company_id: companyId,
                    payment_method: paymentMethod
                },
                success: function(response) {
                    if (response.status) {
                        notyf.success(response.message);
                        setTimeout(() => location.reload(), 1500);
                    } else {
                        notyf.error(response.message);
                    }
                },
                error: function() {
                    notyf.error('Có lỗi xảy ra, vui lòng thử lại');
                },
                complete: function() {
                    $button.prop('disabled', false);
                }
            });
        }
    });

    // Reject
    $('.btn-reject').on('click', function() {
        const companyId = $(this).data('company-id');
        const paymentMethod = $(this).data('payment-method');
        
        $('#rejectCompanyId').val(companyId);
        $('#rejectPaymentMethod').val(paymentMethod);
        $('#rejectModal').modal('show');
    });

    // Reject Form Submit
    $('#rejectForm').on('submit', function(e) {
        e.preventDefault();
        
        const $form = $(this);
        const $submitBtn = $form.find('button[type="submit"]');
        
        $submitBtn.prop('disabled', true);
        
        $.ajax({
            url: '<?= base_url('test/reject') ?>',
            method: 'POST',
            data: {
                '<?= csrf_token() ?>': '<?= csrf_hash() ?>',
                company_id: $('#rejectCompanyId').val(),
                payment_method: $('#rejectPaymentMethod').val(),
                rejection_reason: $('textarea[name="rejection_reason"]').val()
            },
            success: function(response) {
                if (response.status) {
                    notyf.success(response.message);
                    $('#rejectModal').modal('hide');
                    $form[0].reset();
                    setTimeout(() => location.reload(), 1500);
                } else {
                    notyf.error(response.message);
                }
            },
            error: function() {
                notyf.error('Có lỗi xảy ra, vui lòng thử lại');
            },
            complete: function() {
                $submitBtn.prop('disabled', false);
            }
        });
    });

    function loadSubmissionDetails(companyId, paymentMethod) {
        $.ajax({
            url: '<?= base_url('test/getSubmissionDetails') ?>',
            method: 'GET',
            data: { 
                company_id: companyId,
                payment_method: paymentMethod
            },
            success: function(response) {
                if (response.status) {
                    renderSubmissionDetails(response);
                    $('#viewDetailsModal').modal('show');
                } else {
                    notyf.error(response.message);
                }
            },
            error: function() {
                notyf.error('Có lỗi xảy ra, vui lòng thử lại');
            }
        });
    }

    function renderSubmissionDetails(response) {
        const { submission, company, onboard_data } = response;
        const container = $('#detailsModalBody');
        
        let html = `
            <div class="row">
                <div class="col-md-6">
                    <h6>Thông tin công ty</h6>
                    <table class="table table-sm">
                        <tr><td>Tên đầy đủ:</td><td><strong>${company.full_name}</strong></td></tr>
                        <tr><td>Tên viết tắt:</td><td>${company.short_name}</td></tr>
                        <tr><td>ID:</td><td>${company.id}</td></tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <h6>Thông tin submission</h6>
                    <table class="table table-sm">
                        <tr><td>Phương thức:</td><td>${submission.payment_method}</td></tr>
                        <tr><td>Trạng thái:</td><td><span class="badge bg-${submission.status === 'submitted' ? 'warning' : (submission.status === 'approved' ? 'success' : 'danger')}">${submission.status}</span></td></tr>
                        <tr><td>Bước hiện tại:</td><td>${submission.current_step}</td></tr>
                        <tr><td>Ngày nộp:</td><td>${submission.submitted_at}</td></tr>
                    </table>
                </div>
            </div>
            
            <div class="row mt-3">
                <div class="col-12">
                    <h6>Dữ liệu Onboarding</h6>
                    <div class="border rounded p-3 bg-light">
                        <pre class="mb-0">${JSON.stringify(onboard_data, null, 2)}</pre>
                    </div>
                </div>
            </div>
        `;
        
        container.html(html);
    }
});
</script>
<?= $this->endSection() ?>
