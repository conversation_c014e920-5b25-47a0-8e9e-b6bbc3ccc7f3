<?php

use Config\OutputDevices;

$uri = service('uri');
$segment1 = $uri->getSegment(1);
 
$segments = $uri->getSegments();

if($user_details->theme == 'auto') {
    if(date('H') > 5 && date('H') < 17)
      $theme_mode = 'light';
    else
      $theme_mode = 'dark';
} else 
    $theme_mode = $user_details->theme;
   
/*has_permission('Transactions', 'can_view_all')
has_permission('BankAccount', 'can_view_all')
has_permission('NotificationTelegram', 'can_view_all')
has_permission('Webhooks', 'can_view_all')
*/
$perTransactions = has_permission('Transactions', 'can_view_all');
$perBankAccount = has_permission('BankAccount', 'can_view_all');
$perNotificationTelegram = has_permission('NotificationTelegram', 'can_view_all');
$perNotificationLarkMessenger = has_permission('NotificationLarkMessenger', 'can_view_all');
$perNotificationGoogleSheets = has_permission('NotificationGoogleSheets', 'can_view_all');
$configGoogleSheets = config(\Config\GoogleSheets::class);

$perWebhooks = has_permission('Webhooks', 'can_view_all');


$perStatisticsCashFlow = has_permission('StatisticsCashFlow', 'can_view_all');
$perStatisticsTransaction = has_permission('StatisticsTransaction', 'can_view_all');
$perReportsAccountBalance = has_permission('ReportsAccountBalance', 'can_view_all');
//$perReportsSubAccountIncome = has_permission('ReportsSubAccountIncome', 'can_view_all');

$perWebhooksLog = has_permission('WebhooksLog', 'can_view_all');
$perReportsSubBankAccountIncome = has_permission('ReportsSubBankAccountIncome', 'can_view_all');
$bankSubAccount = get_configuration('BankSubAccount');
$bankBonus = in_array($company_details->role, ['Admin', 'SuperAdmin']) ?? false;
$configBankBonus = config(\Config\BankBonus::class);

$gaConfig = config(\Config\GoogleAnalytics::class);

//get config
$configOutputDevice  = config(OutputDevices::class);
$config_output_device = $configOutputDevice->enabled ?? false;


$configGoHighLevel = config(GoHighLevel::class);
$config_visibility_ghl = $configGoHighLevel->visibility ?? false;

$billingConfig = config(\Config\Billing::class);
$creditUsagable = (property_exists($billingConfig, 'creditUsagable') && $billingConfig->creditUsagable) || is_admin();

?>
<!doctype html>
<html lang="vi">

<head>
    <!-- Required meta tags -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

    <meta name="color-scheme" content="light dark">
    <meta name="format-detection" content="telephone=no">

    <meta name="csrf-token" content="<?= csrf_hash() ?>">
    <!-- Bootstrap CSS -->    
    <link rel="stylesheet" href="<?php echo base_url();?>/assets/font/bootstrap-icons.css">

    <?php if($theme_mode == 'dark') { ?>
    <link href="<?php echo base_url();?>/dist/css/dark.css" rel="stylesheet">
    <?php } else { ?>
    <link href="<?php echo base_url();?>/dist/css/light.css" rel="stylesheet">
    <?php } ?>
    <link rel="stylesheet" href="<?php echo base_url();?>/assets/notyf/notyf.min.css">

    <link rel="shortcut icon" href="<?php echo base_url();?>/assets/images/favicon.png" type="image/x-icon" />
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500&display=swap" rel="stylesheet">
    <!--contact-box-css-->
    <link rel="stylesheet" href="<?php echo base_url();?>/assets/css/contact-box.css">
    <!--/contact-box-css-->
    <title><?php if(isset($page_title)) echo esc($page_title) . ' - ';?>SePay</title>
    <style>
        .content {
            padding: 0.5rem 0.5rem 1.5rem;
        }
       
        .thumb-xl {
            height: 120px;
            width: 120px;
        }

        body[data-theme=dark] .sidebar-cta-content {
            background: unset !important;
        }
        .sidebar-cta-content {
            padding: 1rem;
        }

        .fs-7 {
            font-size: .725rem!important;
        }

       

        body[data-theme="dark"] .ts-control{
          color: #bfc1c6;
        }

        body[data-theme="dark"] .ts-dropdown, .ts-dropdown.form-control, .ts-dropdown.form-select {
          background: #a9acb3;
        }
 
    </style>

	<?php if ($gaConfig->tagId): ?>
		<!-- Google tag (gtag.js) -->
		<script async src="https://www.googletagmanager.com/gtag/js?id=<?= $gaConfig->tagId ?>"></script>
		<script>
			window.dataLayer = window.dataLayer || [];
			function gtag(){dataLayer.push(arguments);}
			gtag('js', new Date());

			gtag('config', '<?= $gaConfig->tagId ?>');
		</script>
	<?php endif; ?>
</head>

<body  data-theme="<?php if($theme_mode == 'dark') echo 'dark'; else echo 'colored';?>" data-layout="fluid" data-sidebar-position="left" data-sidebar-behavior="<?php echo esc($user_details->sidebar_behavior);?>">
    <div class="d-none" id="blocked-noti-toast"> 
        <div style="background-color: #facc15; color: #000" class="d-flex align-items-center px-3 py-1">
            <div style="width: 24px; height: 24px; color: #facc15; background-color: #000; flex: none;" class="me-2 d-flex align-items-center justify-content-center rounded-circle"><i class="bi bi-bell-slash-fill fs-5"></i></div>
            <p class="mb-0 text-sm fw-bold">Hãy bật thông báo để luôn nhận được các cập nhật và chương trình ưu đãi từ SePay</p>
            <button type="button" class="btn rounded-pill mx-2 btn-sm btn-secondary" style="background-color: #000; color: #fff; flex: none;" data-bs-toggle="modal" data-bs-target="#turn-on-noti-guide-modal">Bật lại</button>
            <i class="ms-auto cursor-pointer btn-close"></i>
        </div> 
    </div>

    <div class="wrapper">
        <nav id="sidebar" class="sidebar <?php if($user_details->sidebar_toggle == 1 && !is_mobile()) echo 'collapsed';?>">
            <div class="sidebar-content js-simplebar" data-simplebar="init">
                <a class="sidebar-brand" href="<?php echo base_url();?>">
                    
                    <img src="<?= base_url('assets/images/logo/autopay-30x30-white-icon.png');?>">
                    <span class="align-middle me-3">SePay</span>
                    <p style="font-size:10px;" class="text-center mb-0"></p>
                </a>
                <ul class="sidebar-nav">
                    <li class="sidebar-item <?php if($segment1 == '') echo 'active';?>">
                        
                        <a href="<?php echo base_url();?>" class="sidebar-link "><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" class="bi bi-speedometer" viewBox="0 0 16 16">
  <path d="M8 2a.5.5 0 0 1 .5.5V4a.5.5 0 0 1-1 0V2.5A.5.5 0 0 1 8 2zM3.732 3.732a.5.5 0 0 1 .707 0l.915.914a.5.5 0 1 1-.708.708l-.914-.915a.5.5 0 0 1 0-.707zM2 8a.5.5 0 0 1 .5-.5h1.586a.5.5 0 0 1 0 1H2.5A.5.5 0 0 1 2 8zm9.5 0a.5.5 0 0 1 .5-.5h1.5a.5.5 0 0 1 0 1H12a.5.5 0 0 1-.5-.5zm.754-4.246a.389.389 0 0 0-.527-.02L7.547 7.31A.91.91 0 1 0 8.85 8.569l3.434-4.297a.389.389 0 0 0-.029-.518z"/>
  <path fill-rule="evenodd" d="M6.664 15.889A8 8 0 1 1 9.336.11a8 8 0 0 1-2.672 15.78zm-4.665-4.283A11.945 11.945 0 0 1 8 10c2.186 0 4.236.585 6.001 1.606a7 7 0 1 0-12.002 0z"/>
</svg> <span class="align-middle">Tổng quan</span></a>
                    </li>
                    
                    <?php
                    
                    if($perTransactions) { ?>
                    <li class="sidebar-item  <?php if ($segment1 == 'transactions') {
                        echo 'active';
                    }?>">
                    <a href="<?php echo base_url('transactions');?>" class="sidebar-link"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" class="bi bi-arrow-left-right" viewBox="0 0 16 16">
  <path fill-rule="evenodd" d="M1 11.5a.5.5 0 0 0 .5.5h11.793l-3.147 3.146a.5.5 0 0 0 .708.708l4-4a.5.5 0 0 0 0-.708l-4-4a.5.5 0 0 0-.708.708L13.293 11H1.5a.5.5 0 0 0-.5.5zm14-7a.5.5 0 0 1-.5.5H2.707l3.147 3.146a.5.5 0 1 1-.708.708l-4-4a.5.5 0 0 1 0-.708l4-4a.5.5 0 1 1 .708.708L2.707 4H14.5a.5.5 0 0 1 .5.5z"/>
</svg> <span class="align-middle">Giao dịch</span></a>
                    </li>
                    <?php } ?>
                    
                    <?php if($perBankAccount) { ?>
                    <li class="sidebar-item <?php if ($segment1 == 'bankaccount' || $segment1 == 'banksubaccount' || $segment1 == 'klb') {
                        echo 'active';
                    }?>">
                    <a href="<?php echo base_url('bankaccount');?>" class="sidebar-link "><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" class="bi bi-bank" viewBox="0 0 16 16">
  <path d="m8 0 6.61 3h.89a.5.5 0 0 1 .5.5v2a.5.5 0 0 1-.5.5H15v7a.5.5 0 0 1 .485.38l.5 2a.498.498 0 0 1-.485.62H.5a.498.498 0 0 1-.485-.62l.5-2A.501.501 0 0 1 1 13V6H.5a.5.5 0 0 1-.5-.5v-2A.5.5 0 0 1 .5 3h.89L8 0ZM3.777 3h8.447L8 1 3.777 3ZM2 6v7h1V6H2Zm2 0v7h2.5V6H4Zm3.5 0v7h1V6h-1Zm2 0v7H12V6H9.5ZM13 6v7h1V6h-1Zm2-1V4H1v1h14Zm-.39 9H1.39l-.25 1h13.72l-.25-1Z"/>
</svg> <span class="align-middle">Ngân hàng</span></a>
                    </li>
                        <?php } ?>


                    <li class="sidebar-header">Cổng thanh toán</li>

                        <li class="sidebar-item <?php if ($segment1 == 'createqr') {
                        echo 'active';
                    }?>" >
                        <a href="<?php echo base_url('createqr');?>" class="sidebar-link">
                        <svg fill="currentColor" width="18px" height="18px" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" data-name="Layer 1"><path d="M8,21H4a1,1,0,0,1-1-1V16a1,1,0,0,0-2,0v4a3,3,0,0,0,3,3H8a1,1,0,0,0,0-2Zm14-6a1,1,0,0,0-1,1v4a1,1,0,0,1-1,1H16a1,1,0,0,0,0,2h4a3,3,0,0,0,3-3V16A1,1,0,0,0,22,15ZM20,1H16a1,1,0,0,0,0,2h4a1,1,0,0,1,1,1V8a1,1,0,0,0,2,0V4A3,3,0,0,0,20,1ZM2,9A1,1,0,0,0,3,8V4A1,1,0,0,1,4,3H8A1,1,0,0,0,8,1H4A3,3,0,0,0,1,4V8A1,1,0,0,0,2,9Zm8-4H6A1,1,0,0,0,5,6v4a1,1,0,0,0,1,1h4a1,1,0,0,0,1-1V6A1,1,0,0,0,10,5ZM9,9H7V7H9Zm5,2h4a1,1,0,0,0,1-1V6a1,1,0,0,0-1-1H14a1,1,0,0,0-1,1v4A1,1,0,0,0,14,11Zm1-4h2V9H15Zm-5,6H6a1,1,0,0,0-1,1v4a1,1,0,0,0,1,1h4a1,1,0,0,0,1-1V14A1,1,0,0,0,10,13ZM9,17H7V15H9Zm5-1a1,1,0,0,0,1-1,1,1,0,0,0,0-2H14a1,1,0,0,0-1,1v1A1,1,0,0,0,14,16Zm4-3a1,1,0,0,0-1,1v3a1,1,0,0,0,0,2h1a1,1,0,0,0,1-1V14A1,1,0,0,0,18,13Zm-4,4a1,1,0,1,0,1,1A1,1,0,0,0,14,17Z"/></svg> 
                        <span class="align-middle">Tạo QR</span> <span class="ms-auto align-middle"></span></a>
                    </li>

                    <li class="sidebar-item <?php if($segment1 == 'branches') {
                        echo 'active';
                    }?>">
                        <a href="<?php echo base_url('branches');?>" class="sidebar-link d-flex align-items-center">
                          <i class="bi bi-geo-alt lh-1" style="font-size: 18px;"></i>
                        <span class="align-middle">Chi nhánh</span>
                        </a>
                    </li>

                    <li class="sidebar-item <?php if($segment1 == 'paymentmethods') {
                        echo 'active';
                    }?>">
                        <a href="<?php echo base_url('paymentmethods');?>" class="sidebar-link d-flex align-items-center">
                          <i class="bi bi-credit-card lh-1" style="font-size: 18px;"></i>
                        <span class="align-middle">Phương thức thanh toán</span>
                        </a>
                    </li>

                    <li class="sidebar-item <?php if($segment1 == 'orders') {
                        echo 'active';
                    }?>">
                        <a href="<?php echo base_url('orders');?>" class="sidebar-link">
                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" class="bi bi-cart-check" viewBox="0 0 16 16">
                          <path d="M11.354 6.354a.5.5 0 0 0-.708-.708L8 8.293 6.854 7.146a.5.5 0 1 0-.708.708l1.5 1.5a.5.5 0 0 0 .708 0z"/>
                          <path d="M.5 1a.5.5 0 0 0 0 1h1.11l.401 1.607 1.498 7.985A.5.5 0 0 0 4 12h1a2 2 0 1 0 0 4 2 2 0 0 0 0-4h7a2 2 0 1 0 0 4 2 2 0 0 0 0-4h1a.5.5 0 0 0 .491-.408l1.5-8A.5.5 0 0 0 14.5 3H2.89l-.405-1.621A.5.5 0 0 0 2 1zm3.915 10L3.102 4h10.796l-1.313 7zM6 14a1 1 0 1 1-2 0 1 1 0 0 1 2 0m7 0a1 1 0 1 1-2 0 1 1 0 0 1 2 0"/>
                        </svg>
                        <span class="align-middle">Đơn hàng</span>
                        </a>
                    </li>

                    <li class="sidebar-header">Kinh doanh & Ưu đãi</li>

                    <li class="sidebar-item">
                        <a href="https://sepay.vn/khuyen-mai" target="_blank" class="sidebar-link">
                        <svg fill="currentColor" height="18px" width="18px" version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" 
                            viewBox="0 0 512 512" xml:space="preserve">
                          <g>
                            <g>
                              <path d="M33.408,233.76v228.157c0,27.617,22.466,50.083,50.083,50.083h139.12V233.76H33.408z"/>
                            </g>
                          </g>
                          <g>
                            <g>
                              <path d="M289.389,233.76V512h139.12c27.617,0,50.083-22.466,50.083-50.083V233.76H289.389z"/>
                            </g>
                          </g>
                          <g>
                            <g>
                              <path d="M478.592,100.205H370.015c12.655-8.15,24.542-17.707,35.448-28.612c6.521-6.515,6.521-17.086,0-23.607
                                c-6.511-6.521-17.086-6.521-23.607,0c-15.832,15.827-34.098,28.31-53.941,37.099c7.368-8.884,11.558-20.151,11.558-31.963V50.15
                                c0-16.293-7.581-31.101-20.798-40.633c-13.211-9.516-29.656-12.042-45.122-6.88c-6.639,2.21-12.565,5.717-17.552,10.174
                                c-4.987-4.457-10.915-7.963-17.552-10.174c-15.477-5.179-31.911-2.647-45.122,6.88c-13.216,9.531-20.798,24.34-20.798,40.633
                                v2.973c0,11.812,4.189,23.078,11.558,31.964c-19.843-8.789-38.109-21.272-53.941-37.099c-6.521-6.521-17.096-6.521-23.607,0
                                c-6.521,6.521-6.521,17.091,0,23.607c10.906,10.905,22.792,20.462,35.448,28.612H33.408c-18.441,0-33.389,14.948-33.389,33.389
                                v50.083c0,9.22,7.475,16.694,16.694,16.694h478.572c9.22,0,16.694-7.475,16.694-16.694v-50.083
                                C511.98,115.153,497.032,100.205,478.592,100.205z M239.305,85.009l-26.205-18.167c-4.5-3.12-7.184-8.249-7.184-13.722v-2.972h0
                                c0-5.435,2.528-10.368,6.934-13.542c2.901-2.092,6.271-3.174,9.706-3.174c1.783,0,3.576,0.294,5.337,0.875
                                c6.826,2.277,11.412,8.641,11.412,15.841V85.009z M306.083,53.122c0,5.472-2.684,10.602-7.179,13.716l-26.21,18.172V50.149
                                c0-7.2,4.588-13.564,11.412-15.841c1.761-0.581,3.554-0.875,5.337-0.875c3.435,0,6.804,1.082,9.706,3.174
                                c4.407,3.174,6.934,8.108,6.934,13.542V53.122z"/>
                            </g>
                          </g>
                          </svg>
                          <span class="align-middle">Khuyến mãi</span></a>
                    </li>

                      <?php if (in_array($company_details->role, ['Admin', 'SuperAdmin'])): ?>
                          <li class="sidebar-item <?= $segment1 == 'referral' ? 'active' : '' ?>">
                              <a href="<?= base_url('referral') ?>" class="sidebar-link">
                              <svg width="18px" height="18px" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg" fill="currentColor" class="bi bi-person-plus">
                                <path d="M6 8a3 3 0 1 0 0-6 3 3 0 0 0 0 6zm2-3a2 2 0 1 1-4 0 2 2 0 0 1 4 0zm4 8c0 1-1 1-1 1H1s-1 0-1-1 1-4 6-4 6 3 6 4zm-1-.004c-.001-.246-.154-.986-.832-1.664C9.516 10.68 8.289 10 6 10c-2.29 0-3.516.68-4.168 1.332-.678.678-.83 1.418-.832 1.664h10z"/>
                                <path fill-rule="evenodd" d="M13.5 5a.5.5 0 0 1 .5.5V7h1.5a.5.5 0 0 1 0 1H14v1.5a.5.5 0 0 1-1 0V8h-1.5a.5.5 0 0 1 0-1H13V5.5a.5.5 0 0 1 .5-.5z"/>
                              </svg>
                                  <span class="align-middle">Giới thiệu bạn bè</span>
                                  <span class="ms-auto align-middle"><span class="badge bg-danger">MỚI</span></span>
                              </a>
                          </li>
                      <?php endif ?>

                    <li class="sidebar-item">
                        <a href="https://sepay.vn/affiliate.html" target="_blank" class="sidebar-link">
                          <svg height="18px" width="18px" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" 
                            viewBox="0 0 235.517 235.517" xml:space="preserve">
                          <g>
                            <path style="fill:currentColor;" d="M118.1,235.517c7.898,0,14.31-6.032,14.31-13.483c0-7.441,0-13.473,0-13.473
                              c39.069-3.579,64.932-24.215,64.932-57.785v-0.549c0-34.119-22.012-49.8-65.758-59.977V58.334c6.298,1.539,12.82,3.72,19.194,6.549
                              c10.258,4.547,22.724,1.697,28.952-8.485c6.233-10.176,2.866-24.47-8.681-29.654c-11.498-5.156-24.117-8.708-38.095-10.236V8.251
                              c0-4.552-6.402-8.251-14.305-8.251c-7.903,0-14.31,3.514-14.31,7.832c0,4.335,0,7.843,0,7.843
                              c-42.104,3.03-65.764,25.591-65.764,58.057v0.555c0,34.114,22.561,49.256,66.862,59.427v33.021
                              c-10.628-1.713-21.033-5.243-31.623-10.65c-11.281-5.755-25.101-3.72-31.938,6.385c-6.842,10.1-4.079,24.449,7.294,30.029
                              c16.709,8.208,35.593,13.57,54.614,15.518v13.755C103.79,229.36,110.197,235.517,118.1,235.517z M131.301,138.12
                              c14.316,4.123,18.438,8.257,18.438,15.681v0.555c0,7.979-5.776,12.651-18.438,14.033V138.12z M86.999,70.153v-0.549
                              c0-7.152,5.232-12.657,18.71-13.755v29.719C90.856,81.439,86.999,77.305,86.999,70.153z"/>
                          </g>
                          </svg>
                          <span class="align-middle">Affiliate</span></a>
                    </li>

                    <?php if($bankBonus && (!$configBankBonus->visibility || is_admin())) {?>
                      <li class="sidebar-item <?php if($segment1 == 'bankbonus') {
                        echo 'active';
                    }?>">
                        <a href="<?= base_url('bankbonus')?>" class="sidebar-link">
                        <svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" width="18px" height="18px" fill="currentColor"
                          viewBox="0 0 512 512" style="enable-background:new 0 0 512 512;" xml:space="preserve">
                        <g>
                          <path d="M369,512c61.86,0,112-50.14,112-112s-50.14-112-112-112s-112,50.14-112,112S307.14,512,369,512z M385,352v32h32
                            c8.84,0,16,7.16,16,16s-7.16,16-16,16h-32v32c0,8.84-7.16,16-16,16s-16-7.16-16-16v-32h-32c-8.84,0-16-7.16-16-16s7.16-16,16-16h32
                            v-32c0-8.84,7.16-16,16-16S385,343.16,385,352z"/>
                          <path d="M33,32C33,14.33,47.33,0,65,0h320c17.67,0,32,14.33,32,32v208c0,8.84-7.16,16-16,16s-16-7.16-16-16V32H65v448h96v-80
                            c0-8.84,7.16-16,16-16h48v128H65c-17.67,0-32-14.33-32-32V32z"/>
                          <path d="M113,64c-8.84,0-16,7.16-16,16v32c0,8.84,7.16,16,16,16h32c8.84,0,16-7.16,16-16V80c0-8.84-7.16-16-16-16H113z M209,64
                            c-8.84,0-16,7.16-16,16v32c0,8.84,7.16,16,16,16h32c8.84,0,16-7.16,16-16V80c0-8.84-7.16-16-16-16H209z M305,64
                            c-8.84,0-16,7.16-16,16v32c0,8.84,7.16,16,16,16h32c8.84,0,16-7.16,16-16V80c0-8.84-7.16-16-16-16H305z M113,160
                            c-8.84,0-16,7.16-16,16v32c0,8.84,7.16,16,16,16h32c8.84,0,16-7.16,16-16v-32c0-8.84-7.16-16-16-16H113z M209,160
                            c-8.84,0-16,7.16-16,16v32c0,8.84,7.16,16,16,16h32c8.84,0,16-7.16,16-16v-32c0-8.84-7.16-16-16-16H209z M305,160
                            c-8.84,0-16,7.16-16,16v32c0,8.84,7.16,16,16,16h32c8.84,0,16-7.16,16-16v-32c0-8.84-7.16-16-16-16H305z M113,256
                            c-8.84,0-16,7.16-16,16v32c0,8.84,7.16,16,16,16h32c8.84,0,16-7.16,16-16v-32c0-8.84-7.16-16-16-16H113z M209,256
                            c-8.84,0-16,7.16-16,16v32c0,8.84,7.16,16,16,16h32c8.84,0,16-7.16,16-16v-32c0-8.84-7.16-16-16-16H209z"/>
                        </g>
                        </svg>
                        <span class="align-middle">Mở mới TK nhận giao dịch</span></a>
                      </li>
                    <?php }?>

                        <?php if($perNotificationTelegram || $perNotificationLarkMessenger ||$perNotificationGoogleSheets || has_permission('NotificationViber', 'can_view_all') || mobile_feature_visibility() || $config_output_device || $perWebhooks) { ?>
                          <li class="sidebar-header">Tích hợp & Thông báo</li>

                          <?php if($perNotificationTelegram) { ?>
                    <li class="sidebar-item <?php if($segment1 == 'notificationtelegram') {
                        echo 'active';
                    }?>">
                    <a href="<?php echo base_url('notificationtelegram');?>" class="sidebar-link "><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" class="bi bi-telegram" viewBox="0 0 16 16">
  <path d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zM8.287 5.906c-.778.324-2.334.994-4.666 2.01-.378.15-.577.298-.595.442-.03.243.275.339.69.47l.175.055c.408.133.958.288 1.243.294.26.006.549-.1.868-.32 2.179-1.471 3.304-2.214 3.374-2.23.05-.012.12-.026.166.016.047.041.042.12.037.141-.03.129-1.227 1.241-1.846 1.817-.193.18-.33.307-.358.336a8.154 8.154 0 0 1-.188.186c-.38.366-.664.64.015 1.088.327.216.589.393.85.571.284.194.568.387.936.629.093.06.183.125.27.187.331.236.63.448.997.414.214-.02.435-.22.547-.82.265-1.417.786-4.486.906-5.751a1.426 1.426 0 0 0-.013-.315.337.337 0 0 0-.114-.217.526.526 0 0 0-.31-.093c-.3.005-.763.166-2.984 1.09z"/>
</svg> <span class="align-middle">Tích hợp Telegram</span></a>
                    </li>
                    <?php } ?>

                    <?php if (has_permission('NotificationViber', 'can_view_all') && (new \App\Features\Viber\ViberFeature)->enabled): ?>
                        <li class="sidebar-item <?= $segment1 == 'notificationviber' ? 'active' : '' ?>">
                            <a href="<?php echo base_url('notificationviber');?>" class="sidebar-link d-flex align-items-center">
                                <i class="fab fa-viber" style="font-size: 18px;"></i>
                                <span class="align-middle">Tích hợp Viber</span>
                            </a>
                        </li>
                    <?php endif; ?>

                    <?php if($perNotificationLarkMessenger) { ?>
                    <li class="sidebar-item <?php if($segment1 == 'notificationlarkmessenger') {
                        echo 'active';
                    }?>">
                    <a href="<?php echo base_url('notificationlarkmessenger');?>" class="sidebar-link "><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" class="bi bi-send" viewBox="0 0 16 16">
  <path d="M15.854.146a.5.5 0 0 1 .11.54l-5.819 14.547a.75.75 0 0 1-1.329.124l-3.178-4.995L.643 7.184a.75.75 0 0 1 .124-1.33L15.314.037a.5.5 0 0 1 .54.11ZM6.636 10.07l2.761 4.338L14.13 2.576 6.636 10.07Zm6.787-8.201L1.591 6.602l4.339 2.76 7.494-7.493Z"/>
</svg> <span class="align-middle">Tích hợp Lark Messenger</span></a>
                    </li>
                    <?php } ?>

                    <?php if($perNotificationGoogleSheets && ($configGoogleSheets->visibility || is_admin())) { ?>
                    <li class="sidebar-item <?php if($segment1 == 'googlesheets') {
                        echo 'active';
                    }?>">
                    <a href="<?php echo base_url('googlesheets');?>" class="sidebar-link ">
                    <svg fill="currentColor" xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 50 50" width="20px" height="20px"><path d="M 28.90625 1.96875 C 28.863281 1.976563 28.820313 1.988281 28.78125 2 L 11.5 2 C 9.585938 2 8 3.558594 8 5.46875 L 8 43.90625 C 8 46.160156 9.867188 48 12.125 48 L 37.875 48 C 40.132813 48 42 46.160156 42 43.90625 L 42 15.1875 C 42.027344 15.054688 42.027344 14.914063 42 14.78125 L 42 14.5 C 42.007813 14.234375 41.90625 13.972656 41.71875 13.78125 L 30.21875 2.28125 C 30.027344 2.09375 29.765625 1.992188 29.5 2 L 29.1875 2 C 29.097656 1.976563 29 1.964844 28.90625 1.96875 Z M 11.5 4 L 28 4 L 28 12.34375 C 28 14.355469 29.644531 16 31.65625 16 L 40 16 L 40 43.90625 C 40 45.074219 39.054688 46 37.875 46 L 12.125 46 C 10.945313 46 10 45.074219 10 43.90625 L 10 5.46875 C 10 4.644531 10.660156 4 11.5 4 Z M 30 4.9375 L 39.0625 14 L 31.65625 14 C 30.722656 14 30 13.277344 30 12.34375 Z M 17 24 L 17 38 L 33 38 L 33 24 Z M 19 26 L 24 26 L 24 28 L 19 28 Z M 26 26 L 31 26 L 31 28 L 26 28 Z M 19 30 L 24 30 L 24 32 L 19 32 Z M 26 30 L 31 30 L 31 32 L 26 32 Z M 19 34 L 24 34 L 24 36 L 19 36 Z M 26 34 L 31 34 L 31 36 L 26 36 Z"/></svg><span class="align-middle">Tích hợp Google Sheets</span></a>
                    </li>
                    <?php } ?>
                    <?php } ?>
                    
                    <?php if (mobile_feature_visibility() && in_array($company_details->role, ['SuperAdmin', 'Admin'])): ?>
                    <li class="sidebar-item <?php if($segment1 == 'mobileapp') {
                        echo 'active';
                    }?>">
                    <a href="<?php echo base_url('mobileapp'); ?>" class="sidebar-link"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" class="bi bi-phone-vibrate-fill" viewBox="0 0 16 16">
                      <path d="M4 4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2zm5 7a1 1 0 1 0-2 0 1 1 0 0 0 2 0M1.807 4.734a.5.5 0 1 0-.884-.468A8 8 0 0 0 0 8c0 1.347.334 2.618.923 3.734a.5.5 0 1 0 .884-.468A7 7 0 0 1 1 8c0-1.18.292-2.292.807-3.266m13.27-.468a.5.5 0 0 0-.884.468C14.708 5.708 15 6.819 15 8c0 1.18-.292 2.292-.807 3.266a.5.5 0 0 0 .884.468A8 8 0 0 0 16 8a8 8 0 0 0-.923-3.734M3.34 6.182a.5.5 0 1 0-.93-.364A6 6 0 0 0 2 8c0 .769.145 1.505.41 2.182a.5.5 0 1 0 .93-.364A5 5 0 0 1 3 8c0-.642.12-1.255.34-1.818m10.25-.364a.5.5 0 0 0-.93.364c.22.563.34 1.176.34 1.818s-.12 1.255-.34 1.818a.5.5 0 0 0 .93.364C13.856 9.505 14 8.769 14 8s-.145-1.505-.41-2.182"/>
                    </svg> <span class="align-middle">Mobile app</span></a>
                    </li>

                    <?php endif ?>
                    
                    <?php if ($config_output_device == true || is_admin()): ?>
                      <?php if (in_array($company_details->role, ['SuperAdmin', 'Admin'])): ?>
                              <li class="sidebar-item <?php echo ($segment1 == 'outputdevice') ? 'active' : ''; ?>">
                                  <a href="<?php echo base_url('outputdevice'); ?>" class="sidebar-link">
                                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-speaker" viewBox="0 0 16 16">
                                        <path d="M12 1a1 1 0 0 1 1 1v12a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1zM4 0a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2z"/>
                                        <path d="M8 4.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5M8 6a2 2 0 1 0 0-4 2 2 0 0 0 0 4m0 3a1.5 1.5 0 1 0 0 3 1.5 1.5 0 0 0 0-3m-3.5 1.5a3.5 3.5 0 1 1 7 0 3.5 3.5 0 0 1-7 0"/>
                                      </svg>
                                      <span class="align-middle">Loa thanh toán</span>
                                  </a>
                              </li>
                          <?php endif; ?>
                    <?php endif; ?>

                    <?php if($perWebhooks) { ?>

                    <li class="sidebar-item <?php if($segment1 == 'webhooks') echo 'active';?>">
                    <a href="<?php echo base_url('webhooks');?>" class="sidebar-link "><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" class="bi bi-lightning-charge" viewBox="0 0 16 16">
  <path d="M11.251.068a.5.5 0 0 1 .227.58L9.677 6.5H13a.5.5 0 0 1 .364.843l-8 8.5a.5.5 0 0 1-.842-.49L6.323 9.5H3a.5.5 0 0 1-.364-.843l8-8.5a.5.5 0 0 1 .615-.09zM4.157 8.5H7a.5.5 0 0 1 .478.647L6.11 13.59l5.732-6.09H9a.5.5 0 0 1-.478-.647L9.89 2.41 4.157 8.5z"/>
</svg> <span class="align-middle">Tích hợp WebHooks</span></a>
                    </li>

                   <li class="sidebar-item <?php if($segment1 == 'sapo') echo 'active';?>">
                    <a href="<?php echo base_url('sapo');?>" class="sidebar-link "><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" class="bi bi-cart3" viewBox="0 0 16 16">
  <path d="M0 1.5A.5.5 0 0 1 .5 1H2a.5.5 0 0 1 .485.379L2.89 3H14.5a.5.5 0 0 1 .49.598l-1 5a.5.5 0 0 1-.465.401l-9.397.472L4.415 11H13a.5.5 0 0 1 0 1H4a.5.5 0 0 1-.491-.408L2.01 3.607 1.61 2H.5a.5.5 0 0 1-.5-.5zM3.102 4l.84 4.479 9.144-.459L13.89 4H3.102zM5 12a2 2 0 1 0 0 4 2 2 0 0 0 0-4zm7 0a2 2 0 1 0 0 4 2 2 0 0 0 0-4zm-7 1a1 1 0 1 1 0 2 1 1 0 0 1 0-2zm7 0a1 1 0 1 1 0 2 1 1 0 0 1 0-2z"/>
</svg> <span class="align-middle">Tích hợp Sapo</span></a>
                    </li>
 
                    <li class="sidebar-item <?php if($segment1 == 'haravan') echo 'active';?>">
                    <a href="<?php echo base_url('haravan');?>" class="sidebar-link "><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" class="bi bi-h-square-fill" viewBox="0 0 16 16">
  <path d="M2 0a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2H2Zm9 4.002V12H9.67V8.455H6.33V12H5V4.002h1.33v3.322h3.34V4.002H11Z"/>
</svg> <span class="align-middle">Tích hợp Haravan</span></a>
                    </li>

                    <?php if (config(\Config\Shopify::class) && config(\Config\Shopify::class)->visibility): ?>
                    <li class="sidebar-item <?php if($segment1 == 'shopify') echo 'active';?>">
                    <a href="<?php echo base_url('shopify');?>" class="sidebar-link "><svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 452 512"><path fill="currentColor" d="m321.759 65.091l27.784 27.607l41.318 3.08c1.868.157 4.128 1.584 4.482 4.165l56.036 379.051l-151.457 32.766zM285.758 512L0 458.459l38.992-300.79c1.885-13.53 2.339-13.983 16.697-18.486l46.332-14.37C111.94 82.755 143.566 0 209.766 0c8.647 0 18.66 4.643 26.741 15.331c30.296-1.411 46.972 24.128 56.126 50.465l15.168-4.682zm-58.14-475.626c-16.145 6.928-34.61 25.29-44.461 63.31l51.16-15.843c.187-22.342-2.63-37.265-6.7-47.467m-105.18 82.113l40.439-12.524c9.604-50.212 32.345-75.358 53.561-85.341c-2.754-1.897-5.633-2.866-8.49-2.866c-47.55 0-74.262 62.431-85.51 100.73m116.47 64.938s-13.22-7.666-39.983-7.666c-69.453 0-103.86 46.39-103.86 94.327c0 56.955 56.827 58.5 56.827 93.164c0 8.379-5.926 19.846-20.49 19.846c-22.292 0-48.708-22.68-48.708-22.68L69.24 404.874s25.698 31.277 75.952 31.277c41.879 0 72.938-31.533 72.938-80.5c0-62.241-69.268-72.422-69.268-98.995c0-4.87 1.55-24.197 32.345-24.197c21.006 0 38.143 9.146 38.143 9.146zm12.774-104.958l24.77-7.665c-6.275-19.175-16.028-35.834-30.67-37.673c4.5 12.968 6.096 28.395 5.9 45.338"/></svg> <span class="align-middle">Tích hợp Shopify</span></a>
                    </li>
                    <?php endif ?>

                    <?php if ($config_visibility_ghl == true || is_admin()): ?>
                      <li class="sidebar-item <?php if($segment1 == 'gohighlevel') echo 'active';?>">
                      <a href="<?php echo base_url('gohighlevel');?>" class="sidebar-link "> <svg version="1.0" xmlns="http://www.w3.org/2000/svg"  width="20" height="20"  viewBox="0 0 63.000000 50.000000"  preserveAspectRatio="xMidYMid meet">  <g transform="translate(0.000000,50.000000) scale(0.100000,-0.100000)" fill="currentColor"  stroke="none"> <path d="M427 433 c-37 -36 -65 -68 -63 -71 3 -3 25 -1 48 4 36 8 43 13 40 30 -2 13 6 27 21 37 23 15 26 15 46 -7 25 -27 26 -30 5 -51 -11 -11 -25 -14 -45 -10 l-29 7 0 -187 c0 -165 2 -186 16 -183 13 3 16 24 15 156 0 84 -3 157 -6 162 -9 14 9 12 23 -2 8 -8 12 -59 12 -165 0 -129 2 -153 15 -153 13 0 15 27 15 186 l0 185 48 -6 47 -7 -70 71 c-38 39 -70 71 -70 71 0 0 -31 -30 -68 -67z"/> <path d="M63 431 c-35 -32 -63 -61 -63 -65 0 -3 20 -3 45 1 l45 6 0 -187 c0 -167 8 -224 23 -166 9 33 5 250 -4 283 -5 17 -6 33 -4 36 10 9 55 -43 50 -58 -7 -26 -9 -270 -2 -278 14 -13 16 5 17 152 2 231 5 212 -29 207 -37 -5 -80 31 -59 51 41 39 46 40 77 18 20 -14 28 -27 24 -40 -4 -15 3 -19 33 -24 21 -4 40 -4 43 -2 4 5 -114 117 -128 122 -3 1 -34 -24 -68 -56z"/> <path d="M240 225 l-64 -65 47 0 47 0 0 -80 c0 -64 3 -80 15 -80 17 0 20 43 6 102 -7 28 -6 35 5 31 18 -7 45 -49 36 -57 -3 -4 -7 -23 -9 -42 -1 -28 2 -34 18 -34 17 0 19 7 19 83 l0 83 43 -4 42 -4 -65 66 c-35 36 -67 66 -70 66 -3 0 -35 -29 -70 -65z m98 -1 c35 -24 26 -58 -16 -62 -32 -3 -62 11 -62 29 0 11 37 49 48 49 4 0 18 -7 30 -16z"/> </g> </svg>  <span class="align-middle">Tích hợp GoHighLevel</span></a>
                      </li>
                    <?php endif ?>



                    <?php if(in_array($company_details->role,['Admin','SuperAdmin'])) { ?>
                    <li class="sidebar-item <?= strpos($uri->getPath(), 'home/other_intergation') === 0 ? 'active' : ''; ?>">
                        <a class="sidebar-link" href="<?= base_url('home/other_intergation')?>">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" class="bi bi-node-plus-fill" viewBox="0 0 16 16">
                          <path d="M11 13a5 5 0 1 0-4.975-5.5H4A1.5 1.5 0 0 0 2.5 6h-1A1.5 1.5 0 0 0 0 7.5v1A1.5 1.5 0 0 0 1.5 10h1A1.5 1.5 0 0 0 4 8.5h2.025A5 5 0 0 0 11 13m.5-7.5v2h2a.5.5 0 0 1 0 1h-2v2a.5.5 0 0 1-1 0v-2h-2a.5.5 0 0 1 0-1h2v-2a.5.5 0 0 1 1 0"/>
                        </svg>
                            <span class="align-middle">Tất cả tích hợp</span>
                        </a>
                    </li>
                    <?php } ?>
                    <?php } ?>

                    <?php if($perStatisticsCashFlow || $perStatisticsTransaction || $perReportsAccountBalance || $perReportsSubBankAccountIncome || in_array($company_details->role,['Admin','SuperAdmin']) || $perWebhooksLog) { ?>
                      <li class="sidebar-header">Quản trị & Báo cáo</li>

                    <?php if(in_array($company_details->role,['Admin','SuperAdmin'])) { ?>
                      <li class="sidebar-item <?php if($segment1 == 'ticket') echo 'active';?>">
                      <a href="<?php echo base_url('ticket');?>" class="sidebar-link "><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" class="bi bi-life-preserver" viewBox="0 0 16 16">
  <path d="M8 16A8 8 0 1 0 8 0a8 8 0 0 0 0 16m6.43-5.228a7.03 7.03 0 0 1-3.658 3.658l-1.115-2.788a4 4 0 0 0 1.985-1.985zM5.228 14.43a7.03 7.03 0 0 1-3.658-3.658l2.788-1.115a4 4 0 0 0 1.985 1.985zm9.202-9.202-2.788 1.115a4 4 0 0 0-1.985-1.985l1.115-2.788a7.03 7.03 0 0 1 3.658 3.658m-8.087-.87a4 4 0 0 0-1.985 1.985L1.57 5.228A7.03 7.03 0 0 1 5.228 1.57zM8 11a3 3 0 1 1 0-6 3 3 0 0 1 0 6"/>
</svg> <span class="align-middle">Yêu cầu hỗ trợ</span></a>
                      </li>
                    <?php } ?>

                    <li class="sidebar-item <?php if($segment1 == 'statistics') echo 'active';?>">
                    
                        <a data-bs-target="#statistics" data-bs-toggle="collapse" class="sidebar-link collapsed"> <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" class="bi bi-bar-chart-line" viewBox="0 0 16 16">
  <path d="M11 2a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v12h.5a.5.5 0 0 1 0 1H.5a.5.5 0 0 1 0-1H1v-3a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v3h1V7a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v7h1V2zm1 12h2V2h-2v12zm-3 0V7H7v7h2zm-5 0v-3H2v3h2z"/>
</svg> <span class="align-middle">Thống kê</span></a>

                    <ul id="statistics" class="sidebar-dropdown list-unstyled collapse <?php if($segment1 == 'statistics' || $segment1 == 'reports') echo 'show';?>" data-bs-parent="#sidebar">
                    <?php if($perReportsAccountBalance): ?>
							          <li class="sidebar-item <?php if($segment1 == 'reports' && isset($segments[1]) && $segments[1] == "bankaccounts") echo 'active';?>"><a class="sidebar-link" href="<?php echo base_url('reports/bankaccounts');?>">Số dư tài khoản</a></li>
                      <?php endif;?>
                     <?php if($perStatisticsCashFlow): ?>
							          <li class="sidebar-item <?php if($segment1 == 'statistics' && isset($segments[1]) && ($segments[1] == "cashflow" || $segments[1] == "daily_cashflow")) echo 'active';?>"><a class="sidebar-link" href="<?php echo base_url('statistics/cashflow');?>">Dòng tiền</a></li>
 
                      <?php endif;?>

                      <?php if($perStatisticsTransaction): ?>
							          <li class="sidebar-item <?php if($segment1 == 'statistics' && isset($segments[1]) && $segments[1] == "transaction") echo 'active';?>"><a class="sidebar-link" href="<?php echo base_url('statistics/transaction');?>">Số lượng giao dịch</a></li>
 
                      <?php endif;?>
                      <?php if($perStatisticsTransaction): ?>
							          <li class="sidebar-item <?php if($segment1 == 'statistics' && isset($segments[1]) && $segments[1] == "counter") echo 'active';?>"><a class="sidebar-link" href="<?php echo base_url('statistics/counter');?>">Bộ đếm</a></li>
 
                      <?php endif;?>
                     

                      <?php if($perReportsSubBankAccountIncome && $bankSubAccount == "on"): ?>
							          <li class="sidebar-item <?php if($segment1 == 'reports' && isset($segments[1]) && $segments[1] == "subbankaccounts") echo 'active';?>"><a class="sidebar-link" href="<?php echo base_url('reports/subbankaccounts');?>">Tiền vào tài khoản phụ</a></li>
                      <?php endif;?>

                    
                   
						        </ul>
                    </li>
                    <?php } ?>


                 


                    <?php if(in_array($company_details->role,['Admin','SuperAdmin'])) { ?>

                    <li class="sidebar-item <?php if($segment1 == 'companyusers' || $segment1 == 'userpermission' || $segment1 == 'company' ||  $segment1 == 'invoices' ||  $segment1 == 'companyapi') echo 'active';?>">
                    
                        <a data-bs-target="#settings" data-bs-toggle="collapse" class="sidebar-link collapsed"> <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" class="bi bi-gear-wide-connected" viewBox="0 0 16 16">
  <path d="M7.068.727c.243-.97 1.62-.97 1.864 0l.071.286a.96.96 0 0 0 1.622.434l.205-.211c.695-.719 1.888-.03 1.613.931l-.08.284a.96.96 0 0 0 1.187 1.187l.283-.081c.96-.275 1.65.918.931 1.613l-.211.205a.96.96 0 0 0 .434 1.622l.286.071c.97.243.97 1.62 0 1.864l-.286.071a.96.96 0 0 0-.434 1.622l.211.205c.719.695.03 1.888-.931 1.613l-.284-.08a.96.96 0 0 0-1.187 1.187l.081.283c.275.96-.918 1.65-1.613.931l-.205-.211a.96.96 0 0 0-1.622.434l-.071.286c-.243.97-1.62.97-1.864 0l-.071-.286a.96.96 0 0 0-1.622-.434l-.205.211c-.695.719-1.888.03-1.613-.931l.08-.284a.96.96 0 0 0-1.186-1.187l-.284.081c-.96.275-1.65-.918-.931-1.613l.211-.205a.96.96 0 0 0-.434-1.622l-.286-.071c-.97-.243-.97-1.62 0-1.864l.286-.071a.96.96 0 0 0 .434-1.622l-.211-.205c-.719-.695-.03-1.888.931-1.613l.284.08a.96.96 0 0 0 1.187-1.186l-.081-.284c-.275-.96.918-1.65 1.613-.931l.205.211a.96.96 0 0 0 1.622-.434l.071-.286zM12.973 8.5H8.25l-2.834 3.779A4.998 4.998 0 0 0 12.973 8.5zm0-1a4.998 4.998 0 0 0-7.557-3.779l2.834 3.78h4.723zM5.048 3.967c-.03.021-.058.043-.087.065l.087-.065zm-.431.355A4.984 4.984 0 0 0 3.002 8c0 1.455.622 2.765 1.615 3.678L7.375 8 4.617 4.322zm.344 7.646.087.065-.087-.065z"/> </svg> <span class="align-middle">Cấu hình Công ty</span></a>

                    <ul id="settings" class="sidebar-dropdown list-unstyled collapse <?php if($segment1 == 'companyusers' || $segment1 == 'userpermission' || $segment1 == 'company' || $segment1 == 'invoices' || $segment1 == 'companyapi') echo 'show';?>" data-bs-parent="#sidebar">

                    <li class="sidebar-item <?php if($segment1 == 'company' && isset($segments[1]) && $segments[1] == "plans") echo 'active';?>"><a class="sidebar-link" href="<?php echo base_url('company/plans');?>">Gói dịch vụ</a></li>

                    
							          <li class="sidebar-item <?php if($segment1 == 'company' && isset($segments[1]) && $segments[1] == "profile") echo 'active';?>"><a class="sidebar-link" href="<?php echo base_url('company/profile');?>">Hồ sơ công ty</a></li>

                        <li class="sidebar-item <?php if($segment1 == 'invoices') echo 'active';?>"><a class="sidebar-link" href="<?php echo base_url('invoices');?>">Hóa đơn</a></li>


                        <li class="sidebar-item <?php if($segment1 == 'company' && isset($segments[1]) && $segments[1] == "configuration") echo 'active';?>"><a class="sidebar-link" href="<?php echo base_url('company/configuration');?>">Cấu hình chung</a></li>

                            <li class="sidebar-item <?php if($segment1 == 'companyusers' || $segment1 == 'userpermission') echo 'active';?>"><a class="sidebar-link" href="<?php echo base_url('companyusers');?>">Người dùng</a></li>
                            <li class="sidebar-item <?php if($segment1 == 'companyapi' || $segment1 == 'companyapi') echo 'active';?>"><a class="sidebar-link" href="<?php echo base_url('companyapi');?>">API Access</a></li>
                            
						</ul>
                    </li>
                    <?php } ?>





                    <?php if($perWebhooksLog) { ?>
                    <li class="sidebar-item <?php if($segment1 == 'logs' || $segment1 == 'webhookslog') echo 'active';?>">
                    
                        <a data-bs-target="#logs" data-bs-toggle="collapse" class="sidebar-link collapsed"> <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" class="bi bi-clock-history" viewBox="0 0 16 16">
  <path d="M8.515 1.019A7 7 0 0 0 8 1V0a8 8 0 0 1 .589.022l-.074.997zm2.004.45a7.003 7.003 0 0 0-.985-.299l.219-.976c.383.086.76.2 1.126.342l-.36.933zm1.37.71a7.01 7.01 0 0 0-.439-.27l.493-.87a8.025 8.025 0 0 1 .979.654l-.615.789a6.996 6.996 0 0 0-.418-.302zm1.834 1.79a6.99 6.99 0 0 0-.653-.796l.724-.69c.27.285.52.59.747.91l-.818.576zm.744 1.352a7.08 7.08 0 0 0-.214-.468l.893-.45a7.976 7.976 0 0 1 .45 1.088l-.95.313a7.023 7.023 0 0 0-.179-.483zm.53 2.507a6.991 6.991 0 0 0-.1-1.025l.985-.17c.067.386.106.778.116 1.17l-1 .025zm-.131 1.538c.033-.17.06-.339.081-.51l.993.123a7.957 7.957 0 0 1-.23 1.155l-.964-.267c.046-.165.086-.332.12-.501zm-.952 2.379c.184-.29.346-.594.486-.908l.914.405c-.16.36-.345.706-.555 1.038l-.845-.535zm-.964 1.205c.122-.122.239-.248.35-.378l.758.653a8.073 8.073 0 0 1-.401.432l-.707-.707z"/>
  <path d="M8 1a7 7 0 1 0 4.95 11.95l.707.707A8.001 8.001 0 1 1 8 0v1z"/>
  <path d="M7.5 3a.5.5 0 0 1 .5.5v5.21l3.248 1.856a.5.5 0 0 1-.496.868l-3.5-2A.5.5 0 0 1 7 9V3.5a.5.5 0 0 1 .5-.5z"/>
</svg> <span class="align-middle">Nhật ký</span></a>

                    <ul id="logs" class="sidebar-dropdown list-unstyled collapse <?php if($segment1 == 'logs' || $segment1 == 'webhookslog') echo 'show';?>" data-bs-parent="#sidebar">

                    <?php if(in_array($company_details->role,['Admin','SuperAdmin'])): ?>

							          <li class="sidebar-item <?php if($segment1 == 'logs') echo 'active';?>"><a class="sidebar-link" href="<?php echo base_url('logs');?>">Nhật ký người dùng</a></li>
 
                      <?php endif;?>

                      <?php if($perWebhooksLog): ?>

                      <li class="sidebar-item <?php if($segment1 == 'webhookslog') echo 'active';?>"><a class="sidebar-link" href="<?php echo base_url('webhookslog');?>">Nhật ký WebHooks</a></li>
                      <?php endif;?>

						        </ul>
                    </li>
                    <?php } ?>

                    <li class="sidebar-item mt-3 text-center">  <a  class="sidebar-link"  onclick="sidebar_behavior()" id="sidebar_behavior_icon">
                        <?php if($user_details->sidebar_behavior =='fixed') { ?>
                        <i  class="bi bi-chevron-left"></i> Thu gọn
                        <?php } else { ?>
                        <i  class="bi bi-chevron-right"></i>
                        <?php } ?>
                        </a>
                      </li>
                      
                    
                </ul>
                

                <div class="sidebar-cta">
                  <div class="sidebar-cta-content border text-center p-1">
                  <?php if(in_array($company_details->role,['Admin','SuperAdmin'])) { ?>
                    <a class="sidebar-link py-1" href="<?= base_url('company/profile');?>"><i class="bi bi-buildings"></i> <?php echo esc($company_details->short_name);?></a>

                    <a class="sidebar-link" href="<?= base_url('company/plans');?>">Gói: <?= esc($company_details->plan_name);?></a>
                    <?php } else { ?>
                      <div class="py-3">
                      <strong class="d-inline-block"><?php echo esc($company_details->short_name);?></strong>
                    </div>
                    <?php } ?>
                  </div>
                </div>

                <div class="sidebar-cta">
                  <div class="sidebar-cta-content text-center">
                    <div><span class=''><i class="bi bi-telephone"></i> Hotline hỗ trợ</span></div>
                    <a class="sidebar-link" href="tel:02873059589">02873.059.589</a>
                  </div>
                </div>

              


            </div>
        </nav>
        <div class="main">
        <div id="top-alert"></div>
        <nav class="navbar navbar-expand navbar-light navbar-bg">
            <a class="sidebar-toggle" <?php if(!is_mobile()) { ?> onclick="sidebar_toggle()" <?php } ?>>
                <i class="hamburger align-self-center"></i>
            </a>
            <h3 class="mb-0 text-truncate"><?php 
echo (strlen(esc($page_title)) > 15) && is_mobile() ? substr(esc($page_title), 0, 10).'...' : esc($page_title);
?> </h3>

            <ul class="navbar-nav" style="flex: none;">
  <li id="quick_menu" class="nav-item px-2 dropdown d-none d-sm-inline-block">
    <a class="nav-link dropdown-toggle" href="#" id="servicesDropdown" role="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
      Quick menu
    </a>
    <div class="dropdown-menu dropdown-menu-start dropdown-mega" aria-labelledby="servicesDropdown">
      <div class="d-md-flex align-items-start justify-content-start">


        <div class="dropdown-mega-list">
          <div class="dropdown-header">Dashboard</div>
          <a class="dropdown-item" href="<?php echo base_url();?>"><i class="bi bi-speedometer me-2"></i> Tổng quan</a>
          <?php if($perTransactions) { ?>
          <a class="dropdown-item" href="<?php echo base_url('transactions');?>"><i class="bi bi-arrow-left-right me-2"></i> Giao dịch</a>
          <?php } ?>
          <?php if($perBankAccount) { ?>
          <a class="dropdown-item" href="<?php echo base_url('bankaccount');?>"><i class="bi bi-bank me-2"></i> Ngân hàng</a>
          <?php } ?>
          <?php if($bankBonus && (!$configBankBonus->visibility || is_admin())) {?>
            <a class="dropdown-item" href="<?php echo base_url('bankbonus');?>"><i class="bi bi-building-add me-2"></i> Mở mới TK nhận giao dịch</a>
          <?php }?>
          <?php if(in_array($company_details->role,['Admin','SuperAdmin'])) { ?>
            <a class="dropdown-item" href="<?php echo base_url('ticket');?>"><i class="bi bi-life-preserver me-2"></i> Hỗ trợ</a>

          <?php } ?>
          <a class="dropdown-item" target="_blank" href="https://sepay.vn/affiliate.html"><i class="bi bi-currency-dollar me-2"></i> Affiliate</a>
        </div>

        <?php if($perNotificationTelegram || $perWebhooks || $perNotificationLarkMessenger) { ?>

        <div class="dropdown-mega-list">
          <div class="dropdown-header">Tích hợp</div>
          <?php if (mobile_feature_visibility() && in_array($company_details->role, ['SuperAdmin', 'Admin'])): ?>
          <a class="dropdown-item" href="<?php echo base_url('mobileapp');?>"><i class="bi bi-phone-vibrate-fill me-2"></i> Mobile App</a>
          <?php endif ?>
          <?php if($perNotificationTelegram) { ?>
          <a class="dropdown-item" href="<?php echo base_url('notificationtelegram');?>"><i class="bi bi-telegram me-2"></i> Tích hợp Telegram</a>
          <?php } ?>    
          <?php if($perNotificationLarkMessenger) { ?>
          <a class="dropdown-item" href="<?php echo base_url('notificationlarkmessenger');?>"><i class="bi bi-send me-2"></i> Tích hợp Lark Messenger</a>
          <?php } ?>
          <?php if($perNotificationGoogleSheets && ($configGoogleSheets->visibility || is_admin())) { ?>
          <a class="dropdown-item" href="<?php echo base_url('googlesheets');?>"><i class="bi bi-file-earmark-spreadsheet me-2"></i> Tích hợp Google Sheets</a>
          <?php } ?>        
          <?php if($perWebhooks) { ?>
          <a class="dropdown-item" href="<?php echo base_url('webhooks');?>"><i class="bi bi-lightning-charge me-2"></i> Tích hợp WebHooks</a>
          <a class="dropdown-item" href="<?php echo base_url('sapo');?>"><i class="bi bi-cart3 me-2"></i> Tích hợp Sapo</a>
          <a class="dropdown-item" href="<?php echo base_url('haravan');?>"><i class="bi bi-h-square-fill me-2"></i> Tích hợp Haravan</a>
          <a class="dropdown-item" href="<?php echo base_url('shopify');?>"><svg xmlns="http://www.w3.org/2000/svg" class="me-1" width="16" height="16" viewBox="0 0 452 512"><path fill="currentColor" d="m321.759 65.091l27.784 27.607l41.318 3.08c1.868.157 4.128 1.584 4.482 4.165l56.036 379.051l-151.457 32.766zM285.758 512L0 458.459l38.992-300.79c1.885-13.53 2.339-13.983 16.697-18.486l46.332-14.37C111.94 82.755 143.566 0 209.766 0c8.647 0 18.66 4.643 26.741 15.331c30.296-1.411 46.972 24.128 56.126 50.465l15.168-4.682zm-58.14-475.626c-16.145 6.928-34.61 25.29-44.461 63.31l51.16-15.843c.187-22.342-2.63-37.265-6.7-47.467m-105.18 82.113l40.439-12.524c9.604-50.212 32.345-75.358 53.561-85.341c-2.754-1.897-5.633-2.866-8.49-2.866c-47.55 0-74.262 62.431-85.51 100.73m116.47 64.938s-13.22-7.666-39.983-7.666c-69.453 0-103.86 46.39-103.86 94.327c0 56.955 56.827 58.5 56.827 93.164c0 8.379-5.926 19.846-20.49 19.846c-22.292 0-48.708-22.68-48.708-22.68L69.24 404.874s25.698 31.277 75.952 31.277c41.879 0 72.938-31.533 72.938-80.5c0-62.241-69.268-72.422-69.268-98.995c0-4.87 1.55-24.197 32.345-24.197c21.006 0 38.143 9.146 38.143 9.146zm12.774-104.958l24.77-7.665c-6.275-19.175-16.028-35.834-30.67-37.673c4.5 12.968 6.096 28.395 5.9 45.338"></path></svg> Tích hợp Shopify</a>
          <?php if ($config_visibility_ghl == true || is_admin()): ?>
              <a class="dropdown-item" href="<?php echo base_url('gohighlevel');?>"><svg version="1.0" xmlns="http://www.w3.org/2000/svg"  width="20" height="20"  viewBox="0 0 63.000000 50.000000"  preserveAspectRatio="xMidYMid meet">  <g transform="translate(0.000000,50.000000) scale(0.100000,-0.100000)" fill="currentColor"  stroke="none"> <path d="M427 433 c-37 -36 -65 -68 -63 -71 3 -3 25 -1 48 4 36 8 43 13 40 30 -2 13 6 27 21 37 23 15 26 15 46 -7 25 -27 26 -30 5 -51 -11 -11 -25 -14 -45 -10 l-29 7 0 -187 c0 -165 2 -186 16 -183 13 3 16 24 15 156 0 84 -3 157 -6 162 -9 14 9 12 23 -2 8 -8 12 -59 12 -165 0 -129 2 -153 15 -153 13 0 15 27 15 186 l0 185 48 -6 47 -7 -70 71 c-38 39 -70 71 -70 71 0 0 -31 -30 -68 -67z"/> <path d="M63 431 c-35 -32 -63 -61 -63 -65 0 -3 20 -3 45 1 l45 6 0 -187 c0 -167 8 -224 23 -166 9 33 5 250 -4 283 -5 17 -6 33 -4 36 10 9 55 -43 50 -58 -7 -26 -9 -270 -2 -278 14 -13 16 5 17 152 2 231 5 212 -29 207 -37 -5 -80 31 -59 51 41 39 46 40 77 18 20 -14 28 -27 24 -40 -4 -15 3 -19 33 -24 21 -4 40 -4 43 -2 4 5 -114 117 -128 122 -3 1 -34 -24 -68 -56z"/> <path d="M240 225 l-64 -65 47 0 47 0 0 -80 c0 -64 3 -80 15 -80 17 0 20 43 6 102 -7 28 -6 35 5 31 18 -7 45 -49 36 -57 -3 -4 -7 -23 -9 -42 -1 -28 2 -34 18 -34 17 0 19 7 19 83 l0 83 43 -4 42 -4 -65 66 c-35 36 -67 66 -70 66 -3 0 -35 -29 -70 -65z m98 -1 c35 -24 26 -58 -16 -62 -32 -3 -62 11 -62 29 0 11 37 49 48 49 4 0 18 -7 30 -16z"/> </g> </svg> Tích hợp Gohighlevel</a>
            <?php endif?>
            <?php } ?>
        </div>
        <?php } ?>


        <?php if($perStatisticsCashFlow || $perStatisticsTransaction ||$perReportsAccountBalance || $perReportsSubBankAccountIncome ) { ?>

        <div class="dropdown-mega-list">
          <div class="dropdown-header">Thống kê</div>
          <?php if($perReportsAccountBalance) { ?>
          <a class="dropdown-item" href="<?php echo base_url('reports/bankaccounts');?>"><i class="bi bi-wallet2 me-2"></i> Số dư tài khoản</a>
          <?php } ?>
          <?php if($perStatisticsCashFlow) { ?>
          <a class="dropdown-item" href="<?php echo base_url('statistics/cashflow');?>"><i class="bi bi-cash me-2"></i> Dòng tiền</a>
          <?php } ?>   
          <?php if($perStatisticsTransaction) { ?>
          <a class="dropdown-item" href="<?php echo base_url('statistics/transaction');?>"><i class="bi bi-bar-chart me-2"></i> Số lượng giao dịch</a>
          <a class="dropdown-item" href="<?php echo base_url('statistics/counter');?>"><i class="bi bi-hourglass-bottom me-2"></i> Bộ đếm</a>

          <?php } ?>     
           
          <?php if($perReportsSubBankAccountIncome && $bankSubAccount == "on") { ?>
          <a class="dropdown-item" href="<?php echo base_url('reports/subbankaccounts');?>"><i class="bi bi-diagram-3 me-2"></i> Tiền vào tài khoản phụ</a>
          <?php } ?>        
        </div>
        <?php } ?>        

        <?php if(in_array($company_details->role,['Admin','SuperAdmin'])) { ?>

        <div class="dropdown-mega-list">
          <div class="dropdown-header">Cấu hình</div>
          <a class="dropdown-item" href="<?php echo base_url('company/configuration');?>"><i class="bi bi-gear-wide-connected me-2"></i> Cấu hình chung</a>

          <a class="dropdown-item" href="<?php echo base_url('company/plans');?>"><i class="bi bi-box me-2"></i> Gói dịch vụ</a>
          <a class="dropdown-item" href="<?php echo base_url('company/profile');?>"><i class="bi bi-buildings me-2"></i> Hồ sơ công ty</a>
          <a class="dropdown-item" href="<?php echo base_url('invoices');?>"><i class="bi bi-receipt me-2"></i> Hóa đơn</a>
          <a class="dropdown-item" href="<?php echo base_url('companyusers');?>"><i class="bi bi-people me-2"></i> Người dùng</a>
          <a class="dropdown-item" href="<?php echo base_url('companyapi');?>"><i class="bi bi-code-slash me-2"></i> API Access</a>

        </div>
        <?php } ?>   


      </div>
    </div>
  </li>
</ul>

<div class="navbar-collapse collapse">
        <ul class="navbar-nav navbar-align">

        <li class="nav-item d-none d-xl-block">
          <a href="https://sepay.vn/khuyen-mai" class="nav-link link-primary d-flex align-items-center" target="_blank"><i class="bi bi-gift-fill me-2"></i> Ưu đãi</a>
        </li>

        <?php if(in_array($company_details->role,['Admin','SuperAdmin'])) { ?>

        <li class="nav-item" style="white-space: nowrap;">
          <a href="<?php echo base_url('company/change_plan'); ?>" class="nav-link link-warning"><i class="bi bi-star-fill"></i> Nâng cấp</a>
        </li>

        <?php if ($creditUsagable): ?>
        <li class="nav-item d-none d-xl-block" style="white-space: nowrap;">
          <a href="<?php echo base_url('creditlog');?>" class="nav-link link-dark">
            <i class="bi bi-wallet2"></i> Số dư: <span class="text-success"><?= number_format($company_details->credit_balance) ?>đ</span>
          </a>
        </li>
        <?php endif ?>
        <?php } ?>
<?php 
  if($user_details->theme == 'auto') {
    if(date('H') >= 6 && date('H') <= 18)
      $theme_mode = 'light';
    else
      $theme_mode = 'dark';
  } else 
    $theme_mode = $user_details->theme;
   
?>          
                        <li class="nav-item dropdown" id="dropdown-noti">
							<a class="nav-icon dropdown-toggle" href="#" data-bs-toggle="dropdown" aria-expanded="true">
								<div class="position-relative">
                                    <i class="bi bi-bell-fill fs-3 <?= $user_details->unread_noti_count ? 'text-primary' : '' ?>"></i>
                                    <?php if ($user_details->unread_noti_count): ?>
                                        <span class="indicator unread-noti-count bg-danger"><?= $user_details->unread_noti_count ?></span>
                                    <?php endif ?>
								</div>
                            </a>
                            <div class="dropdown-menu dropdown-menu-lg dropdown-menu-end py-0" aria-labelledby="dropdown-noti" data-bs-popper="static">
 							    <div class="dropdown-menu-header py-1">
									<div class="position-relative d-flex align-items-center">
                    <h5 class="mb-0">Thông báo</h3>
										<a href="#" class="text-sm ms-auto fw-normal text-muted mark-read-all-noti"><i class="bi bi-check-all fs-4"></i> Đánh dấu tất cả đã đọc</a>
									</div>
                                </div>
                                <div class="border-bottom">
                                    <div class="d-flex align-items-center" id="btn-noti-filter-group">
                                        <div style="width: 33.33%;"><div class="bg-white border-2 border-bottom border-secondary btn-noti-filter cursor-pointer px-2 py-1 text-sm text-center" data-filter="*"><i class="bi bi-asterisk"></i> Tất cả</div></div>
                                        <div style="width: 33.33%;"><div class="bg-white border-2 btn-noti-filter cursor-pointer px-2 py-1 text-sm text-center" data-filter=""><i class="bi bi-bell"></i> Thông báo</div></div>
                                        <div style="width: 33.33%;"><div class="bg-white border-2 btn-noti-filter cursor-pointer px-2 py-1 text-sm text-center" data-filter="promotion"><i class="bi bi-gift-fill"></i> Ưu đãi</div></div>
                                    </div>
                                </div>
                                <div class="p-5 text-center" id="list-noti-empty-state">
                                    <div style="width: 56px; height: 56px; background-color: rgba(31, 155, 207, .175); color: #1f9bcf;" class="mx-auto rounded-circle d-flex align-items-center justify-content-center">
                                        <i class="bi bi-inbox fs-2"></i>
                                    </div>
                                    <p class="mt-2 fw-bold mb-0">Hiện chưa có thông báo nào</p>
                                </div>
                                <div class="list-group" id="list-noti" style="max-height: 500px; overflow: auto;">
                                
                                </div>
                                <div id="list-noti-loader" class="d-flex align-items-center justify-content-center py-5">
                                    <div class="spinner-border me-1" role="status" style="width: 15px; height: 15px;">
                                      <span class="visually-hidden">Loading...</span>
                                    </div>Đang tải...
                                </div>
								<div class="dropdown-menu-footer border-top">
                                    <a href="<?= base_url('notification') ?>" class="text-muted">Hiển thị tất cả</a>
                                </div>
                            </div>
						</li>

              <li class="nav-item dropdown">
							<a class="nav-flag dropdown-toggle" href="#" id="languageDropdown" data-bs-toggle="dropdown">
              <?php if($user_details->theme == 'light') { ?>
                <i class="bi bi-brightness-high-fill"></i>
              <?php } else if($user_details->theme == 'dark') { ?>
                <i class="bi bi-moon-stars-fill"></i>
              <?php } else if($user_details->theme == 'auto') {  ?>
                <i class="bi bi-circle-half"></i>
                <?php } ?>
              </a>
							<div class="dropdown-menu dropdown-menu-end" aria-labelledby="languageDropdown">
								<a class="dropdown-item d-flex align-items-center <?php if($user_details->theme == 'light') echo 'active'; ?>" onclick="set_theme('light')"  href="javascript:;">
                <i class="bi bi-brightness-high-fill me-2"></i>
                  <span class="align-middle">Sáng</span>
                </a>
								<a class="dropdown-item d-flex align-items-center <?php if($user_details->theme == 'dark') echo 'active'; ?>" onclick="set_theme('dark')" href="javascript:;">
                <i class="bi bi-moon-stars-fill  me-2"></i>
                  <span class="align-middle">Tối</span>
                </a>
                <a class="dropdown-item d-flex align-items-center <?php if($user_details->theme == 'auto') echo 'active'; ?>" onclick="set_theme('auto')" href="javascript:;">
                <i class="bi bi-circle-half me-2"></i>
                  <span class="align-middle">Tự động</span>
                </a>
								 
							</div>
						</li>

                
            <li class="nav-item dropdown">
              
							<a class="nav-icon dropdown-toggle d-inline-block d-sm-none" href="#" data-bs-toggle="dropdown" aria-expanded="false">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-settings align-middle"><circle cx="12" cy="12" r="3"></circle><path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path></svg>
              </a>

							<a class="nav-link dropdown-toggle d-none d-sm-inline-block" href="<?php echo base_url('user/my_profile');?>" data-bs-toggle="dropdown">
                <img src="<?php echo esc(get_gravatar($user_details->email,32));?>" class="avatar img-fluid rounded-circle me-1" alt=""> <span class="text-dark"><?php echo esc(character_limiter( $user_details->lastname . ' ' .$user_details->firstname, 15, '..'));?></span>
              </a>
                            <div class="dropdown-menu dropdown-menu-end">
                
                <a href="https://sepay.vn/khuyen-mai" class="dropdown-item d-xl-none text-primary d-flex align-items-center" target="_blank"><i class="bi bi-gift-fill me-2"></i> Ưu đãi <i class="bi bi-box-arrow-up-right ms-2"></i></a> 

              <?php if(in_array($company_details->role,['Admin','SuperAdmin'])) { ?>
                <a class="dropdown-item" href="<?php echo base_url('company/profile');?>"><?php echo esc(character_limiter( $company_details->full_name, 20, '..'));?></a>
                <?php if ($creditUsagable): ?>
                <a class="dropdown-item d-xl-none" href="<?= base_url('creditlog') ?>">
                  <i class="bi bi-wallet2"></i> Số dư: <span class="text-success"><?= number_format($company_details->credit_balance) ?>đ</span>
                </a>
                <?php endif ?>
								<div class="dropdown-divider"></div>
              <?php } ?>

								<a class="dropdown-item" href="<?php echo base_url('user/my_profile');?>"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-user align-middle me-1"><path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path><circle cx="12" cy="7" r="4"></circle></svg> Tài khoản</a>

                 <?php if(in_array($company_details->role,['Admin','SuperAdmin'])) { ?>

                <a class="dropdown-item" href="<?php echo base_url('companyusers');?>"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" class="bi bi-people me-1" viewBox="0 0 16 16">
  <path d="M15 14s1 0 1-1-1-4-5-4-5 3-5 4 1 1 1 1zm-7.978-1L7 12.996c.001-.264.167-1.03.76-1.72C8.312 10.629 9.282 10 11 10c1.717 0 2.687.63 3.24 1.276.593.69.758 1.457.76 1.72l-.008.002-.014.002zM11 7a2 2 0 1 0 0-4 2 2 0 0 0 0 4m3-2a3 3 0 1 1-6 0 3 3 0 0 1 6 0M6.936 9.28a6 6 0 0 0-1.23-.247A7 7 0 0 0 5 9c-4 0-5 3-5 4q0 1 1 1h4.216A2.24 2.24 0 0 1 5 13c0-1.01.377-2.042 1.09-2.904.243-.294.526-.569.846-.816M4.92 10A5.5 5.5 0 0 0 4 13H1c0-.26.164-1.03.76-1.724.545-.636 1.492-1.256 3.16-1.275ZM1.5 5.5a3 3 0 1 1 6 0 3 3 0 0 1-6 0m3-2a2 2 0 1 0 0 4 2 2 0 0 0 0-4"/>
</svg> Quản lý người dùng</a>

<a class="dropdown-item" href="<?php echo base_url('company/plans');?>"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" class="bi bi-box me-1" viewBox="0 0 16 16">
  <path d="M8.186 1.113a.5.5 0 0 0-.372 0L1.846 3.5 8 5.961 14.154 3.5zM15 4.239l-6.5 2.6v7.922l6.5-2.6V4.24zM7.5 14.762V6.838L1 4.239v7.923zM7.443.184a1.5 1.5 0 0 1 1.114 0l7.129 2.852A.5.5 0 0 1 16 3.5v8.662a1 1 0 0 1-.629.928l-7.185 2.874a.5.5 0 0 1-.372 0L.63 13.09a1 1 0 0 1-.63-.928V3.5a.5.5 0 0 1 .314-.464z"/>
</svg> Gói dịch vụ</a>

<a class="dropdown-item" href="<?php echo base_url('invoices');?>"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" class="bi bi-receipt me-1" viewBox="0 0 16 16">
  <path d="M1.92.506a.5.5 0 0 1 .434.14L3 1.293l.646-.647a.5.5 0 0 1 .708 0L5 1.293l.646-.647a.5.5 0 0 1 .708 0L7 1.293l.646-.647a.5.5 0 0 1 .708 0L9 1.293l.646-.647a.5.5 0 0 1 .708 0l.646.647.646-.647a.5.5 0 0 1 .708 0l.646.647.646-.647a.5.5 0 0 1 .801.13l.5 1A.5.5 0 0 1 15 2v12a.5.5 0 0 1-.053.224l-.5 1a.5.5 0 0 1-.8.13L13 14.707l-.646.647a.5.5 0 0 1-.708 0L11 14.707l-.646.647a.5.5 0 0 1-.708 0L9 14.707l-.646.647a.5.5 0 0 1-.708 0L7 14.707l-.646.647a.5.5 0 0 1-.708 0L5 14.707l-.646.647a.5.5 0 0 1-.708 0L3 14.707l-.646.647a.5.5 0 0 1-.801-.13l-.5-1A.5.5 0 0 1 1 14V2a.5.5 0 0 1 .053-.224l.5-1a.5.5 0 0 1 .367-.27m.217 1.338L2 2.118v11.764l.137.274.51-.51a.5.5 0 0 1 .707 0l.646.647.646-.646a.5.5 0 0 1 .708 0l.646.646.646-.646a.5.5 0 0 1 .708 0l.646.646.646-.646a.5.5 0 0 1 .708 0l.646.646.646-.646a.5.5 0 0 1 .708 0l.646.646.646-.646a.5.5 0 0 1 .708 0l.509.509.137-.274V2.118l-.137-.274-.51.51a.5.5 0 0 1-.707 0L12 1.707l-.646.647a.5.5 0 0 1-.708 0L10 1.707l-.646.647a.5.5 0 0 1-.708 0L8 1.707l-.646.647a.5.5 0 0 1-.708 0L6 1.707l-.646.647a.5.5 0 0 1-.708 0L4 1.707l-.646.647a.5.5 0 0 1-.708 0z"/>
  <path d="M3 4.5a.5.5 0 0 1 .5-.5h6a.5.5 0 1 1 0 1h-6a.5.5 0 0 1-.5-.5m0 2a.5.5 0 0 1 .5-.5h6a.5.5 0 1 1 0 1h-6a.5.5 0 0 1-.5-.5m0 2a.5.5 0 0 1 .5-.5h6a.5.5 0 1 1 0 1h-6a.5.5 0 0 1-.5-.5m0 2a.5.5 0 0 1 .5-.5h6a.5.5 0 0 1 0 1h-6a.5.5 0 0 1-.5-.5m8-6a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 0 1h-1a.5.5 0 0 1-.5-.5m0 2a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 0 1h-1a.5.5 0 0 1-.5-.5m0 2a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 0 1h-1a.5.5 0 0 1-.5-.5m0 2a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 0 1h-1a.5.5 0 0 1-.5-.5"/>
</svg> Thanh toán</a>

<a class="dropdown-item" href="<?php echo base_url('statistics/counter');?>"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" class="bi bi-bar-chart me-1" viewBox="0 0 16 16">
  <path d="M4 11H2v3h2zm5-4H7v7h2zm5-5v12h-2V2zm-2-1a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1h2a1 1 0 0 0 1-1V2a1 1 0 0 0-1-1zM6 7a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v7a1 1 0 0 1-1 1H7a1 1 0 0 1-1-1zm-5 4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v3a1 1 0 0 1-1 1H2a1 1 0 0 1-1-1z"/>
</svg> Thống kê sử dụng</a>

<a class="dropdown-item" href="<?php echo base_url('company/change_plan');?>"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" class="bi bi-star me-1" viewBox="0 0 16 16">
  <path d="M2.866 14.85c-.078.444.36.791.746.593l4.39-2.256 4.389 2.256c.386.198.824-.149.746-.592l-.83-4.73 3.522-3.356c.33-.314.16-.888-.282-.95l-4.898-.696L8.465.792a.513.513 0 0 0-.927 0L5.354 5.12l-4.898.696c-.441.062-.612.636-.283.95l3.523 3.356-.83 4.73zm4.905-2.767-3.686 1.894.694-3.957a.56.56 0 0 0-.163-.505L1.71 6.745l4.052-.576a.53.53 0 0 0 .393-.288L8 2.223l1.847 3.658a.53.53 0 0 0 .393.288l4.052.575-2.906 2.77a.56.56 0 0 0-.163.506l.694 3.957-3.686-1.894a.5.5 0 0 0-.461 0z"/>
</svg> Nâng cấp</a>
            <?php } ?>
								<div class="dropdown-divider"></div>
								
								<a class="dropdown-item" href="<?php echo base_url('login/logout');?>"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" class="bi bi-box-arrow-right align-middle me-1" viewBox="0 0 16 16">
  <path fill-rule="evenodd" d="M10 12.5a.5.5 0 0 1-.5.5h-8a.5.5 0 0 1-.5-.5v-9a.5.5 0 0 1 .5-.5h8a.5.5 0 0 1 .5.5v2a.5.5 0 0 0 1 0v-2A1.5 1.5 0 0 0 9.5 2h-8A1.5 1.5 0 0 0 0 3.5v9A1.5 1.5 0 0 0 1.5 14h8a1.5 1.5 0 0 0 1.5-1.5v-2a.5.5 0 0 0-1 0v2z"/>
  <path fill-rule="evenodd" d="M15.854 8.354a.5.5 0 0 0 0-.708l-3-3a.5.5 0 0 0-.708.708L14.293 7.5H5.5a.5.5 0 0 0 0 1h8.793l-2.147 2.146a.5.5 0 0 0 .708.708l3-3z"/>
</svg> Đăng xuất</a>
							</div>
						</li>


                </ul>
            </div> 
</nav>
<?php if(getenv('SEPAY_ENV') == "STAGING"){ ?>
<div class="alert alert-warning alert-dismissible mb-0">
  <div class="alert-message">
  Bạn đang sử dụng hệ thống DEV. Mọi thông tin trên hệ thống là giả lập.
  </div>
</div>
<?php } ?>
