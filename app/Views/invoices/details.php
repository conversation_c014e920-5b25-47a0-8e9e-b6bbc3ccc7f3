<style>
.copyjs {
    cursor: pointer;
}
</style>
<main class="content">
    <div class="container-fluid">
        <div class="row mx-auto" style="max-width:800px">
            <h1 class="h3 my-3">Hóa đơn #<?= esc($invoice_details->id);?></h1>

            <div class="col-12">
                <div class="card">
                    <div class="card-body m-sm-3 m-md-5">
                        <div class="mb-4">
                            <img src="https://sepay.vn/assets/img/logo/sepay-blue-154x50.png" class="img-fluid">
                        </div>

                        <div class="my-3">
                            <?php if($invoice_details->status == "Paid") { ?>
                            <h1 class="text-center"><span class="badge bg-success rounded-pill">Đã thanh toán</span>
                            </h1>

                            <?php } else if($invoice_details->status == "Unpaid") { ?>
                            <h1 class="text-center"><span class="badge bg-danger  rounded-pill">Ch<PERSON><PERSON> thanh toán</span>
                            </h1>

                            <?php } else if($invoice_details->status == "Cancelled") { ?>
                            <h1 class="text-center"><span class="badge bg-secondary  rounded-pill">Đã hủy</span>
                            </h1>

                            <?php } else if($invoice_details->status == "Refunded") { ?>
                            <h1 class="text-center"><span class="badge bg-secondary  rounded-pill">Đã hoàn tiền</span>
                            </h1>

                            <?php } ?>
                        </div>

                        <div class="row">
                            <div class="col-6">
                                <div class="text-muted">Hóa đơn số</div>
                                <strong>#<?= esc($invoice_details->id);?></strong>
                            </div>
                            <div class="col-6 text-md-right">
                                <div class="text-muted">Ngày tạo</div>
                                <strong><?= esc($invoice_details->date);?></strong>
                            </div>
                        </div>

                        <hr class="my-4" />

                        <div class="row mb-4">
                            <div class="col-8 col-md-6">
                                <div class="text-muted">Bên mua</div>
                                <strong>
                                    <?= esc($company_details->short_name);?>
                                </strong>
                                <p>
                                    <?= esc($company_details->full_name);?>
                                </p>
                            </div>
                            <div class="col-4  col-md-6 text-md-right">
                                <div class="text-muted">Bên bán</div>
                                <strong>
                                    SePay
                                </strong>

                            </div>
                        </div>

                        <?php if($invoice_details->status == "Unpaid"): ?>

                        <div class="p-3 border" id="container-qrcode">
                            <h5 class="text-center">Hướng dẫn thanh toán</h5>
                            <div class="row">
                                <div class="col-md-5 text-center">

                                    <img src="<?= $qrcode;?>" class="img-fluid">
                                    <a href="javascript:void(0)" onclick="downloadQRCode()" class="btn btn-outline-primary btn-sm mt-2">
                                        <i class="bi bi-download"></i> Tải ảnh QR
                                    </a>

                                </div>
                                <div class="col-md-7">
                                    <table class="table table-borderless">
                                        <tbody>
                                            <tr>
                                                <td>Ngân hàng</td>
                                                <td class="fw-bold">MBBank</td>
                                            </tr>
                                            <tr>
                                                <td>Số tài khoản</td>
                                                <td class="fw-bold"><span id="vcb_id">*************</span> <i
                                                        class="bi bi-files copyjs" data-clipboard-target="#vcb_id"
                                                        id="i_vcb_id" data-bs-toggle="tooltip"
                                                        data-bs-title="Đã copy"></i></td>
                                            </tr>
                                            <tr>
                                                <td>Thụ hưởng</td>
                                                <td class="fw-bold">SEPAY JSC</td>
                                            </tr>

                                            <tr>
                                                <td>Nội dung CK</td>
                                                <td class="fw-bold"><span id="trans_content"><?= esc($paycode);?></span> <i class="bi bi-files copyjs" data-clipboard-target="#trans_content"
                                                        id="i_trans_content" data-bs-toggle="tooltip"
                                                        data-bs-title="Đã copy"></i>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>Số tiền</td>
                                                <td class="fw-bold"><?= number_format($invoice_details->total);?> đ
                                                </td>
                                            </tr>
                                            <tr>
                                                <td colspan="2"><i class="bi bi-qr-code-scan"></i> Dùng ứng dụng ngân
                                                    hàng quét mã
                                                    QR để chuyển khoản</td>
                                            </tr>
                                        </tbody>
                                    </table>

                                </div>
                            </div>
                            <div class="alert alert-secondary alert-dismissible" role="alert">
                                <div class="alert-message">
                                    Vui lòng <strong>giữ nguyên nội dung chuyển khoản</strong>. Hệ thống sẽ tự nhận
                                    diện
                                    thanh toán trong vài giây
                                </div>
                            </div>
                        </div>

                        <?php if ($company_details->credit_balance > 0): ?>
                        <?= component('_components/billing/credit-apply-form', ['invoice_details' => $invoice_details, 'company_details' => $company_details]) ?>
                        <?php endif ?>

                        <?php if (pg_billing_enabled()): ?>
                            <?= component('_components/billing/payment-gateway', [
                                'invoice' => $invoice_details,
                                'company' => $company_details,
                                'invoice_items' => $invoice_items
                            ]) ?>
                        <?php endif ?>

                        <?php endif;?>

                        <table class="table table-sm mt-5">
                            <thead>
                                <tr>
                                    <th>Mô tả</th>
                                    <th>Thuế</th>
                                    <th class="text-end">Giá tiền</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach($invoice_items as $item): ?>
                                <tr>
                                    <td><?= esc($item->description);?> <?php if($invoice_details->type=='Excess') {?><br> <a class='ms-2' target='_blank' href='<?= base_url('statistics/counter?period=custom&start_date=' . esc($item->start_date) . '&end_date=' . esc($item->end_date));?>'> <i class="bi bi-chevron-right"></i> Xem thống kê vượt <i class="bi bi-bar-chart-line"></i></a> <?php } ?>
                                        <?php if ($item->details): ?>
                                            <?= $item->details ?>
                                        <?php endif ?>
                                    </td>
                                    <td><?php if($item->taxed == 1) echo 'Có'; else echo 'Không'; ?></td>

                                    <td class="text-end" style="white-space: nowrap;"><?= number_format($item->amount);?> đ</td>
                                </tr>
                                <?php endforeach; ?>

                                
                            </tbody>
                            
                        </table>
                        <div class="row">
                            <div class="col-md-6"></div>
                            <div class="col-md-6">
                                <table class="table">
                                    <tbody>
                                        <tr>
                                            <td class="text-end fw-bold">Tạm tính</td>
                                            <td class="text-end"><?= number_format($invoice_details->subtotal);?> đ</td>
                                            
                                        </tr>
                                        <?php if ($invoice_details->credit): ?>
                                        <tr>
                                            <td class="text-end fw-bold">Tín dụng</td>
                                            <td class="text-end">-<?= number_format($invoice_details->credit) ?> đ</td>
                                        </tr>
                                        <?php endif ?>

                                        <tr>
                                            <td class="text-end  fw-bold">Thuế VAT (<?= number_format($invoice_details->tax_rate);?>%)</td>
                                            <td class="text-end"><?= number_format($invoice_details->tax);?> đ</td>
                                            
                                        </tr>
                                        <tr>
                                            <td class="text-end  fw-bold">Tổng</td>
                                            <td class="text-end  fw-bold"><?= number_format($invoice_details->total);?> đ</td>
                                            
                                        </tr>
                                    </tbody>
                                </table>
                                <?php if ($canRequestVatInvoice): ?>
                                    <?php if (! $invoice_details->vat_invoice_requested_at): ?>
                                        <div class="text-end mt-2">
                                            <button type="button" class="btn btn-outline-primary btn-sm" data-bs-toggle="modal" data-bs-target="#vatNoteModal">
                                                <i class="bi bi-receipt me-1"></i> Yêu cầu xuất hóa đơn VAT
                                            </button>
                                        </div>
                                    <?php elseif ($invoice_details->vat_invoice_requested_at && ! $invoice_details->tax_issued): ?>
                                        <div class="text-warning text-end mb-2">
                                            <i class="bi bi-check-circle me-1"></i> Đã gửi yêu cầu xuất hóa đơn VAT
                                        </div>
                                    <?php elseif ($invoice_details->tax_issued): ?>
                                        <div class="text-success text-end mb-2">
                                            <i class="bi bi-check-circle me-1"></i> Đã xuất hóa đơn VAT
                                        </div>
                                    <?php endif; ?>
                                <?php endif; ?>
                            </div>
                        </div>

                        <div class="row mt-5">
                            <div class="col-6 text-start"><a href="<?= base_url('invoices');?>"><i class="bi bi-chevron-left"></i> Quay lại</a></div>
                            <div class="col-6 text-end">
                                <a onclick="window.print()">
                                <i class="bi bi-printer"></i> In
                            </a></div>
                           
                        </div>
                    </div>
                </div>
            </div>
        </div>


    </div>
</main>

<?php include(APPPATH . 'Views/templates/autopay/inc_footer.php');?>

<script src="<?php echo base_url();?>/assets/js/bootstrap.bundle.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/jquery-3.5.1.js"></script>
<script src="<?php echo base_url();?>/assets/js/jquery.dataTables.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/dataTables.bootstrap5.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/dataTables.rowReorder.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/dataTables.responsive.min.js"></script>
<script src="<?php echo base_url();?>/assets/notyf/notyf.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/app.js?v=1"></script>
<script src="<?php echo base_url();?>/assets/clipboardjs/clipboard.min.js"></script>

<script>
    const vcbTooltip = document.getElementById('i_vcb_id');
    const transTooltip = document.getElementById('i_trans_content');

    if (vcbTooltip) {
        const tooltip = new bootstrap.Tooltip(vcbTooltip);
        tooltip.disable();
    }

    if (transTooltip) {
        const tooltip = new bootstrap.Tooltip(transTooltip);
        tooltip.disable();
    }


var clipboard = new ClipboardJS('.copyjs');

clipboard.on('success', function(e) {


    id = e.trigger.getAttribute('id');

    tooltip = new bootstrap.Tooltip(document.getElementById(id), {
        trigger: 'click'
    });
    tooltip.show();

    setTimeout(function() {
        tooltip.hide();
        tooltip.disable();

    }, 500);
});

<?php if($invoice_details->status == 'Unpaid'): ?>

function check_invoice_status() {
    $.ajax({
        url : "<?= base_url('invoices/ajax_check_status');?>",
        type: "POST",
        data: {invoice_id: <?= $invoice_details->id;?>, "<?php echo csrf_token() ?>": "<?php echo csrf_hash() ?>"},
        dataType: "JSON",
        success: function(data)
        {
            //if success close modal and reload ajax table
            if(data.status == true && data.invoice_status == 'Paid') {
                notyf.success('Thanh toán thành công!');
                location.reload();
            }  
        }
        
    });
}

setInterval( function () {
    check_invoice_status();
}, 3000 );

function downloadQRCode() {
        const qrImageSrc = "<?= $qrcode; ?>";
        if (qrImageSrc) {
            let img = new Image();
            img.crossOrigin = "anonymous";
            img.src = qrImageSrc;

            img.onload = function() {
                let canvas = document.createElement("canvas");
                let ctx = canvas.getContext("2d");

                canvas.width = img.width;
                canvas.height = img.height;

                ctx.drawImage(img, 0, 0);

                canvas.toBlob(blob => {
                    let url = URL.createObjectURL(blob);
                    let a = document.createElement('a');
                    a.href = url;
                    a.download = "qr-invoice-<?= esc($invoice_details->id); ?>.png";
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    URL.revokeObjectURL(url);
                }, "image/png");
            };

            img.onerror = function() {
                notyf.error({
                    message: "Không thể tải hình ảnh QR."
                });
            };
        } else {
            notyf.error({
                message: "Không tìm thấy hình ảnh QR để tải xuống."
            });
        }
    }

<?php endif;?>
</script>

<?php include(APPPATH . 'Views/company/issue-vat-invoice.php'); ?>


<?php if ($invoice_details->status == 'Unpaid' && $company_details->credit_balance > 0): ?>
<?= component_script('_components/billing/credit-apply-form') ?>
<?php endif;?>

<?php if ($invoice_details->status == 'Unpaid' && pg_billing_enabled()): ?>
<?= component_script('_components/billing/payment-gateway') ?>
<?php endif ?>