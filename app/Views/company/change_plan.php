<style>
    .card-top-price {
        border-top-left-radius: 7% 3% !important;
        border-top-right-radius: 7% 3% !important;
        overflow: hidden;
    }

    .dialog__header {
        padding: 1rem clamp(2rem, 3vw, 6rem) 0;
        border-radius: 16px 16px 0 0;
        min-height: 80px;
        text-align: left;
        background-color: var(--modal);
    }

    .dialog__header__title {
        margin-bottom: 1rem;
        font-size: 1rem !important;
        line-height: 4rem;
    }

    .card-top-vpbank {
        background: url('<?= base_url('assets/images/other/vpbank-promotion-header.png') ?>') no-repeat center center;
        background-size: cover;
        height: 3.5rem;
    }
</style>

<?= component_style('company/includes/vpbank-promotion') ?>
<?= component_style('_components/billing/billing-switcher') ?>

<main class="content">
    <div class="container-fluid">

        <?= view('_components/billing/billing-switcher', [
            'defaultBillingType' => $billing_type
        ]) ?>

        <div class="row mt-5">
            <div class="mx-auto">
                <h1 class="text-center">Đ<PERSON>i gói phù hợp với bạn</h1>
                <p class="lead text-center mb-4">Có thể thay đổi bất cứ lúc nào </p>


                <div class="row justify-content-center mt-3 mb-2">
                    <div class="col-auto">
                        <div class="btn-group" role="group" aria-label="Basic radio toggle button group">
                            <input type="radio" class="btn-check" name="btnradio" id="btnradio1" autocomplete="off" checked onclick="updatePriceBox('billing_select', 1, this)" data-discount="0">
                            <label class="btn btn-outline-primary" for="btnradio1">Theo tháng</label>

                            <input type="radio" class="btn-check" name="btnradio" id="btnBillYearly" autocomplete="off" onclick="updatePriceBox('billing_select', 12, this)" data-discount="0.3">
                            <label class="btn btn-outline-primary" for="btnBillYearly">Theo năm <span class="badge bg-primary rounded-pill">-30%</span></label>

                        </div>
                    </div>
                </div>

                <div class="tab-content mx-auto" style="max-width:1200px">
                    <div class="tab-pane fade show active" id="monthly">
                        <div class="swiper plan-swipe py-5 mb-3">
                            <div class="swiper-wrapper">
                                <?php if (is_object($plans_free)) {
                                    $plan = $plans_free;
                                ?>
                                    <div class="swiper-slide">
                                        <div class="card h-100 mb-0 rounded-4 py-0 card-top-price border">
                                            <div class="card-header fw-bold text-center card-top-price" style="background-color: #2196f32e;">MIỄN PHÍ</div>
                                            <div class="card-body d-flex flex-column p-0">
                                                <div class="px-4 pt-4 pb-2">
                                                    <h5 class="card-title"><?= esc(mb_strtoupper($plan->name)); ?></h5>
                                                    <p class="fw-bold h2"><?= number_format($plan->price_monthly); ?>đ <span
                                                            class="fw-normal fs-5 text-muted">/tháng</span></p>

                                                    <div class="mt-3" style="font-size: 14px;">
                                                        <div class="my-2"><svg width="15" height="15" viewBox="0 0 15 15"
                                                                fill="none" xmlns="http://www.w3.org/2000/svg" class="me-2 text-success">
                                                                <path
                                                                    d="M7.49991 0.877045C3.84222 0.877045 0.877075 3.84219 0.877075 7.49988C0.877075 11.1575 3.84222 14.1227 7.49991 14.1227C11.1576 14.1227 14.1227 11.1575 14.1227 7.49988C14.1227 3.84219 11.1576 0.877045 7.49991 0.877045ZM1.82708 7.49988C1.82708 4.36686 4.36689 1.82704 7.49991 1.82704C10.6329 1.82704 13.1727 4.36686 13.1727 7.49988C13.1727 10.6329 10.6329 13.1727 7.49991 13.1727C4.36689 13.1727 1.82708 10.6329 1.82708 7.49988ZM10.1589 5.53774C10.3178 5.31191 10.2636 5.00001 10.0378 4.84109C9.81194 4.68217 9.50004 4.73642 9.34112 4.96225L6.51977 8.97154L5.35681 7.78706C5.16334 7.59002 4.84677 7.58711 4.64973 7.78058C4.45268 7.97404 4.44978 8.29061 4.64325 8.48765L6.22658 10.1003C6.33054 10.2062 6.47617 10.2604 6.62407 10.2483C6.77197 10.2363 6.90686 10.1591 6.99226 10.0377L10.1589 5.53774Z"
                                                                    fill="currentColor" fill-rule="evenodd" clip-rule="evenodd">
                                                                </path>
                                                            </svg> <b><?= esc(number_format($plan->monthly_transaction_limit)); ?></b>
                                                            giao dịch/tháng</div>

                                                        <div class="text-sm text-muted mb-3"><em>(Cho phép vượt giao dịch và tính phí sau vượt)</em> <i class="bi bi-question-circle ms-2" data-bs-toggle="tooltip" data-bs-title="SePay cho phép bạn sử dụng vượt giới hạn số lượng giao dịch cho phép. Cuối kỳ thanh toán, hệ thống sẽ ra hóa đơn để tính phí giao dịch vượt. Giá giao dịch vượt là 700đ/ giao dịch với gói miễn phí. Giao dịch tiền ra sẽ được miễn phí và không tính vào hạn mức giao dịch của gói."></i></div>

                                                        <div class="my-2">
                                                            <svg width="15" height="15" viewBox="0 0 15 15" fill="none"
                                                                xmlns="http://www.w3.org/2000/svg" class="me-2 text-success">
                                                                <path
                                                                    d="M7.49991 0.877045C3.84222 0.877045 0.877075 3.84219 0.877075 7.49988C0.877075 11.1575 3.84222 14.1227 7.49991 14.1227C11.1576 14.1227 14.1227 11.1575 14.1227 7.49988C14.1227 3.84219 11.1576 0.877045 7.49991 0.877045ZM1.82708 7.49988C1.82708 4.36686 4.36689 1.82704 7.49991 1.82704C10.6329 1.82704 13.1727 4.36686 13.1727 7.49988C13.1727 10.6329 10.6329 13.1727 7.49991 13.1727C4.36689 13.1727 1.82708 10.6329 1.82708 7.49988ZM10.1589 5.53774C10.3178 5.31191 10.2636 5.00001 10.0378 4.84109C9.81194 4.68217 9.50004 4.73642 9.34112 4.96225L6.51977 8.97154L5.35681 7.78706C5.16334 7.59002 4.84677 7.58711 4.64973 7.78058C4.45268 7.97404 4.44978 8.29061 4.64325 8.48765L6.22658 10.1003C6.33054 10.2062 6.47617 10.2604 6.62407 10.2483C6.77197 10.2363 6.90686 10.1591 6.99226 10.0377L10.1589 5.53774Z"
                                                                    fill="currentColor" fill-rule="evenodd" clip-rule="evenodd">
                                                                </path>
                                                            </svg> <a data-bs-toggle="modal" data-bs-target="#modalBDSD">Chia sẻ biến động số dư <i class="bi bi-eye float-end text-primary"></i></a>
                                                        </div>
                                                        <div class="my-2">
                                                            <svg width="15" height="15" viewBox="0 0 15 15" fill="none"
                                                                xmlns="http://www.w3.org/2000/svg" class="me-2 text-success">
                                                                <path
                                                                    d="M7.49991 0.877045C3.84222 0.877045 0.877075 3.84219 0.877075 7.49988C0.877075 11.1575 3.84222 14.1227 7.49991 14.1227C11.1576 14.1227 14.1227 11.1575 14.1227 7.49988C14.1227 3.84219 11.1576 0.877045 7.49991 0.877045ZM1.82708 7.49988C1.82708 4.36686 4.36689 1.82704 7.49991 1.82704C10.6329 1.82704 13.1727 4.36686 13.1727 7.49988C13.1727 10.6329 10.6329 13.1727 7.49991 13.1727C4.36689 13.1727 1.82708 10.6329 1.82708 7.49988ZM10.1589 5.53774C10.3178 5.31191 10.2636 5.00001 10.0378 4.84109C9.81194 4.68217 9.50004 4.73642 9.34112 4.96225L6.51977 8.97154L5.35681 7.78706C5.16334 7.59002 4.84677 7.58711 4.64973 7.78058C4.45268 7.97404 4.44978 8.29061 4.64325 8.48765L6.22658 10.1003C6.33054 10.2062 6.47617 10.2604 6.62407 10.2483C6.77197 10.2363 6.90686 10.1591 6.99226 10.0377L10.1589 5.53774Z"
                                                                    fill="currentColor" fill-rule="evenodd" clip-rule="evenodd">
                                                                </path>
                                                            </svg><a data-bs-toggle="modal" data-bs-target="#modalQRCODE"> Cổng thanh toán trực tuyến <i class="bi bi-eye float-end text-primary"></i></a>
                                                        </div>
                                                        <div class="my-2">
                                                            <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg" class="me-2 text-success">
                                                                <path d="M7.49991 0.877045C3.84222 0.877045 0.877075 3.84219 0.877075 7.49988C0.877075 11.1575 3.84222 14.1227 7.49991 14.1227C11.1576 14.1227 14.1227 11.1575 14.1227 7.49988C14.1227 3.84219 11.1576 0.877045 7.49991 0.877045ZM1.82708 7.49988C1.82708 4.36686 4.36689 1.82704 7.49991 1.82704C10.6329 1.82704 13.1727 4.36686 13.1727 7.49988C13.1727 10.6329 10.6329 13.1727 7.49991 13.1727C4.36689 13.1727 1.82708 10.6329 1.82708 7.49988ZM10.1589 5.53774C10.3178 5.31191 10.2636 5.00001 10.0378 4.84109C9.81194 4.68217 9.50004 4.73642 9.34112 4.96225L6.51977 8.97154L5.35681 7.78706C5.16334 7.59002 4.84677 7.58711 4.64973 7.78058C4.45268 7.97404 4.44978 8.29061 4.64325 8.48765L6.22658 10.1003C6.33054 10.2062 6.47617 10.2604 6.62407 10.2483C6.77197 10.2363 6.90686 10.1591 6.99226 10.0377L10.1589 5.53774Z" fill="currentColor" fill-rule="evenodd" clip-rule="evenodd">
                                                                </path>
                                                            </svg><a data-bs-toggle="modal" data-bs-target="#modalAPINH"> Hỗ trợ Webhook, API <i class="bi bi-eye text-primary float-end"></i></a>
                                                        </div>
                                                        <div class="my-2">
                                                            <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg" class="me-2 text-success">
                                                                <path d="M7.49991 0.877045C3.84222 0.877045 0.877075 3.84219 0.877075 7.49988C0.877075 11.1575 3.84222 14.1227 7.49991 14.1227C11.1576 14.1227 14.1227 11.1575 14.1227 7.49988C14.1227 3.84219 11.1576 0.877045 7.49991 0.877045ZM1.82708 7.49988C1.82708 4.36686 4.36689 1.82704 7.49991 1.82704C10.6329 1.82704 13.1727 4.36686 13.1727 7.49988C13.1727 10.6329 10.6329 13.1727 7.49991 13.1727C4.36689 13.1727 1.82708 10.6329 1.82708 7.49988ZM10.1589 5.53774C10.3178 5.31191 10.2636 5.00001 10.0378 4.84109C9.81194 4.68217 9.50004 4.73642 9.34112 4.96225L6.51977 8.97154L5.35681 7.78706C5.16334 7.59002 4.84677 7.58711 4.64973 7.78058C4.45268 7.97404 4.44978 8.29061 4.64325 8.48765L6.22658 10.1003C6.33054 10.2062 6.47617 10.2604 6.62407 10.2483C6.77197 10.2363 6.90686 10.1591 6.99226 10.0377L10.1589 5.53774Z" fill="currentColor" fill-rule="evenodd" clip-rule="evenodd">
                                                                </path>
                                                            </svg> <a data-bs-toggle="modal" data-bs-target="#modalTCTN"> Tất cả tính năng của SePay <i class="bi bi-eye text-primary float-end"></i></a>
                                                        </div>

                                                    </div>
                                                </div>
                                                <div class="text-center mt-2 pb-4 d-grid d-grid px-3">
                                                    <?php if ($plan->id === $subscription_details->plan_id) { ?>
                                                        <a class="btn btn-lg btn-outline-primary disabled">Đang sử dụng</a>

                                                    <?php } else { ?>
                                                        <a href="<?= base_url('company/cart?product_id=' . $plan->id . '&billing_cycle=monthly'); ?>" class="btn btn-lg btn-outline-primary">Chọn</a>
                                                    <?php } ?>
                                                </div>
                                                <div class="border-top p-4">
                                                    <p class="fw-4 h5 mb-3">Ngân hàng hỗ trợ (<?= count($bank_api) ?>)</p>
                                                    <div class="d-flex flex-wrap gap-2">
                                                        <?php foreach ($bank_api as $b) { ?>
                                                            <div class="position-relative">
                                                                <img data-bs-toggle="tooltip" data-bs-title="<?= esc($b->brand_name) ?>" class="rounded-circle border p-1" src="<?php echo base_url(); ?>/assets/images/banklogo/<?= $b->icon_path ?>" style="height:35px; width:35px">
                                                                <span class="position-absolute" data-bs-toggle="tooltip" data-bs-title="Đã ký hợp tác chính thức" style="top: -6px; right: -8px">
                                                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-patch-check-fill text-info" viewBox="0 0 16 16">
                                                                        <path d="M10.067.87a2.89 2.89 0 0 0-4.134 0l-.622.638-.89-.011a2.89 2.89 0 0 0-2.924 2.924l.01.89-.636.622a2.89 2.89 0 0 0 0 4.134l.637.622-.011.89a2.89 2.89 0 0 0 2.924 2.924l.89-.01.622.636a2.89 2.89 0 0 0 4.134 0l.622-.637.89.011a2.89 2.89 0 0 0 2.924-2.924l-.01-.89.636-.622a2.89 2.89 0 0 0 0-4.134l-.637-.622.011-.89a2.89 2.89 0 0 0-2.924-2.924l-.89.01zm.287 5.984-3 3a.5.5 0 0 1-.708 0l-1.5-1.5a.5.5 0 1 1 .708-.708L7 8.793l2.646-2.647a.5.5 0 0 1 .708.708"></path>
                                                                    </svg>
                                                                </span>
                                                            </div>
                                                        <?php } ?>

                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php } ?>
                                <?php if ($vpbankPro): ?>
                                    <div class="swiper-slide">
                                        <div class="card h-100 mb-0 rounded-4 py-0 card-top-price border">
                                            <div class="card-header fw-bold text-center card-top-vpbank"></div>
                                            <div class="card-body d-flex flex-column p-0">
                                                <div class="px-4 pt-4 pb-2">
                                                    <h5 class="card-title"><?= esc(mb_strtoupper($vpbankPro->name)); ?></h5>
                                                    <p class="fw-bold h2">
                                                        <span class="text-success">MIỄN PHÍ</span>
                                                        <span class="fw-normal fs-5 text-muted">/năm</span>
                                                        <span class="text-muted fs-6 mb-3 text-decoration-line-through">
                                                            Trị giá: <?= number_format($vpbankPro->promotion->compare_at_price, 0, ',', '.'); ?>đ
                                                        </span>
                                                    </p>

                                                    <div class="mt-3" style="font-size: 14px;">
                                                        <div class="my-2">
                                                            <i class="bi bi-check-circle text-success me-2"></i>
                                                            <b><?= esc(number_format($vpbankPro->monthly_transaction_limit)); ?></b>
                                                            giao dịch/tháng trong 1 năm
                                                        </div>

                                                        <div class="text-sm text-muted mb-3">
                                                            <em>(Chỉ dành cho tài khoản VPBank cá nhân mở mới)</em>
                                                        </div>

                                                        <div class="my-2">
                                                            <i class="bi bi-check-circle text-success me-2"></i>
                                                            <a data-bs-toggle="modal" data-bs-target="#modalBDSD">Chia sẻ biến động số dư <i class="bi bi-eye float-end text-primary"></i></a>
                                                        </div>
                                                        <div class="my-2">
                                                            <i class="bi bi-check-circle text-success me-2"></i>
                                                            <a data-bs-toggle="modal" data-bs-target="#modalQRCODE"> Cổng thanh toán trực tuyến <i class="bi bi-eye float-end text-primary"></i></a>
                                                        </div>
                                                        <div class="my-2">
                                                            <i class="bi bi-check-circle text-success me-2"></i>
                                                            <a data-bs-toggle="modal" data-bs-target="#modalAPINH"> Hỗ trợ Webhook, API <i class="bi bi-eye text-primary float-end"></i></a>
                                                        </div>
                                                        <div class="my-2">
                                                            <i class="bi bi-check-circle text-success me-2"></i>
                                                            <a data-bs-toggle="modal" data-bs-target="#modalTCTN"> Tất cả tính năng của SePay <i class="bi bi-eye text-primary float-end"></i></a>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="text-center mt-2 pb-4 d-grid d-grid px-3">
                                                    <a
                                                        href="<?= base_url("company/cart?product_id={$vpbankPro->id}&billing_cycle={$vpbankPro->promotion->billing_cycle}&promotion_id={$vpbankPro->promotion->id}"); ?>"
                                                        class="btn btn-lg btn-primary btn-order <?= $vpbankPro->id === $subscription_details->plan_id ? 'disabled' : '' ?>"
                                                        data-plan-id="<?= $vpbankPro->id ?>"
                                                        data-plan-name="<?= esc($vpbankPro->name) ?>"
                                                        data-billing-cycle="<?= $vpbankPro->promotion->billing_cycle ?>"
                                                        data-price="0">
                                                        <?= $vpbankPro->id === $subscription_details->plan_id ? 'Đang sử dụng' : 'Chọn' ?>
                                                    </a>
                                                </div>
                                                <div class="border-top p-4">
                                                    <p class="fw-4 h5 mb-3">Ngân hàng hỗ trợ (1)</p>
                                                    <div class="d-flex flex-wrap gap-2 justify-content-start mb-3">
                                                        <div class="position-relative">
                                                            <img
                                                                data-bs-toggle="tooltip"
                                                                data-bs-title="VPBank"
                                                                class="rounded-circle border p-1"
                                                                src="<?php echo base_url(); ?>/assets/images/banklogo/vpbank-icon.png"
                                                                style="height: 35px; width: 35px;" />
                                                            <span class="position-absolute" data-bs-toggle="tooltip" data-bs-title="Đã ký hợp tác chính thức" style="top: -6px; right: -8px;">
                                                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-patch-check-fill text-info" viewBox="0 0 16 16">
                                                                    <path
                                                                        d="M10.067.87a2.89 2.89 0 0 0-4.134 0l-.622.638-.89-.011a2.89 2.89 0 0 0-2.924 2.924l.01.89-.636.622a2.89 2.89 0 0 0 0 4.134l.637.622-.011.89a2.89 2.89 0 0 0 2.924 2.924l.89-.01.622.636a2.89 2.89 0 0 0 4.134 0l.622-.637.89.011a2.89 2.89 0 0 0 2.924-2.924l-.01-.89.636-.622a2.89 2.89 0 0 0 0-4.134l-.637-.622.011-.89a2.89 2.89 0 0 0-2.924-2.924l-.89.01zm.287 5.984-3 3a.5.5 0 0 1-.708 0l-1.5-1.5a.5.5 0 1 1 .708-.708L7 8.793l2.646-2.647a.5.5 0 0 1 .708.708"></path>
                                                                </svg>
                                                            </span>
                                                        </div>
                                                    </div>
                                                    <div class="mb-1">
                                                        <span class="h5 mb-0">Điều kiện</span>
                                                        <a data-bs-toggle="modal" data-bs-target="#modalGuideVPBank" class="text-primary ms-1">
                                                            <i class="bi bi-info-circle" data-bs-toggle="tooltip" data-bs-title="Xem chi tiết chương trình"></i>
                                                        </a>
                                                    </div>
                                                    <div class="text-sm text-muted">
                                                        <em class="d-block">Mở mới tài khoản VPBank cá nhân, nhập mã giới thiệu <code class="fs-5">SEPAY</code> và liên kết tài khoản VPBank vào SePay.</em>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endif; ?>
                                <?php if ($plans_api) {
                                    $plan = $plans_api[0];
                                ?>
                                    <div class="swiper-slide">
                                        <div class="card h-100 mb-0 rounded-4 py-0 card-top-price border border-success">
                                            <div class="card-header fw-bold py-1 text-center card-top-price bg-success text-white">NGÂN HÀNG HỢP TÁC <br><span><i class="bi bi-check-circle-fill"></i> Tiết kiệm 15%</span></div>
                                            <div class="card-body d-flex flex-column p-0">
                                                <div class="px-4 pt-4 pb-2">
                                                    <h5 class="card-title" id="box-api-name"><?= esc(mb_strtoupper($plan->name)); ?></h5>
                                                    <p class="fw-bold h2"><span class="price_value" data-originalprice="<?php echo $plan->price_monthly; ?>" id="box-api-price"><?= number_format($plan->price_monthly); ?>đ</span> <span
                                                            class="fw-normal fs-5 text-muted">/tháng</span></p>

                                                    <div class="position-relative">
                                                        <div class="d-flex position-absolute" style="right: 0; bottom: 0px;">
                                                            <span class="form-switch-promotion-body ms-auto d-flex flex-column justify-content-end align-items-end">
                                                                <span class="form-switch-promotion-text mb-2">
                                                                    <span class="badge bg-danger rounded-pill ms-1">Bạn cần gói cao hơn?</span>
                                                                </span>
                                                                <svg version="1.1" style="width: 30px; height: 30px; fill: #ddd;" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 302.816 302.816" style="enable-background:new 0 0 302.816 302.816;" xml:space="preserve" transform="matrix(-1,0,0,-1,0,0)">
                                                                    <path id="XMLID_2_" d="M241.053,78.136c0.01-0.224,0.031-0.448,0.03-0.672c0-0.345-0.024-0.686-0.049-1.027
                                        c-0.01-0.141-0.01-0.283-0.023-0.424c-0.032-0.322-0.086-0.638-0.138-0.955c-0.027-0.166-0.045-0.332-0.077-0.496
                                        c-0.054-0.271-0.127-0.535-0.196-0.801c-0.053-0.21-0.1-0.422-0.162-0.631c-0.064-0.21-0.145-0.414-0.217-0.621
                                        c-0.091-0.257-0.176-0.516-0.281-0.769c-0.064-0.157-0.144-0.306-0.213-0.46c-0.133-0.29-0.262-0.581-0.413-0.864
                                        c-0.07-0.131-0.153-0.254-0.227-0.384c-0.166-0.29-0.33-0.581-0.518-0.862c-0.144-0.216-0.307-0.418-0.461-0.626
                                        c-0.134-0.18-0.256-0.365-0.399-0.54c-0.317-0.39-0.655-0.762-1.012-1.119c-0.002-0.002-0.004-0.005-0.006-0.007L174.208,4.393
                                        c-5.857-5.858-15.355-5.858-21.213,0c-5.858,5.857-5.858,15.355,0,21.213l35.13,35.13c-59.791,5.858-111.389,50.117-123.7,112.007
                                        c-4.736,23.817-3.136,48.02,4.125,70.432c0.112,0.348,0.215,0.7,0.331,1.047c6.663,20.032,17.873,38.595,33.157,54.118
                                        c0.614,0.624,1.276,1.167,1.964,1.66c2.601,1.866,5.658,2.816,8.726,2.816c0.475,0,0.95-0.022,1.423-0.067
                                        c3.313-0.314,6.545-1.727,9.101-4.244c5.903-5.813,5.977-15.31,0.164-21.213c-3.604-3.66-6.909-7.542-9.926-11.598
                                        c-18.44-24.791-25.755-56.355-19.64-87.097c9.72-48.866,50.676-83.772,97.979-88.071l-38.835,38.836
                                        c-5.858,5.858-5.858,15.355,0,21.213c2.929,2.929,6.768,4.394,10.607,4.394c3.839,0,7.678-1.464,10.606-4.394l62.41-62.411
                                        c0.022-0.022,0.045-0.046,0.067-0.068l0.007-0.006c0.013-0.013,0.023-0.027,0.036-0.04c0.338-0.339,0.659-0.694,0.963-1.065
                                        c0.088-0.106,0.162-0.219,0.246-0.327c0.217-0.279,0.432-0.559,0.629-0.854c0.115-0.172,0.213-0.351,0.32-0.527
                                        c0.146-0.24,0.296-0.476,0.429-0.725c0.125-0.234,0.232-0.475,0.343-0.713c0.095-0.202,0.195-0.399,0.281-0.606
                                        c0.128-0.308,0.233-0.622,0.34-0.936c0.051-0.15,0.11-0.295,0.156-0.448c0.128-0.421,0.231-0.847,0.321-1.275
                                        c0.012-0.055,0.03-0.107,0.041-0.163c0.001-0.004,0.001-0.009,0.002-0.013c0.097-0.492,0.171-0.987,0.218-1.484
                                        C241.041,78.652,241.041,78.394,241.053,78.136z" />
                                                                </svg>
                                                            </span>
                                                        </div>
                                                    </div>

                                                    <div class="mt-3" style="font-size: 14px;">

                                                        <div class="my-2"> <select name="api_plan_select" id="api_plan_select" class="form-select" onchange="updatePriceBox('api')">

                                                                <?php foreach ($plans_api as $plan_select) { ?>
                                                                    <option value="<?php echo $plan_select->id; ?>" data-price="<?php echo $plan_select->price_monthly; ?>" data-transactions="<?php echo $plan_select->monthly_transaction_limit; ?>" <?php if ($plan_select->id === $subscription_details->plan_id)  echo 'selected'; ?> data-plan='<?php echo $plan_select->name; ?>'><?= esc(number_format($plan_select->monthly_transaction_limit)); ?> giao dịch/ tháng <?php if ($plan_select->id === $subscription_details->plan_id) echo '(Hiện tại)'; ?></option>

                                                                <?php } ?>
                                                            </select></div>

                                                        <div class="text-muted text-sm mt-2"><em>(Cho phép vượt giao dịch và tính phí sau vượt)</em> <i class="bi bi-question-circle ms-2" data-bs-toggle="tooltip" data-bs-title="SePay cho phép bạn sử dụng vượt giới hạn số lượng giao dịch cho phép. Cuối kỳ thanh toán, hệ thống sẽ ra hóa đơn để tính phí giao dịch vượt. Giá mỗi giao dịch vượt tương ứng với giá tiền mỗi giao dịch của gói đó. Giao dịch tiền ra sẽ được miễn phí và không tính vào hạn mức giao dịch của gói."></i></div>

                                                        <div class="my-2 mt-4">
                                                            <svg width="15" height="15" viewBox="0 0 15 15" fill="none"
                                                                xmlns="http://www.w3.org/2000/svg" class="me-2 text-success">
                                                                <path
                                                                    d="M7.49991 0.877045C3.84222 0.877045 0.877075 3.84219 0.877075 7.49988C0.877075 11.1575 3.84222 14.1227 7.49991 14.1227C11.1576 14.1227 14.1227 11.1575 14.1227 7.49988C14.1227 3.84219 11.1576 0.877045 7.49991 0.877045ZM1.82708 7.49988C1.82708 4.36686 4.36689 1.82704 7.49991 1.82704C10.6329 1.82704 13.1727 4.36686 13.1727 7.49988C13.1727 10.6329 10.6329 13.1727 7.49991 13.1727C4.36689 13.1727 1.82708 10.6329 1.82708 7.49988ZM10.1589 5.53774C10.3178 5.31191 10.2636 5.00001 10.0378 4.84109C9.81194 4.68217 9.50004 4.73642 9.34112 4.96225L6.51977 8.97154L5.35681 7.78706C5.16334 7.59002 4.84677 7.58711 4.64973 7.78058C4.45268 7.97404 4.44978 8.29061 4.64325 8.48765L6.22658 10.1003C6.33054 10.2062 6.47617 10.2604 6.62407 10.2483C6.77197 10.2363 6.90686 10.1591 6.99226 10.0377L10.1589 5.53774Z"
                                                                    fill="currentColor" fill-rule="evenodd" clip-rule="evenodd">
                                                                </path>
                                                            </svg> <a data-bs-toggle="modal" data-bs-target="#modalBDSD">Chia sẻ biến động số dư <i class="bi bi-eye float-end text-primary"></i></a>
                                                        </div>
                                                        <div class="my-2">
                                                            <svg width="15" height="15" viewBox="0 0 15 15" fill="none"
                                                                xmlns="http://www.w3.org/2000/svg" class="me-2 text-success">
                                                                <path
                                                                    d="M7.49991 0.877045C3.84222 0.877045 0.877075 3.84219 0.877075 7.49988C0.877075 11.1575 3.84222 14.1227 7.49991 14.1227C11.1576 14.1227 14.1227 11.1575 14.1227 7.49988C14.1227 3.84219 11.1576 0.877045 7.49991 0.877045ZM1.82708 7.49988C1.82708 4.36686 4.36689 1.82704 7.49991 1.82704C10.6329 1.82704 13.1727 4.36686 13.1727 7.49988C13.1727 10.6329 10.6329 13.1727 7.49991 13.1727C4.36689 13.1727 1.82708 10.6329 1.82708 7.49988ZM10.1589 5.53774C10.3178 5.31191 10.2636 5.00001 10.0378 4.84109C9.81194 4.68217 9.50004 4.73642 9.34112 4.96225L6.51977 8.97154L5.35681 7.78706C5.16334 7.59002 4.84677 7.58711 4.64973 7.78058C4.45268 7.97404 4.44978 8.29061 4.64325 8.48765L6.22658 10.1003C6.33054 10.2062 6.47617 10.2604 6.62407 10.2483C6.77197 10.2363 6.90686 10.1591 6.99226 10.0377L10.1589 5.53774Z"
                                                                    fill="currentColor" fill-rule="evenodd" clip-rule="evenodd">
                                                                </path>
                                                            </svg><a data-bs-toggle="modal" data-bs-target="#modalQRCODE"> Cổng thanh toán trực tuyến <i class="bi bi-eye float-end text-primary"></i></a>
                                                        </div>
                                                        <div class="my-2">
                                                            <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg" class="me-2 text-success">
                                                                <path d="M7.49991 0.877045C3.84222 0.877045 0.877075 3.84219 0.877075 7.49988C0.877075 11.1575 3.84222 14.1227 7.49991 14.1227C11.1576 14.1227 14.1227 11.1575 14.1227 7.49988C14.1227 3.84219 11.1576 0.877045 7.49991 0.877045ZM1.82708 7.49988C1.82708 4.36686 4.36689 1.82704 7.49991 1.82704C10.6329 1.82704 13.1727 4.36686 13.1727 7.49988C13.1727 10.6329 10.6329 13.1727 7.49991 13.1727C4.36689 13.1727 1.82708 10.6329 1.82708 7.49988ZM10.1589 5.53774C10.3178 5.31191 10.2636 5.00001 10.0378 4.84109C9.81194 4.68217 9.50004 4.73642 9.34112 4.96225L6.51977 8.97154L5.35681 7.78706C5.16334 7.59002 4.84677 7.58711 4.64973 7.78058C4.45268 7.97404 4.44978 8.29061 4.64325 8.48765L6.22658 10.1003C6.33054 10.2062 6.47617 10.2604 6.62407 10.2483C6.77197 10.2363 6.90686 10.1591 6.99226 10.0377L10.1589 5.53774Z" fill="currentColor" fill-rule="evenodd" clip-rule="evenodd">
                                                                </path>
                                                            </svg><a data-bs-toggle="modal" data-bs-target="#modalAPINH"> Hỗ trợ Webhook, API <i class="bi bi-eye text-primary float-end"></i></a>
                                                        </div>
                                                        <div class="my-2">
                                                            <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg" class="me-2 text-success">
                                                                <path d="M7.49991 0.877045C3.84222 0.877045 0.877075 3.84219 0.877075 7.49988C0.877075 11.1575 3.84222 14.1227 7.49991 14.1227C11.1576 14.1227 14.1227 11.1575 14.1227 7.49988C14.1227 3.84219 11.1576 0.877045 7.49991 0.877045ZM1.82708 7.49988C1.82708 4.36686 4.36689 1.82704 7.49991 1.82704C10.6329 1.82704 13.1727 4.36686 13.1727 7.49988C13.1727 10.6329 10.6329 13.1727 7.49991 13.1727C4.36689 13.1727 1.82708 10.6329 1.82708 7.49988ZM10.1589 5.53774C10.3178 5.31191 10.2636 5.00001 10.0378 4.84109C9.81194 4.68217 9.50004 4.73642 9.34112 4.96225L6.51977 8.97154L5.35681 7.78706C5.16334 7.59002 4.84677 7.58711 4.64973 7.78058C4.45268 7.97404 4.44978 8.29061 4.64325 8.48765L6.22658 10.1003C6.33054 10.2062 6.47617 10.2604 6.62407 10.2483C6.77197 10.2363 6.90686 10.1591 6.99226 10.0377L10.1589 5.53774Z" fill="currentColor" fill-rule="evenodd" clip-rule="evenodd">
                                                                </path>
                                                            </svg> <a data-bs-toggle="modal" data-bs-target="#modalTCTN"> Tất cả tính năng của SePay <i class="bi bi-eye text-primary float-end"></i></a>
                                                        </div>


                                                    </div>
                                                </div>
                                                <div class="text-center mt-2 pb-4 d-grid d-grid px-3">
                                                    <a id="box-api-order" href="<?= base_url('company/cart?product_id=' . $plan->id . '&billing_cycle=monthly'); ?>"
                                                        class="btn btn-lg btn-primary btn-order">Chọn</a>
                                                </div>
                                                <div class="border-top p-4">
                                                    <p class="fw-4 h5 mb-3">Ngân hàng hỗ trợ (<?= count($bank_api) ?>)</p>
                                                    <div class="d-flex flex-wrap gap-2">

                                                        <?php foreach ($bank_api as $b) { ?>

                                                            <div class="position-relative">
                                                                <img data-bs-toggle="tooltip" data-bs-title="<?= esc($b->brand_name) ?>" class="rounded-circle border p-1" src="<?php echo base_url(); ?>/assets/images/banklogo/<?= esc($b->icon_path) ?>" style="height:35px; width:35px">
                                                                <span class="position-absolute" data-bs-toggle="tooltip" data-bs-title="Đã ký hợp tác chính thức" style="top: -6px; right: -8px">
                                                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-patch-check-fill text-info" viewBox="0 0 16 16">
                                                                        <path d="M10.067.87a2.89 2.89 0 0 0-4.134 0l-.622.638-.89-.011a2.89 2.89 0 0 0-2.924 2.924l.01.89-.636.622a2.89 2.89 0 0 0 0 4.134l.637.622-.011.89a2.89 2.89 0 0 0 2.924 2.924l.89-.01.622.636a2.89 2.89 0 0 0 4.134 0l.622-.637.89.011a2.89 2.89 0 0 0 2.924-2.924l-.01-.89.636-.622a2.89 2.89 0 0 0 0-4.134l-.637-.622.011-.89a2.89 2.89 0 0 0-2.924-2.924l-.89.01zm.287 5.984-3 3a.5.5 0 0 1-.708 0l-1.5-1.5a.5.5 0 1 1 .708-.708L7 8.793l2.646-2.647a.5.5 0 0 1 .708.708"></path>
                                                                    </svg>
                                                                </span>
                                                            </div>
                                                        <?php } ?>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php } ?>
                                <?php if ($plans_sms) {
                                    $plan = $plans_sms[0];
                                ?>
                                    <div class="swiper-slide">
                                        <div class="card h-100 mb-0 rounded-4 py-0 card-top-price border border-primary">
                                            <div class="card-header fw-bold text-center card-top-price bg-info text-white">TẤT CẢ NGÂN HÀNG</div>
                                            <div class="card-body d-flex flex-column p-0">
                                                <div class="px-4 pt-4 pb-2">
                                                    <h5 class="card-title" id="box-sms-name"><?= esc(mb_strtoupper($plan->name)); ?></h5>
                                                    <p class="fw-bold h2"><span id="box-sms-price" class="price_value" data-originalprice="<?php echo $plan->price_monthly; ?>"><?= number_format($plan->price_monthly); ?>đ</span> <span
                                                            class="fw-normal fs-5 text-muted">/tháng</span></p>

                                                    <div class="position-relative">
                                                        <div class="d-flex position-absolute" style="right: 0; bottom: 0px;">
                                                            <span class="form-switch-promotion-body ms-auto d-flex flex-column justify-content-end align-items-end">
                                                                <span class="form-switch-promotion-text mb-2">
                                                                    <span class="badge bg-danger rounded-pill ms-1">Bạn cần gói cao hơn?</span>
                                                                </span>
                                                                <svg version="1.1" style="width: 30px; height: 30px; fill: #ddd;" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 302.816 302.816" style="enable-background:new 0 0 302.816 302.816;" xml:space="preserve" transform="matrix(-1,0,0,-1,0,0)">
                                                                    <path id="XMLID_2_" d="M241.053,78.136c0.01-0.224,0.031-0.448,0.03-0.672c0-0.345-0.024-0.686-0.049-1.027
                                        c-0.01-0.141-0.01-0.283-0.023-0.424c-0.032-0.322-0.086-0.638-0.138-0.955c-0.027-0.166-0.045-0.332-0.077-0.496
                                        c-0.054-0.271-0.127-0.535-0.196-0.801c-0.053-0.21-0.1-0.422-0.162-0.631c-0.064-0.21-0.145-0.414-0.217-0.621
                                        c-0.091-0.257-0.176-0.516-0.281-0.769c-0.064-0.157-0.144-0.306-0.213-0.46c-0.133-0.29-0.262-0.581-0.413-0.864
                                        c-0.07-0.131-0.153-0.254-0.227-0.384c-0.166-0.29-0.33-0.581-0.518-0.862c-0.144-0.216-0.307-0.418-0.461-0.626
                                        c-0.134-0.18-0.256-0.365-0.399-0.54c-0.317-0.39-0.655-0.762-1.012-1.119c-0.002-0.002-0.004-0.005-0.006-0.007L174.208,4.393
                                        c-5.857-5.858-15.355-5.858-21.213,0c-5.858,5.857-5.858,15.355,0,21.213l35.13,35.13c-59.791,5.858-111.389,50.117-123.7,112.007
                                        c-4.736,23.817-3.136,48.02,4.125,70.432c0.112,0.348,0.215,0.7,0.331,1.047c6.663,20.032,17.873,38.595,33.157,54.118
                                        c0.614,0.624,1.276,1.167,1.964,1.66c2.601,1.866,5.658,2.816,8.726,2.816c0.475,0,0.95-0.022,1.423-0.067
                                        c3.313-0.314,6.545-1.727,9.101-4.244c5.903-5.813,5.977-15.31,0.164-21.213c-3.604-3.66-6.909-7.542-9.926-11.598
                                        c-18.44-24.791-25.755-56.355-19.64-87.097c9.72-48.866,50.676-83.772,97.979-88.071l-38.835,38.836
                                        c-5.858,5.858-5.858,15.355,0,21.213c2.929,2.929,6.768,4.394,10.607,4.394c3.839,0,7.678-1.464,10.606-4.394l62.41-62.411
                                        c0.022-0.022,0.045-0.046,0.067-0.068l0.007-0.006c0.013-0.013,0.023-0.027,0.036-0.04c0.338-0.339,0.659-0.694,0.963-1.065
                                        c0.088-0.106,0.162-0.219,0.246-0.327c0.217-0.279,0.432-0.559,0.629-0.854c0.115-0.172,0.213-0.351,0.32-0.527
                                        c0.146-0.24,0.296-0.476,0.429-0.725c0.125-0.234,0.232-0.475,0.343-0.713c0.095-0.202,0.195-0.399,0.281-0.606
                                        c0.128-0.308,0.233-0.622,0.34-0.936c0.051-0.15,0.11-0.295,0.156-0.448c0.128-0.421,0.231-0.847,0.321-1.275
                                        c0.012-0.055,0.03-0.107,0.041-0.163c0.001-0.004,0.001-0.009,0.002-0.013c0.097-0.492,0.171-0.987,0.218-1.484
                                        C241.041,78.652,241.041,78.394,241.053,78.136z" />
                                                                </svg>
                                                            </span>
                                                        </div>
                                                    </div>

                                                    <div class="mt-3" style="font-size: 14px;">

                                                        <div class="my-2">
                                                            <select class="form-select" name="sms_plan_select" id="sms_plan_select" onchange="updatePriceBox('sms')">

                                                                <?php foreach ($plans_sms as $plan_select) { ?>
                                                                    <option value="<?php echo $plan_select->id; ?>" data-price="<?php echo $plan_select->price_monthly; ?>" data-transactions="<?php echo $plan_select->monthly_transaction_limit; ?>" <?php if ($plan_select->id == $subscription_details->plan_id && $subscription_details == 'annually') echo 'selected'; ?> data-plan='<?php echo $plan_select->name; ?>'><?= esc(number_format($plan_select->monthly_transaction_limit)); ?> giao dịch/ tháng</option>
                                                                <?php } ?>
                                                            </select>
                                                        </div>

                                                        <div class="text-muted text-sm mt-2"><em>(Cho phép vượt giao dịch và tính phí sau vượt)</em> <i class="bi bi-question-circle ms-2" data-bs-toggle="tooltip" data-bs-title="SePay cho phép bạn sử dụng vượt giới hạn số lượng giao dịch cho phép. Cuối kỳ thanh toán, hệ thống sẽ ra hóa đơn để tính phí giao dịch vượt. Giá mỗi giao dịch vượt tương ứng với giá tiền mỗi giao dịch của gói đó. Giao dịch tiền ra sẽ được miễn phí và không tính vào hạn mức giao dịch của gói." data-bs-original-title="" title=""></i></div>

                                                        <div class="my-2 mt-4">
                                                            <svg width="15" height="15" viewBox="0 0 15 15" fill="none"
                                                                xmlns="http://www.w3.org/2000/svg" class="me-2 text-success">
                                                                <path
                                                                    d="M7.49991 0.877045C3.84222 0.877045 0.877075 3.84219 0.877075 7.49988C0.877075 11.1575 3.84222 14.1227 7.49991 14.1227C11.1576 14.1227 14.1227 11.1575 14.1227 7.49988C14.1227 3.84219 11.1576 0.877045 7.49991 0.877045ZM1.82708 7.49988C1.82708 4.36686 4.36689 1.82704 7.49991 1.82704C10.6329 1.82704 13.1727 4.36686 13.1727 7.49988C13.1727 10.6329 10.6329 13.1727 7.49991 13.1727C4.36689 13.1727 1.82708 10.6329 1.82708 7.49988ZM10.1589 5.53774C10.3178 5.31191 10.2636 5.00001 10.0378 4.84109C9.81194 4.68217 9.50004 4.73642 9.34112 4.96225L6.51977 8.97154L5.35681 7.78706C5.16334 7.59002 4.84677 7.58711 4.64973 7.78058C4.45268 7.97404 4.44978 8.29061 4.64325 8.48765L6.22658 10.1003C6.33054 10.2062 6.47617 10.2604 6.62407 10.2483C6.77197 10.2363 6.90686 10.1591 6.99226 10.0377L10.1589 5.53774Z"
                                                                    fill="currentColor" fill-rule="evenodd" clip-rule="evenodd">
                                                                </path>
                                                            </svg> <a data-bs-toggle="modal" data-bs-target="#modalBDSD">Chia sẻ biến động số dư <i class="bi bi-eye float-end text-primary"></i></a>
                                                        </div>
                                                        <div class="my-2">
                                                            <svg width="15" height="15" viewBox="0 0 15 15" fill="none"
                                                                xmlns="http://www.w3.org/2000/svg" class="me-2 text-success">
                                                                <path
                                                                    d="M7.49991 0.877045C3.84222 0.877045 0.877075 3.84219 0.877075 7.49988C0.877075 11.1575 3.84222 14.1227 7.49991 14.1227C11.1576 14.1227 14.1227 11.1575 14.1227 7.49988C14.1227 3.84219 11.1576 0.877045 7.49991 0.877045ZM1.82708 7.49988C1.82708 4.36686 4.36689 1.82704 7.49991 1.82704C10.6329 1.82704 13.1727 4.36686 13.1727 7.49988C13.1727 10.6329 10.6329 13.1727 7.49991 13.1727C4.36689 13.1727 1.82708 10.6329 1.82708 7.49988ZM10.1589 5.53774C10.3178 5.31191 10.2636 5.00001 10.0378 4.84109C9.81194 4.68217 9.50004 4.73642 9.34112 4.96225L6.51977 8.97154L5.35681 7.78706C5.16334 7.59002 4.84677 7.58711 4.64973 7.78058C4.45268 7.97404 4.44978 8.29061 4.64325 8.48765L6.22658 10.1003C6.33054 10.2062 6.47617 10.2604 6.62407 10.2483C6.77197 10.2363 6.90686 10.1591 6.99226 10.0377L10.1589 5.53774Z"
                                                                    fill="currentColor" fill-rule="evenodd" clip-rule="evenodd">
                                                                </path>
                                                            </svg><a data-bs-toggle="modal" data-bs-target="#modalQRCODE"> Cổng thanh toán trực tuyến <i class="bi bi-eye float-end text-primary"></i></a>
                                                        </div>
                                                        <div class="my-2">
                                                            <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg" class="me-2 text-success">
                                                                <path d="M7.49991 0.877045C3.84222 0.877045 0.877075 3.84219 0.877075 7.49988C0.877075 11.1575 3.84222 14.1227 7.49991 14.1227C11.1576 14.1227 14.1227 11.1575 14.1227 7.49988C14.1227 3.84219 11.1576 0.877045 7.49991 0.877045ZM1.82708 7.49988C1.82708 4.36686 4.36689 1.82704 7.49991 1.82704C10.6329 1.82704 13.1727 4.36686 13.1727 7.49988C13.1727 10.6329 10.6329 13.1727 7.49991 13.1727C4.36689 13.1727 1.82708 10.6329 1.82708 7.49988ZM10.1589 5.53774C10.3178 5.31191 10.2636 5.00001 10.0378 4.84109C9.81194 4.68217 9.50004 4.73642 9.34112 4.96225L6.51977 8.97154L5.35681 7.78706C5.16334 7.59002 4.84677 7.58711 4.64973 7.78058C4.45268 7.97404 4.44978 8.29061 4.64325 8.48765L6.22658 10.1003C6.33054 10.2062 6.47617 10.2604 6.62407 10.2483C6.77197 10.2363 6.90686 10.1591 6.99226 10.0377L10.1589 5.53774Z" fill="currentColor" fill-rule="evenodd" clip-rule="evenodd">
                                                                </path>
                                                            </svg><a data-bs-toggle="modal" data-bs-target="#modalAPINH"> Hỗ trợ Webhook, API <i class="bi bi-eye text-primary float-end"></i></a>
                                                        </div>
                                                        <div class="my-2">
                                                            <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg" class="me-2 text-success">
                                                                <path d="M7.49991 0.877045C3.84222 0.877045 0.877075 3.84219 0.877075 7.49988C0.877075 11.1575 3.84222 14.1227 7.49991 14.1227C11.1576 14.1227 14.1227 11.1575 14.1227 7.49988C14.1227 3.84219 11.1576 0.877045 7.49991 0.877045ZM1.82708 7.49988C1.82708 4.36686 4.36689 1.82704 7.49991 1.82704C10.6329 1.82704 13.1727 4.36686 13.1727 7.49988C13.1727 10.6329 10.6329 13.1727 7.49991 13.1727C4.36689 13.1727 1.82708 10.6329 1.82708 7.49988ZM10.1589 5.53774C10.3178 5.31191 10.2636 5.00001 10.0378 4.84109C9.81194 4.68217 9.50004 4.73642 9.34112 4.96225L6.51977 8.97154L5.35681 7.78706C5.16334 7.59002 4.84677 7.58711 4.64973 7.78058C4.45268 7.97404 4.44978 8.29061 4.64325 8.48765L6.22658 10.1003C6.33054 10.2062 6.47617 10.2604 6.62407 10.2483C6.77197 10.2363 6.90686 10.1591 6.99226 10.0377L10.1589 5.53774Z" fill="currentColor" fill-rule="evenodd" clip-rule="evenodd">
                                                                </path>
                                                            </svg> <a data-bs-toggle="modal" data-bs-target="#modalTCTN"> Tất cả tính năng của SePay <i class="bi bi-eye text-primary float-end"></i></a>
                                                        </div>


                                                    </div>
                                                </div>
                                                <div class="text-center mt-2 pb-4 d-grid d-grid px-3">
                                                    <?php if ($subscription_details->plan_id == $plan->id && $subscription_details->billing_cycle == 'annually'): ?>
                                                        <a id="box-sms-order" href="#" class="btn btn-lg btn-secondary btn-order">Đang sử dụng</a>
                                                    <?php else: ?>
                                                        <a id="box-sms-order" href="<?= base_url('company/cart?product_id=' . $plan->id . '&billing_cycle=monthly'); ?>"
                                                            class="btn btn-lg btn-primary btn-order">Chọn</a>
                                                    <?php endif ?>
                                                </div>
                                                <div class="border-top p-4">
                                                    <p class="fw-4 h5 mb-3">Ngân hàng hỗ trợ
                                                        (<?php echo ($plan->sms_allow == 1) ? (count($bank_sms) + count($bank_api)) : 30; ?>)</p>
                                                    <div class="d-flex flex-wrap gap-2">

                                                        <?php foreach ($bank_api as $b) { ?>
                                                            <div class="position-relative">
                                                                <img data-bs-toggle="tooltip" data-bs-title="<?= esc($b->brand_name) ?>" class="rounded-circle border p-1" src="<?php echo base_url(); ?>/assets/images/banklogo/<?= esc($b->icon_path) ?>" style="height:35px; width:35px">
                                                                <span class="position-absolute" data-bs-toggle="tooltip" data-bs-title="Đã ký hợp tác chính thức" style="top: -6px; right: -8px">
                                                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-patch-check-fill text-info" viewBox="0 0 16 16">
                                                                        <path d="M10.067.87a2.89 2.89 0 0 0-4.134 0l-.622.638-.89-.011a2.89 2.89 0 0 0-2.924 2.924l.01.89-.636.622a2.89 2.89 0 0 0 0 4.134l.637.622-.011.89a2.89 2.89 0 0 0 2.924 2.924l.89-.01.622.636a2.89 2.89 0 0 0 4.134 0l.622-.637.89.011a2.89 2.89 0 0 0 2.924-2.924l-.01-.89.636-.622a2.89 2.89 0 0 0 0-4.134l-.637-.622.011-.89a2.89 2.89 0 0 0-2.924-2.924l-.89.01zm.287 5.984-3 3a.5.5 0 0 1-.708 0l-1.5-1.5a.5.5 0 1 1 .708-.708L7 8.793l2.646-2.647a.5.5 0 0 1 .708.708"></path>
                                                                    </svg>
                                                                </span>
                                                            </div>
                                                        <?php } ?>


                                                        <?php if ($plan->sms_allow == 1) { ?>
                                                            <?php foreach ($bank_sms as $bank) { ?>
                                                                <img data-bs-toggle="tooltip"
                                                                    data-bs-title="<?php echo $bank->brand_name; ?>"
                                                                    class="rounded-circle border p-1"
                                                                    src="<?php echo base_url(); ?>/assets/images/banklogo/<?php echo $bank->icon_path; ?>"
                                                                    style="height:35px; width:35px">
                                                            <?php } ?>

                                                        <?php } ?>

                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php } ?>
                            </div>
                            <div class="swiper-button-next d-none d-md-block"></div>
                            <div class="swiper-button-prev d-none d-md-block"></div>
                            <div class="swiper-pagination d-none d-md-block"></div>
                        </div>
                    </div>
                </div>

                <div class="py-3">
                    <p class="text-center h4">Bạn muốn tư vấn? Hãy liên hệ ngay với chúng tôi
                    <p>
                    <p class="text-center fs-4"><i class="bi bi-telephone"></i> <a href="tel:0328991318">02873.059.589</a> | Chat <i class="bi bi-messenger"></i> <a target="_blank" href="m.me/117903214582465">fb.me/sepay.vn</a> | Mở <a href="<?= base_url('ticket/create'); ?>">yêu cầu hỗ trợ</a>

                    </p>
                </div>

                <div class="text-center">
                    <h1 class="mt-2 mb-5">SePay sẽ giúp bạn</h1>

                    <div class="">
                        <div class="row mx-auto" style="max-width:1200px">
                            <div class="col-md-4">
                                <div class="card">
                                    <img src="<?php echo base_url('assets/images/plans/chia-se-bien-dong-so-du.jpg'); ?>" class="card-img-top" alt="...">
                                    <div class="card-body">
                                        <h5 class="card-title">Chia sẻ biến động số dư</h5>
                                        <p class="card-text">Chia sẻ thông tin giao dịch lên nhóm chat, nhân viên bán hàng nắm bắt thanh toán kịp thời.</p>
                                        <a class="btn btn-outline-primary btn-sm" data-bs-toggle="modal" data-bs-target="#modalBDSD">Xem thêm</a>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card">
                                    <img src="<?php echo base_url('assets/images/plans/cong-thanh-toan-qr.jpg'); ?>" class="card-img-top">
                                    <div class="card-body">
                                        <h5 class="card-title">Tích hợp cổng thanh toán trực tuyến</h5>
                                        <p class="card-text">Tích hợp cổng thanh toán vào website bán hàng của bạn trong 15 phút.</p>
                                        <a data-bs-toggle="modal" data-bs-target="#modalQRCODE" class="btn btn-outline-primary btn-sm">Xem thêm</a>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card">
                                    <img src="<?php echo base_url('assets/images/plans/thong-ke-dong-tien.jpg'); ?>" class="card-img-top" alt="...">
                                    <div class="card-body">
                                        <h5 class="card-title">Thống kê dòng tiền</h5>
                                        <p class="card-text">Xem danh sách giao dịch. Báo cáo dòng tiền vào ra. Tất cả ngân hàng trong 1 giao diện.</p>
                                        <a data-bs-toggle="modal" data-bs-target="#modalTKDT" class="btn btn-outline-primary btn-sm">Xem thêm</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>


                <div class="text-center mt-5 ">
                    <h1 class="mb-3">SePay phù hợp với</h1>

                    <div class="">
                        <div class="row mx-auto" style="max-width:1200px">
                            <div class="col-md-4">
                                <div class="card">
                                    <img src="<?php echo base_url('assets/images/plans/chuoi-cua-hang.jpg'); ?>" class="card-img-top" alt="...">
                                    <div class="card-body">
                                        <h5 class="card-title">Chuỗi cửa hàng</h5>
                                        <p class="card-text">Gửi thông báo thanh toán tức thì đến nhóm chat bán hàng. Phân biệt doanh thu từng chi nhánh. Ẩn thông tin số dư tài khoản.</p>
                                        <a data-bs-toggle="modal" data-bs-target="#modalCCH" class="btn btn-outline-primary btn-sm">Xem thêm</a>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card">
                                    <img src="<?php echo base_url('assets/images/plans/web-app-tich-hop.jpg'); ?>" class="card-img-top" alt="...">
                                    <div class="card-body">
                                        <h5 class="card-title">Website & app bán hàng</h5>
                                        <p class="card-text">Tích hợp SePay làm cổng thanh toán chuyển khoản, tự động hoá nạp tiền, tăng trải nghiệm khách hàng.</p>
                                        <a data-bs-toggle="modal" data-bs-target="#modalWEBAPPBH" class="btn btn-outline-primary btn-sm">Xem thêm</a>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card">
                                    <img src="<?php echo base_url('assets/images/plans/api-ngan-hang.jpg'); ?>" class="card-img-top" alt="...">
                                    <div class="card-body">
                                        <h5 class="card-title">Cần API Ngân hàng</h5>
                                        <p class="card-text">Đồng bộ giao dịch ngân hàng đến ứng dụng của bạn thông qua API và Webhook của SePay. Đầy đủ tài liệu và sandbox.</p>
                                        <a data-bs-toggle="modal" data-bs-target="#modalAPINH" class="btn btn-outline-primary btn-sm">Xem thêm</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <hr />

                <div class="text-center my-4">
                    <h2>Câu hỏi thường gặp</h2>
                </div>
                <div class="row mx-auto" style="max-width:1200px">

                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="h6 card-title">Tôi muốn sử dụng các ngân hàng mà SePay đã hợp tác chiến lược. Nhưng tôi chưa có tài khoản Ngân hàng thì phải làm sao?</h5>
                                <p class="mb-0">Bạn có thể mở tài khoản ngân hàng online qua App. Vui lòng xem hướng dẫn sau: <br><a href="https://sepay.vn/blog/huong-dan-mo-tai-khoan-ngan-hang-mbbank-online-moi-nhat/" target="_blank">Hướng dẫn mở tài khoản ngân hàng MB Bank online</a><br><a href="https://youtu.be/BDFBcWekXs4?si=NqtukeB8dgK5YTg2" target="_blank">Video hướng dẫn mở tài khoản OCB qua app OCB OMNI</a><br><a href="https://youtu.be/h4QrKbrtckw?si=Hj_FI-DoEJHqGOFt" target="_blank">Video hướng dẫn liên kết tài khoản ngân hàng OCB vào SePay</a><br><a href="https://youtu.be/h4QrKbrtckw?si=33SznXSn7CeCgfFz" target="_blank">Video hướng dẫn mở tài khoản KienLongBank qua App</a><br><a href="https://www.youtube.com/watch?v=UNNBAXhliE8" target="_blank">Video hướng dẫn liên kết tài khoản ngân hàng Kiên Long Bank vào SePay</a></p>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="h6 card-title">SePay có hoàn tiền không?</h5>
                                <p class="mb-0">SePay sẽ hỗ trợ hoàn tiền trong 15 ngày đầu tiên nếu khách hàng không hài lòng về dịch vụ. Xem thêm <a href="https://sepay.vn/hoan-tien.html" target="_blank">Quy định hoàn tiền</a>. Vì vậy hãy yên tâm đăng ký và thanh toán nhé.</p>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="h6 card-title">Số lượng giao dịch là gì?</h5>
                                <p class="mb-0">Số lượng giao dịch được tính = số lượng giao dịch tiền vào + số lượng giao dịch tiền ra.</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="h6 card-title">API Banking là gì?</h5>
                                <p class="mb-0">API Banking là phương thức đồng bộ thông tin giao dịch ngân hàng cho bên thứ ba. SePay đã ký kết hợp tác trực tiếp với <a href="https://sepay.vn/mb.html" target="_blank">MB BANK</a> <a href="https://sepay.vn/ocb.html" target="_blank">OCB</a>, <a href="https://sepay.vn/kien-long-bank.html" target="_blank">KienLongBank</a> và <a href="https://sepay.vn/msb.html" target="_blank">MSB</a>.</p>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="h6 card-title">SePay có hỗ trợ xuất hóa đơn VAT không?</h5>
                                <p class="mb-0">SePay có hỗ trợ xuất hóa đơn VAT cho quý khách. Để được xuất hoá đơn VAT, vui lòng liên hệ với SePay nhé.</p>
                            </div>
                        </div>
                    </div>


                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="h6 card-title">Tôi muốn dùng hơn giới hạn số lượng giao dịch của gói thì làm sao?</h5>
                                <p class="mb-0">Bạn có thể dùng vượt giới hạn giao dịch của gói bởi một cách bình thường. Đến kỳ thanh toán hệ thống sẽ tạo hoá đơn tính tiền thêm khi vượt.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div>
                    <div class="text-center mx-auto" style="max-width:800px">
                        <div class="card card-info-link card-sm">
                            <div class="card-body fs-4">
                                Bạn muốn gói cao hơn? hoặc cần tư vấn triển khai? Liên hệ SePay ngay nhé! <a class="card-link ms-2" href="https://sepay.vn/lien-he.html">Liên hệ <span class="bi-chevron-right small ms-1"></span></a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>

<?php include(APPPATH . 'Views/templates/autopay/inc_footer.php'); ?>
<?php include(APPPATH . 'Views/company/includes/modal-features.php'); ?>

<script src="<?php echo base_url(); ?>/assets/js/bootstrap.bundle.min.js"></script>
<script src="<?php echo base_url(); ?>/assets/js/jquery-3.5.1.js"></script>
<script src="<?php echo base_url(); ?>/assets/notyf/notyf.min.js"></script>
<script src="<?php echo base_url(); ?>/assets/js/app.js?v=1"></script>

<?= view('company/includes/vpbank-promotion') ?>

<script>
    setTimeout(() => $('#api_plan_select').trigger('change'), 50);
    var b_discount = 0;
    var b_cycle = 1;
    var billing_cycle_map = {
        1: 'monthly',
        12: 'annually',
    };
    var subscription_details = <?= json_encode($subscription_details) ?>

    function updateBtnOrderSms() {
        var e = document.getElementById("sms_plan_select");
        var option = e.options[e.selectedIndex];
        var plan_id = e.value;

        var btnOrder = document.getElementById("box-sms-order");

        if (plan_id == subscription_details.plan_id && subscription_details.billing_cycle == billing_cycle_map[b_cycle]) {
            btnOrder.innerHTML = 'Đang sử dụng'
            btnOrder.classList.remove('btn-primary')
            btnOrder.classList.add('btn-secondary')
            btnOrder.href = "#"
        } else {
            btnOrder.classList.remove('btn-secondary')
            btnOrder.classList.add('btn-primary')
            btnOrder.innerHTML = 'Chọn'
            var order_url = new URL('<?= base_url('company/cart') ?>');
            order_url.searchParams.set('billing_cycle', billing_cycle_map[b_cycle]);
            order_url.searchParams.set('product_id', plan_id);
            btnOrder.href = order_url.toString();
        }
    }

    function updateBtnOrderApi() {
        var e = document.getElementById("api_plan_select");
        var option = e.options[e.selectedIndex];
        var plan_id = e.value;

        var btnOrder = document.getElementById("box-api-order");
        if (plan_id == subscription_details.plan_id && subscription_details.billing_cycle == billing_cycle_map[b_cycle]) {
            btnOrder.innerHTML = 'Đang sử dụng'
            btnOrder.classList.remove('btn-primary')
            btnOrder.classList.add('btn-secondary')
            btnOrder.href = "#"
        } else {
            btnOrder.classList.remove('btn-secondary')
            btnOrder.classList.add('btn-primary')
            btnOrder.innerHTML = 'Chọn'
            var order_url = new URL('<?= base_url('company/cart') ?>');
            order_url.searchParams.set('billing_cycle', billing_cycle_map[b_cycle]);
            order_url.searchParams.set('product_id', plan_id);
            btnOrder.href = order_url.toString();
        }
    }

    function updatePriceBox(box, m_cycle = false, element = false) {

        if (box == 'sms' || box == 'api') {
            if (box == 'sms') {
                var e = document.getElementById("sms_plan_select");
                var option = e.options[e.selectedIndex];
                var plan_id = e.value;

            } else if (box == 'api') {
                var e = document.getElementById("api_plan_select");
                var option = e.options[e.selectedIndex];
                var plan_id = e.value;
            }

            var plan_price = option.getAttribute("data-price");
            var plan_name = option.getAttribute("data-plan");
            var plan_transactions = option.getAttribute("data-transactions");
            var order_url = "<?php echo base_url('company/cart') ?>";

            if (box == 'sms') {


                document.getElementById("box-sms-price").innerText = Intl.NumberFormat('en-US').format(plan_price - b_discount * plan_price) + 'đ';

                document.getElementById("box-sms-price").dataset['originalprice'] = plan_price;

                document.getElementById("box-sms-name").innerText = plan_name.toUpperCase();

                var btnOrder = document.getElementById("box-sms-order");
                //btnOrder.href = order_url + '?product_id=' + plan_id + '&billing_cycle=monthly';

                updateBtnOrderSms();
            } else {
                document.getElementById("box-api-price").innerText = Intl.NumberFormat('en-US').format(plan_price - b_discount * plan_price) + 'đ';
                document.getElementById("box-api-price").dataset['originalprice'] = plan_price;

                document.getElementById("box-api-name").innerText = plan_name.toUpperCase();

                var btnOrder = document.getElementById("box-api-order");

                updateBtnOrderApi()
            }


        } else if (box == 'billing_select') {
            var discount = element.dataset.discount;
            b_discount = discount;
            b_cycle = m_cycle;

            var priceElms = document.querySelectorAll(".price_value");
            [].forEach.call(priceElms, function(el) {
                var before_discount = el.dataset.originalprice;

                var after_discount = before_discount - discount * before_discount;

                el.innerHTML = Intl.NumberFormat('en-US').format(after_discount) + 'đ';
            });

            var priceElms = document.querySelectorAll(".btn-order");
            [].forEach.call(priceElms, function(el) {
                var order_url = new URL(el.href);
                order_url.searchParams.set('billing_cycle', billing_cycle_map[m_cycle]);
                el.href = order_url.toString();
            });

            var apiOptionElms = document.querySelectorAll("#api_plan_select option");

            apiOptionElms.forEach((el) => {
                var before_discount = el.dataset.price / el.dataset.transactions;
                var after_discount = parseInt(before_discount - discount * before_discount);

                $(el).html(el.dataset.transactions + ' giao dịch/ tháng' + ' ↔ 1 gd ≈ ' + Intl.NumberFormat('en-US').format(after_discount) + 'đ');
            });

            var smsOptionElms = document.querySelectorAll("#sms_plan_select option");

            smsOptionElms.forEach((el) => {
                var before_discount = el.dataset.price / el.dataset.transactions;
                var after_discount = parseInt(before_discount - discount * before_discount);

                $(el).html(el.dataset.transactions + ' giao dịch/ tháng' + ' ↔ 1 gd ≈ ' + Intl.NumberFormat('en-US').format(after_discount) + 'đ');
            });

            updateBtnOrderSms()
            updateBtnOrderApi()
        }

    }

    document.getElementById("btnBillYearly").click();

    $(document)
        .on("click", ".btn-order", function(e) {
            e.preventDefault();

            const $this = $(this);

            <?php if ($vpbankPro): ?>
                if ($this.data("plan-name") == "Free") {
                    $('#modalVPBankPRO').modal('show');
                    return;
                }
            <?php endif; ?>

            if (typeof gtag === "function") {
                gtag("event", "select_item", {
                    currency: "VND",
                    items: [{
                        item_id: $this.data("plan-id"),
                        item_name: $this.data("plan-name"),
                        price: $this.data("price"),
                        quantity: 1,
                    }, ],
                });
            }

            window.location.href = $this.attr("href");
        });
</script>

<?= component_script('_components/billing/billing-switcher') ?>
<?= component_script('company/includes/vpbank-promotion') ?>