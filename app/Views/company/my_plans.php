<main class="content">
    <div class="container-fluid">

        <div class="mt-5">
            <h1 class="text-center">G<PERSON>i dịch đã chọn <a href="https://docs.sepay.vn/goi-dich-vu.html" target="_blank"><i class="bi bi-question-circle"></i></a></h1>
            <div class=" mt-3 mx-auto" style="max-width:800px">
                
                <div class="">
                    <?php if (isset($payment_details->invoice_status) && $payment_details->invoice_status == "Unpaid") { ?>

                        <div class="alert alert-danger">
                            <div class="alert-icon">
                                <i class="far fa-fw fa-bell"></i>
                            </div>
                            <div class="alert-message">Vui lòng thanh toán hóa đơn <a
                                    href="<?= base_url('invoices/details/' . $payment_details->invoice_id); ?>">
                                    #<?= $payment_details->invoice_id; ?></a>
                                <?php if ($subscription_details->status == "Pending") echo 'để dịch vụ được kích ho<PERSON>t';   ?>
                            </div>
                        </div>

                    <?php } ?>

                    <?php if (is_object($subscription_change_invoice_details)): ?>
                        <div class="alert alert-warning">
                            <div class="alert-icon">
                                <i class="far fa-fw fa-bell"></i>
                            </div>
                            <div class="alert-message">Bạn đang có yêu cầu đổi gói dịch vụ <b><?= $subscription_change_invoice_details->name ?>
                                    <?php if ($subscription_change_invoice_details->billing_cycle != 'free'): ?>
                                        (<?= $subscription_change_invoice_details->billing_cycle === 'monthly' ? 'Theo tháng' : 'Theo năm' ?>)
                                    <?php endif ?>
                                </b>, vui lòng thanh toán hóa đơn <a
                                    href="<?= base_url('invoices/details/' . $subscription_change_invoice_details->invoice_id); ?>">
                                    #<?= $subscription_change_invoice_details->invoice_id; ?></a> để kích hoạt
                                <div class="mt-2">
                                    <a href="<?= base_url('invoices/details/' . $subscription_change_invoice_details->invoice_id); ?>" class="btn btn-warning">Thanh toán ngay</a>
                                    <button type="button" class="btn btn-link text-warning" onclick="cancelSubscriptionChange()">Hủy đổi gói</button>
                                </div>
                            </div>
                        </div>

                    <?php endif ?>

                    <div class="row">
                        <div class="col-md-7">
                            <div class="card">
                                <div class="card-body">
                                    <h5 class="card-title">Gói dịch vụ</h5>
                                    <table class="table" style="max-width:400px">

                                        <tbody>
                                            <tr>
                                                <td>Gói của bạn</td>
                                                <td class="fw-bold fs-4"><?= esc($plan_details->name); ?>
                                                    <?php if ($subscription_details->status != "Active") { ?>
                                                        <a class="btn btn-sm btn-light rounded float-md-end" onclick="cancelPendingSubscription();"> Hủy gói, đăng ký lại</a>
                                                    <?php } else { ?>
                                                        <a class="btn btn-sm btn-primary rounded float-md-end <?= $subscription_change_invoice_details ? 'disabled' : '' ?>" href="<?= base_url('company/change_plan') ?>"> Đổi gói</a>
                                                    <?php } ?>

                                                </td>
                                            </tr>
                                            <tr>
                                                <td>Chu kỳ thanh toán</td>
                                                <td class="fw-bold"><?php

                                                                    echo get_text_by_billing_cycle($subscription_details->billing_cycle); ?>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>Hạn mức giao dịch</td>
                                                <td class="fw-bold">
                                                    <?= number_format($total_transaction_limit) ?>
                                                    <a class="text-primary ms-1" data-bs-toggle="modal" data-bs-target="#transactionLimitModal">
                                                        <i class="bi bi-question-circle"></i>
                                                    </a>
                                                </td>
                                            </tr>
                                            <?php ?>
                                            <tr>
                                                <td>Đã sử dụng</td>
                                                <td class="fw-bold"><?= number_format($count_trans_this_period); ?></td>
                                            </tr>
                                            <tr>
                                                <td>Thanh toán lần đầu</td>
                                                <td class="fw-bold"><?= number_format($subscription_details->first_payment); ?> đ</td>
                                            </tr>
                                            <tr>
                                                <td class="<?= pg_billing_enabled() && $subscription_details->recurring_payment > 0 ? 'border-bottom-0' : '' ?>">Gia hạn</td>
                                                <td class="<?= pg_billing_enabled() && $subscription_details->recurring_payment > 0 ? 'border-bottom-0' : '' ?>"><?= number_format($subscription_details->recurring_payment); ?> đ</td>
                                            </tr>
                                            
                                            <?php if (pg_billing_enabled() && $subscription_details->recurring_payment > 0): ?>
                                            <tr>
                                                <td colspan="2" class="pt-0">
                                                    <?= component('_components/billing/auto-renew', [
                                                        'subscription' => $subscription_details,
                                                        'product' => $plan_details,
                                                    ]) ?>
                                                </td>
                                            </tr>
                                            <?php endif ?>

                                            <tr>
                                                <td>Ngày bắt đầu</td>
                                                <td class="fw-bold"><?= esc($subscription_details->begin_date); ?></td>
                                            </tr>
                                            <tr>
                                                <td>Ngày hết hạn</td>
                                                <td><?php if ($plan_details->billing_type == "Recurring")  echo '<span class="fw-bold">' . esc($subscription_details->end_date) . '</span>' . '<br><small>(còn lại ' . calc_subscription_remaining_days($subscription_details->end_date) . ' ngày)</small>';
                                                    else echo "N/A"; ?></td>
                                            </tr>
                                            <tr>
                                                <td>Trạng thái dịch vụ</td>
                                                <td><?php if ($subscription_details->status == 'Pending') { ?>
                                                        <span class="badge bg-danger rounded-pill">Đang chờ</span>
                                                    <?php } else if ($subscription_details->status == 'Active') { ?>
                                                        <span class="badge bg-success rounded-pill">Đang hoạt động</span>
                                                    <?php } else if ($subscription_details->status == 'Suspended') { ?>
                                                        <span class="badge bg-warning rounded-pill">Tạm khóa</span>

                                                    <?php } else if ($subscription_details->status == 'Terminated') { ?>
                                                        <span class="badge bg-dark rounded-pill">Đã chấm dứt</span>

                                                    <?php } else if ($subscription_details->status == 'Cancelled') { ?>
                                                        <span class="badge bg-secondary rounded-pill">Đã hủy</span>

                                                    <?php } else if ($subscription_details->status == 'Fraud') { ?>
                                                        <span class="badge bg-secondary rounded-pill">Khác</span>

                                                    <?php } else { ?>
                                                        <?= esc($subscription_details->status); ?>
                                                    <?php } ?>
                                                </td>
                                            </tr>

                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-5">

                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Mức sử dụng giao dịch <i class="bi bi-question-circle" data-bs-toggle="tooltip" data-bs-title="Từ ngày <?= esc($begin_date); ?> đến <?= esc($end_date); ?>"></i></h5>
                                </div>
                                <?php
                                if ($subscription_details->monthly_transaction_limit > 0)
                                    $percent_usage = round(($count_trans_this_period / ($subscription_details->monthly_transaction_limit * get_month_by_billing_cycle($subscription_details->billing_cycle))) * 100);
                                else
                                    $percent_usage = 0;

                                ?>
                                <div class="card-body">
                                    <div class="progress" role="progressbar" aria-label="Basic example" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">
                                        <div class="progress-bar" style="width: <?= $percent_usage; ?>%"></div>
                                    </div>
                                    <div class="row mt-2">
                                        <div class="col-6 text-start">
                                            <h5 class="text-info"><?= number_format($count_trans_this_period); ?></h5>
                                            <span class="text-muted">Đã sử dụng</span>
                                        </div>
                                        <div class="col-6 text-end">
                                            <h5 class="text-info"><?= number_format($subscription_details->monthly_transaction_limit * get_month_by_billing_cycle($subscription_details->billing_cycle)); ?></h5>
                                            <span class="text-muted">Giới hạn</span>
                                        </div>
                                        <div class="col-12 text-center">
                                            <a href="<?= base_url('statistics/counter/?period=custom&start_date=' . esc($begin_date) . "&end_date=" . esc($end_date)); ?>">Xem bộ đếm<i class="bi bi-chevron-right"></i></a>
                                        </div>
                                    </div>

                                </div>
                            </div>

                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Liên kết</h5>
                                </div>
                                <ul class="list-group list-group-flush">
                                    <?php if (isset($payment_details->invoice_status) && $payment_details->invoice_status == "Unpaid") { ?>
                                        <li class="list-group-item">Hóa đơn nợ <a
                                                href="<?= base_url('invoices/details/' . $payment_details->invoice_id); ?>">
                                                #<?= $payment_details->invoice_id; ?></a></li>
                                    <?php } ?>
                                    <li class="list-group-item"><a href="<?= base_url('invoices'); ?>">Danh sách hóa đơn</a></li>


                                </ul>
                            </div>

                        </div>
                    </div>



                </div>
            </div>
        </div>


    </div>
</main>

<div class="modal fade" id="transactionLimitModal" tabindex="-1" aria-labelledby="transactionLimitModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-md">
        <div class="modal-content shadow-lg">
            <div class="modal-header">
                <h5 class="modal-title" id="transactionLimitModalLabel">
                    <i class="bi bi-info-circle"></i> Thông tin hạn mức giao dịch
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-4">
                <p class="text-muted mb-3">
                    Dưới đây là thông tin chi tiết về hạn mức giao dịch hiện tại của bạn:
                </p>
                <ul class="list-group">
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <strong>Tổng hạn mức giao dịch</strong>
                        <span class="badge bg-primary rounded-pill">
                            <?= number_format($total_transaction_limit) ?>
                        </span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <strong>Hạn mức giao dịch từ chương trình giới thiệu</strong>
                        <span class="badge bg-success rounded-pill">
                            <?= number_format($bonus_transactions) ?>
                        </span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <strong>Hạn mức giao dịch từ chương trình mở mới tài khoản nhận giao dịch</strong>
                        <span class="badge bg-success rounded-pill">
                            <?= number_format($count_bank_bonus) ?>
                        </span>
                    </li>

                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <strong>Hạn mức giao dịch gốc</strong>
                        <span class="badge bg-secondary rounded-pill">
                            <?php
                            if (!empty($count_bank_bonus)) {
                                echo number_format($total_transaction_limit - $bonus_transactions - $count_bank_bonus);
                            } else {
                                echo number_format($total_transaction_limit - $bonus_transactions);
                            }
                            ?>
                        </span>
                    </li>
                </ul>
            </div>
            <div class="modal-footer justify-content-end">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    Đóng
                </button>
            </div>
        </div>
    </div>
</div>

<?php include(APPPATH . 'Views/templates/autopay/inc_footer.php'); ?>


</div>
</div>

<script src="<?php echo base_url(); ?>/assets/js/bootstrap.bundle.min.js"></script>

<script src="<?php echo base_url(); ?>/assets/js/jquery-3.5.1.js"></script>

<script src="<?php echo base_url(); ?>/assets/notyf/notyf.min.js"></script>

<script src="<?php echo base_url(); ?>/assets/js/app.js?v=1"></script>


<script>
    function cancelPendingSubscription() {
        if (window.confirm("Bạn có chắc chắn muốn hủy gói này?")) {
            $.ajax({
                url: '<?= base_url('company/ajax_cancel_pending_subscription'); ?>',
                type: "POST",
                data: {
                    subscription_id: <?= esc($subscription_details->id); ?>,
                    "<?= csrf_token() ?>": "<?= csrf_hash() ?>"
                },
                dataType: "JSON",
                success: function(data) {
                    if (data.status == true) {
                        location.href = '<?= base_url('company/plans'); ?>';
                    } else {
                        alert('Lỗi: ' + data.message);
                    }

                }

            });
        }
    }

    function cancelSubscriptionChange() {
        if (window.confirm("Bạn có chắc chắn muốn hủy đổi gói này?")) {
            $.ajax({
                url: '<?= base_url('company/ajax_cancel_subscription_change'); ?>',
                type: "POST",
                data: {
                    "<?= csrf_token() ?>": "<?= csrf_hash() ?>"
                },
                dataType: "JSON",
                success: function(data) {
                    if (data.status == true) {
                        location.reload()
                    } else {
                        alert('Lỗi: ' + data.message);
                    }
                }
            });
        }
    }
</script>

<?php if (pg_billing_enabled() && $subscription_details->recurring_payment > 0): ?>
<?= component_script('_components/billing/auto-renew', [
    'subscription' => $subscription_details,
    'product' => $plan_details,
]) ?>
<?php endif ?>