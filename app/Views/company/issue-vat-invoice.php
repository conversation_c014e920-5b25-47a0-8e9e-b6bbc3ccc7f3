<?php if (! $invoice_details->vat_invoice_requested_at && ! $invoice_details->tax_issued && $invoice_details->total > 0): ?>
    <div class="modal fade" id="vatInvoiceModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><PERSON><PERSON><PERSON> cầu xuất hóa đơn VAT</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <?= form_open("company/ajax_request_vat_invoice/$invoice_details->id", ['id' => 'vatInvoiceForm']); ?>
                <input type="hidden" name="info_id" value="">

                <div class="modal-body">
                    <div class="mb-4">
                        <div class="btn-group w-100" role="group">
                            <input type="radio" class="btn-check" name="invoice_type" id="personalType" value="personal" checked>
                            <label class="btn btn-outline-primary" for="personalType">
                                <i class="bi bi-person me-1"></i> Cá nhân
                            </label>
                            <input type="radio" class="btn-check" name="invoice_type" id="companyType" value="company">
                            <label class="btn btn-outline-primary" for="companyType">
                                <i class="bi bi-building me-1"></i> Công ty
                            </label>
                        </div>
                    </div>

                    <?php if (! empty($customerInfos)): ?>
                        <div id="savedProfiles" class="mb-3">
                            <label class="form-label">Chọn thông tin có sẵn</label>
                            <select class="form-select" id="profileSelect">
                                <option value="">--- Tạo mới ---</option>
                                <?php foreach ($customerInfos as $info): ?>
                                    <option value="<?= $info->id; ?>">#<?= $info->id; ?> - <?= $info->name ?: $info->company_name; ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    <?php endif; ?>

                    <div id="personalForm">
                        <div class="mb-3">
                            <label for="name" class="form-label">Họ và tên <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name">
                        </div>
                        <div class="mb-3">
                            <label for="tax_code" class="form-label">Mã số thuế</label>
                            <input type="text" class="form-control" id="tax_code" name="tax_code">
                        </div>
                        <div class="mb-3">
                            <label for="address" class="form-label">Địa chỉ <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="address" name="address" rows="2" placeholder="Nhập đầy đủ địa chỉ"></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="email" class="form-label">Email nhận hóa đơn</label>
                            <input type="email" class="form-control" id="email" name="email">
                        </div>
                    </div>

                    <div id="companyForm" style="display: none;">
                        <div class="mb-3">
                            <label for="company_name" class="form-label">Tên công ty <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="company_name" name="company_name" disabled>
                        </div>
                        <div class="mb-3">
                            <label for="company_tax_code" class="form-label">Mã số thuế <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="company_tax_code" name="company_tax_code">
                                <button class="btn btn-outline-primary" type="button" id="lookupTaxCode">
                                    <i class="bi bi-search me-1"></i>
                                    Lấy thông tin
                                </button>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="company_address" class="form-label">Địa chỉ <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="company_address" name="company_address" rows="2" placeholder="Nhập đầy đủ địa chỉ" disabled></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="company_email" class="form-label">Email nhận hóa đơn</label>
                            <input type="email" class="form-control" id="company_email" name="company_email">
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-send me-1"></i> Gửi yêu cầu
                    </button>
                </div>
                <?= form_close(); ?>
            </div>
        </div>
    </div>

    <div class="modal fade" id="vatNoteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-body text-center px-4">
                    <h5 class="mb-4">Lưu ý về Hóa đơn điện tử/Hóa đơn VAT</h5>
                    <button type="button" class="btn-close position-absolute" data-bs-dismiss="modal" aria-label="Close" style="right: 1rem; top: 1rem;"></button>
                    <div class="text-start mb-4">
                        <p class="mb-3">1. Trường hợp Khách hàng không gửi yêu cầu xuất hóa đơn khi thực hiện thanh toán dịch vụ, SePay sẽ mặc định xuất hóa đơn với thông tin "Khách lẻ không lấy hóa đơn"</p>
                        <p>2. SePay chỉ hỗ trợ xuất hóa đơn một lần và Khách hàng không thể thay đổi thông tin hóa đơn sau khi thanh toán dịch vụ</p>
                    </div>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#vatInvoiceModal" onclick="$('#vatNoteModal').modal('hide');">
                        <i class="bi bi-check-lg me-1"></i> Đã hiểu
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        $(() => {
            const customerInfos = <?= $invoice_details->tax_issued ? '[]' : json_encode($customerInfos); ?>;

            $(document)
            <?php if (! $invoice_details->vat_invoice_requested_at && ! $invoice_details->tax_issued): ?>
                    .on('submit', '#vatInvoiceForm', function(e) {
                        e.preventDefault();

                        const form = $(this);
                        const button = form.find('button[type="submit"]');

                        $.ajax({
                            url: form.attr('action'),
                            type: 'POST',
                            data: form.serialize(),
                            dataType: 'JSON',
                            beforeSend: function() {
                                button.prop('disabled', true).html('<span class="spinner-border spinner-border-sm align-middle me-2" role="status" aria-hidden="true"></span> Đang xử lý...');
                                $('input, textarea').removeClass('is-invalid');
                                $('.invalid-feedback').remove();
                            },
                            success: function(response) {
                                if (response.status) {
                                    $('#vatInvoiceModal').modal('hide');
                                    location.reload();
                                } else {
                                    notyf.error(response.message);
                                }
                            },
                            error: function(xhr, status, error) {
                                if (xhr.responseJSON.status == 400) {
                                    const errors = xhr.responseJSON.messages;
                                    Object.keys(errors).forEach((key) => {
                                        const fieldName = key.replace(/\.\d+$/, '');
                                        const field = $(`[name="${fieldName}"], [name="${fieldName}[]"]`).not(':disabled');
                                        const value = errors[key];

                                        if (field.length) {
                                            if (field.attr('type') === 'radio') {
                                                notyf.error(value);
                                            } else {
                                                field.addClass('is-invalid');

                                                if (field.closest('.input-group').length) {
                                                    const inputGroup = field.closest('.input-group');

                                                    if (!inputGroup.next('.invalid-feedback').length) {
                                                        inputGroup.append(`<div class="invalid-feedback">${value}</div>`);
                                                    } else {
                                                        inputGroup.next('.invalid-feedback').text(value);
                                                    }
                                                } else {
                                                    if (!field.next('.invalid-feedback').length) {
                                                        field.after(`<div class="invalid-feedback">${value}</div>`);
                                                    } else {
                                                        field.next('.invalid-feedback').text(value);
                                                    }
                                                }
                                            }
                                        }
                                    });
                                } else {
                                    notyf.error('Đã xảy ra lỗi. Vui lòng thử lại sau.');
                                }
                            },
                            complete: function() {
                                button.prop('disabled', false).html('<i class="bi bi-send me-1"></i> Gửi yêu cầu');
                            }
                        });
                    })
            <?php endif; ?>
                .on('click', '#lookupTaxCode', function(e) {
                    e.preventDefault();

                    const taxCode = $('input[name="company_tax_code"]').val();

                    $('input[name="company_tax_code"]').removeClass('is-invalid');
                    $('input[name="company_tax_code"]').closest('.input-group').find('.invalid-feedback').remove();

                    if (!taxCode) {
                        $('input[name="company_tax_code"]').addClass('is-invalid');
                        $('input[name="company_tax_code"]').closest('.input-group').append('<div class="invalid-feedback">Mã số thuế không được để trống</div>');
                        return;
                    }

                    $.ajax({
                        url: '<?= base_url('speakerorder/lookup_tax_code') ?>/' + taxCode,
                        type: 'POST',
                        dataType: 'JSON',
                        data: {
                            '<?= csrf_token() ?>': '<?= csrf_hash() ?>',
                        },
                        beforeSend: function() {
                            $('#lookupTaxCode').prop('disabled', true).html('<span class="spinner-border spinner-border-sm align-middle me-2"></span>Đang xử lý...');
                        },
                        success: function({
                            status,
                            message,
                            data
                        }) {
                            if (status) {
                                $('input[name="company_name"]').prop('disabled', false).val(data.name);
                                $('textarea[name="company_address"]').prop('disabled', false).val(data.address);
                            } else {
                                $('input[name="company_tax_code"]').addClass('is-invalid');
                                $('input[name="company_tax_code"]').closest('.input-group').append(`<div class="invalid-feedback">${message}</div>`);
                            }
                        },
                        error: function(error) {
                            notyf.error('Có lỗi xảy ra. Vui lòng thử lại sau.');
                        },
                        complete: function() {
                            $('#lookupTaxCode').prop('disabled', false).html('<i class="bi bi-search"></i> Lấy thông tin');
                        }
                    });
                })
                .on('keyup', 'input, textarea', function() {
                    $(this).removeClass('is-invalid');
                    $(this).closest('.input-group').find('.invalid-feedback').remove();
                })
                .on('change', '#profileSelect', function() {
                    const profileId = $(this).val();

                    if (!profileId) {
                        $('input[name="invoice_type"][value="personal"]').prop('checked', true).trigger('change');
                        $('#vatInvoiceForm').find('input:not([name="<?= csrf_token() ?>"]):not([name="invoice_type"]), textarea').val('');
                        $('input[name="info_id"]').val('');
                        return;
                    }

                    const profile = customerInfos.find(info => info.id == profileId);
                    
                    if (!profile) {
                        return;
                    }
                    
                    const type = profile.company_name ? 'company' : 'personal';

                    $(`input[name="invoice_type"][value="${type}"]`).prop('checked', true).trigger('change');
                    $('input[name="info_id"]').val(profileId);

                    $('input, textarea').removeClass('is-invalid');
                    $('.invalid-feedback').remove();

                    if (type === 'personal') {
                        $('#name').val(profile.name || '');
                        $('#tax_code').val(profile.tax_code || '');
                        $('#address').val(profile.address || '');
                        $('#email').val(profile.email ? (Array.isArray(JSON.parse(profile.email)) ? JSON.parse(profile.email)[0] : JSON.parse(profile.email)) : '');
                    } else {
                        $('#company_name').val(profile.company_name || '').prop('disabled', false);
                        $('#company_tax_code').val(profile.tax_code || '');
                        $('#company_address').val(profile.address || '').prop('disabled', false);
                        $('#company_email').val(profile.email ? (Array.isArray(JSON.parse(profile.email)) ? JSON.parse(profile.email)[0] : JSON.parse(profile.email)) : '');
                    }
                })
                .on('change', 'input[name="invoice_type"]', function() {
                    const selectedType = this.value;
                    
                    $('#personalForm').toggle(selectedType === 'personal');
                    $('#companyForm').toggle(selectedType === 'company');

                    if (!$('#profileSelect').val()) {
                        $('#vatInvoiceForm').find('input:not([name="<?= csrf_token() ?>"]):not([name="invoice_type"]):not([name="info_id"]), textarea').val('');
                        
                        if (selectedType === 'company') {
                            $('#company_name').prop('disabled', true);
                            $('#company_address').prop('disabled', true);
                        }
                    }
                    
                    $('input, textarea').removeClass('is-invalid');
                    $('.invalid-feedback').remove();
                });
        });
    </script>
<?php endif; ?>