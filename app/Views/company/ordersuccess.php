<?php

$billingConfig = config(\Config\Billing::class);
$pgEnabled = property_exists($billingConfig, 'pgEnabled') ? ($billingConfig->pgEnabled || is_admin()) : false;  
$napasBankAccount = $billingConfig->napasBankAccount ?? [];

if (empty($napasBankAccount)) {
   $pgEnabled = false;
}

if ($pgEnabled && isset($paycode)) {
    $napasBankAccount['accountNumber'] = $napasBankAccount['vac'] . str_pad($invoice_details->id, 7, '0', STR_PAD_LEFT);

    $qrcode = "https://qr.sepay.vn/img?bank=971133&acc=" . esc($napasBankAccount['accountNumber'] ?? '') . "&template=&amount=" . intval($invoice_details->total) . "&des=" . esc($paycode);
}

?>

<style>
    .copyjs {
        cursor: pointer;
    }
</style>

<main class="content">
    <div class="container-fluid p-0">
        <div class="mx-auto" style="max-width: 800px;">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title fs-2 mb-0 text-center text-primary">Đặt hàng thành công</h4>
                </div>
                <div class="card-body">
                    <div class="mb-4">
                        <p class="text-muted">Cảm ơn quý khách đã cho SePay cơ hội được phục vụ.</p>
                    </div>

                    <div>
                        <h4>Chi tiết đơn hàng</h4>
                        <table class="table table-sm">
                            <tbody>
                                <?php foreach ($invoice_items as $item): ?>
                                    <tr>
                                        <td><strong><?= esc($item->description); ?></strong>
                                            <?php if ($item->details): ?>
                                                <?= $item->details ?>
                                            <?php endif ?>
                                        </td>
                                        <td class="text-end" style="white-space: nowrap;"><?php echo number_format($item->amount); ?> đ</td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>

                        <div class="row mt-4">
                            <div class="col-md-6"></div>
                            <div class="col-md-6">
                                <table class="table text-end">
                                    <tbody>
                                        <tr>
                                            <td>Tạm tính:</td>
                                            <td><?= number_format($invoice_details->subtotal); ?> đ</td>
                                        </tr>
                                        <tr>
                                            <td>VAT (<?= intval($invoice_details->tax_rate); ?>%):</td>
                                            <td><?= number_format($invoice_details->tax); ?> đ</td>
                                        </tr>
                                        <?php if ($invoice_details->credit > 0): ?>
                                            <tr>
                                                <td class="text-end">Tín dụng <i class="bi bi-question-circle ms-1" data-bs-toggle="tooltip" data-bs-title="Tổng số tiền từ tín dụng đã áp dụng cho hóa đơn" data-bs-original-title="" title=""></i></td>
                                                <td class="text-end fw-bold text-success">-<?= number_format($invoice_details->credit) ?> đ</td>
                                            </tr>
                                        <?php endif ?>
                                        <tr class="fw-bold">
                                            <td>Tổng:</td>
                                            <td class="text-primary"><?= number_format($invoice_details->total); ?> đ</td>
                                        </tr>
                                    </tbody>
                                </table>
                                <?php if ($canRequestVatInvoice): ?>
                                    <?php if (! $invoice_details->vat_invoice_requested_at): ?>
                                        <div class="text-end mt-2">
                                            <button type="button" class="btn btn-outline-primary btn-sm" data-bs-toggle="modal" data-bs-target="#vatNoteModal">
                                                <i class="bi bi-receipt me-1"></i> Yêu cầu xuất hóa đơn VAT
                                            </button>
                                        </div>
                                    <?php elseif ($invoice_details->vat_invoice_requested_at && ! $invoice_details->tax_issued): ?>
                                        <div class="text-warning text-end mb-2">
                                            <i class="bi bi-check-circle me-1"></i> Đã gửi yêu cầu xuất hóa đơn VAT
                                        </div>
                                    <?php elseif ($invoice_details->tax_issued): ?>
                                        <div class="text-success text-end mb-2">
                                            <i class="bi bi-check-circle me-1"></i> Đã xuất hóa đơn VAT
                                        </div>
                                    <?php endif; ?>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <?php if ($invoice_details->status === 'Unpaid'): ?>
                        <div class="border rounded p-3 bg-light my-3">
                            <div class="d-flex align-items-center text-primary">
                                <i class="bi bi-info-circle me-2"></i>
                                <strong>Đơn hàng chưa được thanh toán</strong>
                            </div>
                        </div>

                        <?php if ($company_details->credit_balance > 0): ?>
                            <?= component('_components/billing/credit-apply-form', ['invoice_details' => $invoice_details, 'company_details' => $company_details]) ?>
                        <?php endif ?>

                        <?php if (pg_billing_enabled()): ?>
                            <?= component('_components/billing/payment-gateway', [
                                'invoice' => $invoice_details,
                                'company' => $company_details,
                                'invoice_items' => $invoice_items
                            ]) ?>
                        <?php endif ?>

                        <div id="container-qrcode">
                            <div class="row g-4">
                                <div class="col-md-5">
                                    <div class="text-center">
                                        <img src="<?= $qrcode; ?>" class="img-fluid mb-3">
                                        <a href="javascript:void(0)" onclick="downloadQRCode()" class="btn btn-outline-primary btn-sm mt-2">
                                            <i class="bi bi-download"></i> Tải ảnh QR
                                        </a>
                                    </div>
                                </div>
                                <div class="col-md-7">
                                    <?php if ($pgEnabled): ?>
                                    <div>
                                        <h4 class="my-3">Thông tin thanh toán</h4>
                                        <dl class="row mb-0">
                                            <dt class="col-5 mb-3">Ngân hàng:</dt>
                                            <dd class="col-7 mb-3 fw-bold"><?= esc($napasBankAccount['bank'] ?? ''); ?></dd>

                                            <dt class="col-5 mb-3">Số tài khoản:</dt>
                                            <dd class="col-7 mb-3">
                                                <div class="d-flex align-items-center">
                                                    <span id="vcb_id" class="fw-bold"><?= esc($napasBankAccount['accountNumber'] ?? ''); ?></span>
                                                    <i class="bi bi-files ms-2 copyjs" data-clipboard-target="#vcb_id" id="i_vcb_id" data-bs-toggle="tooltip" data-bs-title="Đã copy"></i>
                                                </div>
                                            </dd>

                                            <dt class="col-5 mb-3">Thụ hưởng:</dt>
                                            <dd class="col-7 mb-3 fw-bold"><?= esc($napasBankAccount['holderName'] ?? ''); ?></dd>

                                            <dt class="col-5 mb-3">Nội dung CK:</dt>
                                            <dd class="col-7 mb-3">
                                                <div class="d-flex align-items-center">
                                                    <span id="trans_content" class="fw-bold"><?= esc($paycode); ?></span>
                                                    <i class="bi bi-files ms-2 copyjs" data-clipboard-target="#trans_content" id="i_trans_content" data-bs-toggle="tooltip" data-bs-title="Đã copy"></i>
                                                </div>
                                            </dd>

                                            <dt class="col-5 mb-3">Số tiền:</dt>
                                            <dd class="col-7 mb-3 fw-bold text-primary"><?= number_format($invoice_details->total); ?> đ</dd>
                                        </dl>
                                    </div>
                                    <?php else: ?>
                                    <div>
                                        <h4 class="my-3">Thông tin thanh toán</h4>
                                        <dl class="row mb-0">
                                            <dt class="col-5 mb-3">Ngân hàng:</dt>
                                            <dd class="col-7 mb-3 fw-bold">MBBank</dd>

                                            <dt class="col-5 mb-3">Số tài khoản:</dt>
                                            <dd class="col-7 mb-3">
                                                <div class="d-flex align-items-center">
                                                    <span id="vcb_id" class="fw-bold">*************</span>
                                                    <i class="bi bi-files ms-2 copyjs" data-clipboard-target="#vcb_id" id="i_vcb_id" data-bs-toggle="tooltip" data-bs-title="Đã copy"></i>
                                                </div>
                                            </dd>

                                            <dt class="col-5 mb-3">Thụ hưởng:</dt>
                                            <dd class="col-7 mb-3 fw-bold">SEPAY JSC</dd>

                                            <dt class="col-5 mb-3">Nội dung CK:</dt>
                                            <dd class="col-7 mb-3">
                                                <div class="d-flex align-items-center">
                                                    <span id="trans_content" class="fw-bold"><?= esc($paycode); ?></span>
                                                    <i class="bi bi-files ms-2 copyjs" data-clipboard-target="#trans_content" id="i_trans_content" data-bs-toggle="tooltip" data-bs-title="Đã copy"></i>
                                                </div>
                                            </dd>

                                            <dt class="col-5 mb-3">Số tiền:</dt>
                                            <dd class="col-7 mb-3 fw-bold text-primary"><?= number_format($invoice_details->total); ?> đ</dd>
                                        </dl>
                                    </div>
                                    <?php endif; ?>

                                    <p class="p-3 bg-light rounded">
                                        <i class="bi bi-qr-code-scan text-primary me-1"></i>
                                        Dùng ứng dụng ngân hàng quét mã QR để chuyển khoản
                                    </p>
                                </div>
                            </div>

                            <div class="border rounded p-3 bg-light mt-4">
                                <div class="d-flex align-items-center text-primary">
                                    <i class="bi bi-info-circle me-2"></i>
                                    Vui lòng&nbsp<strong>giữ nguyên nội dung chuyển khoản</strong>. Hệ thống sẽ tự nhận diện thanh toán trong vài giây
                                </div>
                            </div>
                        </div>
                    <?php elseif ($invoice_details->status == "Paid"): ?>
                        <div class="text-center mb-4">
                            <div class="text-success mb-3">
                                <i class="bi bi-check-circle-fill display-1"></i>
                            </div>
                            <h3 class="text-success">Thanh toán thành công!</h3>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            <?php if ($invoice_details->status == "Paid"): ?>
                <div class="card">
                    <div class="card-body">
                        <div>
                            <h4>Hướng dẫn sử dụng</h4>
                            <p class="text-muted">Hệ thống đã khởi tạo dịch vụ cho bạn. Để sử dụng SePay, hãy tham khảo các bước sau:</p>

                            <div class="list-group list-group-flush">
                                <div class="list-group-item">
                                    <div class="d-flex align-items-start mb-2">
                                        <div class="flex-shrink-0">
                                            <div class="badge bg-primary rounded-circle d-flex align-items-center justify-content-center" style="width: 28px; height: 28px;">1</div>
                                        </div>
                                        <div class="flex-grow-1 ms-3">
                                            <h6 class="mb-2">Thêm tài khoản ngân hàng</h6>
                                            <a href="https://docs.sepay.vn/them-tai-khoan-ngan-hang.html" target="_blank" class="btn btn-outline-primary btn-sm">
                                                <i class="bi bi-book me-1"></i> Xem hướng dẫn
                                            </a>
                                        </div>
                                    </div>
                                </div>

                                <div class="list-group-item">
                                    <div class="d-flex align-items-start mb-2">
                                        <div class="flex-shrink-0">
                                            <div class="badge bg-primary rounded-circle d-flex align-items-center justify-content-center" style="width: 28px; height: 28px;">2</div>
                                        </div>
                                        <div class="flex-grow-1 ms-3">
                                            <h6 class="mb-2">Tích hợp báo giao dịch lên phần mềm chat</h6>
                                            <div class="list-group list-group-flush">
                                                <a href="https://docs.sepay.vn/tich-hop-telegram.html" target="_blank" class="list-group-item list-group-item-action">
                                                    <i class="bi bi-telegram me-2"></i> Telegram
                                                </a>
                                                <a href="https://docs.sepay.vn/tich-hop-lark-messenger.html" target="_blank" class="list-group-item list-group-item-action">
                                                    <i class="bi bi-chat-dots me-2"></i> Lark Messenger
                                                </a>
                                                <a href="https://docs.sepay.vn/tich-hop-viber.html" target="_blank" class="list-group-item list-group-item-action">
                                                    <i class="fab fa-viber me-2" style="color: #8B5CF6; line-height: 1.6;"></i> Viber
                                                </a>
                                                <a href="https://docs.sepay.vn/mobile-app.html" target="_blank" class="list-group-item list-group-item-action">
                                                    <i class="bi bi-phone me-2"></i> Mobile App
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="list-group-item">
                                    <div class="d-flex align-items-start mb-2">
                                        <div class="flex-shrink-0">
                                            <div class="badge bg-primary rounded-circle d-flex align-items-center justify-content-center" style="width: 28px; height: 28px;">3</div>
                                        </div>
                                        <div class="flex-grow-1 ms-3">
                                            <h6 class="mb-2">Tích hợp phần mềm bán hàng/website</h6>
                                            <div class="list-group list-group-flush">
                                                <a href="https://docs.sepay.vn/tich-hop-google-sheets.html" target="_blank" class="list-group-item list-group-item-action">
                                                    <i class="bi bi-file-earmark-spreadsheet me-2"></i> Google Sheets
                                                </a>
                                                <a href="https://docs.sepay.vn/tich-hop-webhooks.html" target="_blank" class="list-group-item list-group-item-action">
                                                    <i class="bi bi-code-slash me-2"></i> Webhooks
                                                </a>
                                                <a href="https://docs.sepay.vn/tich-hop-sapo.html" target="_blank" class="list-group-item list-group-item-action">
                                                    <i class="bi bi-shop me-2"></i> Sapo Web
                                                </a>
                                                <a href="https://docs.sepay.vn/tich-hop-haravan.html" target="_blank" class="list-group-item list-group-item-action">
                                                    <i class="bi bi-shop me-2"></i> Haravan Web
                                                </a>
                                                <a href="https://docs.sepay.vn/tich-hop-shopify.html" target="_blank" class="list-group-item list-group-item-action">
                                                    <i class="bi bi-shop me-2"></i> Shopify
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mt-4">
                            <h4>Các kênh hỗ trợ</h4>
                            <div class="list-group list-group-flush">
                                <a href="tel:02873059589" class="list-group-item list-group-item-action">
                                    <i class="bi bi-telephone me-2 text-primary"></i> Hotline: 02873.059.589
                                </a>
                                <a href="https://www.facebook.com/messages/t/sepay.vn" target="_blank" class="list-group-item list-group-item-action">
                                    <i class="bi bi-chat-dots me-2 text-primary"></i> Chat với SePay: fb.com/sepay.vn
                                </a>
                                <a href="https://docs.sepay.vn" target="_blank" class="list-group-item list-group-item-action">
                                    <i class="bi bi-life-preserver me-2 text-primary"></i> Tài liệu hướng dẫn: docs.sepay.vn
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
            <div class="text-center">
                <a href="<?= base_url(); ?>" class="btn btn-outline-primary btn-pill">
                    <i class="bi bi-house me-1"></i> Trang chủ
                </a>
            </div>
        </div>
    </div>
</main>

<?php include(APPPATH . 'Views/templates/autopay/inc_footer.php'); ?>

<script src="<?= base_url('assets/js/bootstrap.bundle.min.js'); ?>"></script>
<script src="<?= base_url('assets/js/jquery-3.5.1.js'); ?>"></script>
<script src="<?= base_url('assets/notyf/notyf.min.js'); ?>"></script>
<script src="<?= base_url('assets/js/app.js?v=1'); ?>"></script>
<script src="<?= base_url('assets/clipboardjs/clipboard.min.js'); ?>"></script>

<script>
    $(() => {
        const vcbTooltip = document.getElementById('i_vcb_id');
        const transTooltip = document.getElementById('i_trans_content');

        if (vcbTooltip) {
            const tooltip = new bootstrap.Tooltip(vcbTooltip);
            tooltip.disable();
        }

        if (transTooltip) {
            const tooltip = new bootstrap.Tooltip(transTooltip);
            tooltip.disable();
        }

        var clipboard = new ClipboardJS('.copyjs');
        clipboard.on('success', function(e) {
            const id = e.trigger.getAttribute('id');
            const element = document.getElementById(id);

            if (element) {
                const tooltip = new bootstrap.Tooltip(element, {
                    trigger: 'click'
                });
                tooltip.show();

                setTimeout(function() {
                    tooltip.hide();
                    tooltip.disable();
                }, 500);
            }
        });

        <?php if ($invoice_details->status == 'Unpaid'): ?>

            function check_invoice_status() {
                $.ajax({
                    url: "<?= base_url('invoices/ajax_check_status'); ?>",
                    type: "POST",
                    data: {
                        invoice_id: <?= $invoice_details->id; ?>,
                        "<?= csrf_token() ?>": "<?= csrf_hash() ?>"
                    },
                    dataType: "JSON",
                    success: function(data) {
                        if (data.status == true && data.invoice_status == 'Paid') {
                            notyf.success('Thanh toán thành công!');
                            location.reload();
                        }
                    }
                });
            }

            setInterval(function() {
                check_invoice_status();
            }, 3000);


        <?php endif; ?>
    });

    <?php if ($invoice_details->status == 'Unpaid'): ?>
        function downloadQRCode() {
            const qrImageSrc = "<?= $qrcode ?? ''; ?>";
            if (qrImageSrc) {
                let img = new Image();
                img.crossOrigin = "anonymous";
                img.src = qrImageSrc;

                img.onload = function() {
                    let canvas = document.createElement("canvas");
                    let ctx = canvas.getContext("2d");

                    canvas.width = img.width;
                    canvas.height = img.height;

                    ctx.drawImage(img, 0, 0);

                    canvas.toBlob(blob => {
                        let url = URL.createObjectURL(blob);
                        let a = document.createElement('a');
                        a.href = url;
                        a.download = "qr-invoice-<?= esc($invoice_details->id); ?>.png";
                        document.body.appendChild(a);
                        a.click();
                        document.body.removeChild(a);
                        URL.revokeObjectURL(url);
                    }, "image/png");
                };

                img.onerror = function() {
                    notyf.error({
                        message: "Không thể tải hình ảnh QR."
                    });
                };
            } else {
                notyf.error({
                    message: "Không tìm thấy hình ảnh QR để tải xuống."
                });
            }
        }
    <?php endif; ?>
    
    $(document).ready(function() {
        function toggleAutoRenew() {
            if ($('input[name="auto_renew"]').prop('checked')) {
                $('#form-pay-card').hide();
                $('#form-pay-recurring-card').show();
            } else {
                $('#form-pay-card').show();
                $('#form-pay-recurring-card').hide();
            }
        }

        $('input[name="auto_renew"]').on('change', toggleAutoRenew);
    });
</script>

<?php include(APPPATH . 'Views/company/issue-vat-invoice.php'); ?>

<?php if ($invoice_details->status == 'Unpaid' && $company_details->credit_balance > 0): ?>
    <?= component_script('_components/billing/credit-apply-form') ?>
<?php endif ?>

<?php if ($invoice_details->status == 'Unpaid' && pg_billing_enabled()): ?>
<?= component_script('_components/billing/payment-gateway') ?>
<?php endif ?>