<style>
    #preloader {
        background: #e0ecff url(<?= base_url('assets/images/other/ajax-loading.gif');?>) no-repeat center center;
        opacity: 0.8;
        filter: alpha(opacity=80);
    }
</style>
<main class="content">
    <div class="container-fluid" style="max-width:1000px">
        <div class="row mt-5">
            <div class="col-lg-7">
                <?php if (is_object($subscription_details)): ?>
                <div class="card mb-0">
                    <div class="card-header">
                        <span class="badge bg-primary">Gói hiện tại</span>
                    </div>
                    <div class="card-body pt-0">
                        <h5 class="card-title mb-2">SePay - <?= esc($subscription_details->product_name);?></h5>
                        <table class="table table-sm">
                            <tbody>
                                <tr>
                                    <td>Chu kỳ thanh toán</td>
                                    <td class="fw-bold"><?= esc(get_text_by_billing_cycle($subscription_details->billing_cycle)) ?></td>
                                </tr>
                                <tr>
                                    <td ><PERSON><PERSON><PERSON> b<PERSON>t đầ<PERSON></td>
                                    <td class="fw-bold"><?= esc($subscription_details->begin_date) ?></td>
                                </tr>
                                <tr>
                                    <td>Ngày hết hạn</td>
                                    <td class="fw-bold">
                                        <?php if($subscription_details->product_billing_type == "Recurring")  echo '<span class="fw-bold">' . esc($subscription_details->end_date) . '</span>' . '<br><small>(còn lại ' . calc_subscription_remaining_days($subscription_details->end_date) . ' ngày)</small>'; else echo "N/A";?>
                                    </td>
                                </tr>
                                <?php if ($subscription_exchange_remaining_money > 0): ?>
                                <tr class="<?= $shop_billing_feature->determineIfOrderIsChangeShopAmount() ? 'd-none' : '' ?> change-plan-card">
                                    <td class="border-0">Số tiền khả dụng có thể hoàn trả <i class="bi bi-question-circle ms-1" data-bs-toggle="tooltip" data-bs-title="<?= $subscription_details->monthly_transaction_limit > 0 ? 'Là số tiền quy từ từ số lượt giao dịch còn lại của gói dịch vụ. Số tiền này sẽ tự động sử dụng để bù chênh lệch cho việc đổi gói dịch vụ tại SePay, phần còn dư sẽ được hoàn về ví.' : 'Là số tiền quy từ từ số ngày sử dụng còn lại của gói dịch vụ. Số tiền này sẽ tự động sử dụng để bù chênh lệch cho việc đổi gói dịch vụ tại SePay, phần còn dư sẽ được hoàn về ví.' ?>" data-bs-original-title="" title=""></i></td>
                                    <td class="fw-bold border-0"><?= esc(number_format($subscription_exchange_remaining_money)) ?>đ</td>
                                </tr>
                                <?php endif ?>
                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="d-flex align-items-center justify-content-center my-2 <?= $shop_billing_feature->determineIfOrderIsChangeShopAmount() ? 'd-none' : '' ?> change-plan-icon">
                    <style>
                        @keyframes bounce { 0%,to { transform: translateY(-10%); animation-timing-function: cubic-bezier(.8,0,1,1) } 50% { transform: none; animation-timing-function: cubic-bezier(0,0,.2,1) } } 
                    </style>
                    <i class="fs-1 bi bi-arrow-down-circle" style="animation: bounce 1s infinite;"></i> <span class="ms-2"> chuyển sang</span>
                </div>
                <?php endif ?>

                <div class="card <?= $shop_billing_feature->determineIfOrderIsChangeShopAmount() ? 'd-none' : '' ?> change-plan-card">
                    <?php if (is_object($subscription_details)): ?>
                    <div class="card-header pb-0">
                        <span class="badge bg-success">Gói mới</span>
                    </div>
                    <?php endif ?>
                    <div class="card-body pb-0">
                        <h5 class="card-title mb-2">SePay - <?= esc($product_details->name);?></h5>
                        <p class="mb-3 billing-cycle-full-text">
                            <?php if ($shop_billing_feature->enabled && $can_trial): ?>
                                <?= $trial_days ?> ngày dùng thử
                            <?php else: ?>
                                <?= get_text_by_billing_cycle($billing_cycle) ?>
                            <?php endif ?>
                        </p>
                        <?= $product_details->description;?>
                    </div>
                </div>
                <?= form_open('', ['id'=>'cart_form', 'class' => 'mt-4']) ;?>
                <input type="hidden" name="product_id" value="<?= esc($product_details->id);?>">
                <input type="hidden" name="coupon_apply" id="coupon_apply" value="">
                <input type="hidden" name="credit_apply" id="credit_apply" value="">
                <?php if ($promotion): ?>
                    <input type="hidden" name="promotion_id" value="<?= esc($promotion->id);?>">
                <?php endif; ?>

                <div class="card <?= ($shop_billing_feature->enabled && $can_trial || $product_details->billing_type === 'Free') ? 'd-none' : '' ?>">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Chu kỳ thanh toán</h5>
                    </div>
                    <div class="card-body pt-0">
                        <?php if ($promotion): ?>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="billing_cycle" id="billing<?= $promotion->billing_cycle ?>" value="<?= $promotion->billing_cycle ?>" <?= $billing_cycle == $promotion->billing_cycle ? 'checked' : '' ?> onchange="updateCart('cart_form');">
                                <label class="form-check-label" for="billing<?= $promotion->billing_cycle ?>">
                                    <?= get_text_by_billing_cycle($promotion->billing_cycle) ?>
                                    <?php if ($promotion->billing_cycle === 'annually' && ! $promotion): ?>
                                        - Giảm 30%
                                    <?php endif; ?>
                                </label>
                            </div>
                        <?php else: ?>
                            <?php if ($shop_billing_feature->enabled && $can_trial): ?>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="billing_cycle" id="billingMonthly" value="monthly" <?= $billing_cycle == "monthly" ? 'checked' : '' ?> onchange="updateCart('cart_form');" data-billing-cycle-text="<?= $trial_days ?> ngày dùng thử">
                                <label class="form-check-label" for="billingMonthly">
                                    Theo tháng
                                </label>
                            </div>
                            <?php else: ?>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="billing_cycle" id="billingMonthly" value="monthly" <?= $billing_cycle == "monthly" ? 'checked' : '' ?> onchange="updateCart('cart_form');" data-billing-cycle-text="<?= get_text_by_billing_cycle('monthly') ?>">
                                <label class="form-check-label" for="billingMonthly">
                                    Theo tháng
                                </label>
                            </div>
                            <?php if($product_details->billing_type != "Free"): ?>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="billing_cycle" id="billingAnnually" value='annually' <?= $billing_cycle == "annually" ? 'checked' : '' ?> onchange="updateCart('cart_form');" data-billing-cycle-text="<?= get_text_by_billing_cycle('annually') ?>">
                                    <label class="form-check-label" for="billingAnnually">
                                        Theo năm - Giảm 30%
                                    </label>
                                </div>
                            <?php endif; ?>
                            <?php endif ?>
                        <?php endif; ?>
                    </div>
                </div>

                <?php if ($is_shop_billing_product): ?>
                    <input type="hidden"
                        name="shop_plan"
                        data-addon-price-monthly="<?= $product_details->addon->price_monthly ?>"
                        data-addon-price-annually="<?= $product_details->addon->price_annually ?>"
                    />

                    <div class="card">
                        <div class="card-header pb-0">
                            <h5 class="card-title mb-0">Số lượng cửa hàng <span class="shop-count badge bg-primary text-base d-inline-flex"><?= $shop_count ?></span>
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="d-flex align-items-center shop-slider" style="gap: 0.5rem;">
                                <button class="btn btn-light btn-sm btn-decrement" type="button"><i class="bi bi-dash-lg"></i></button>
                                <input type="range" class="form-range shop-range" name="shop_count" min="1" max="<?= esc($shop_addon_limit) ?>" value="<?= esc($shop_count) ?>" step="1" />
                                <button class="btn btn-light btn-sm btn-increment" type="button"><i class="bi bi-plus-lg"></i></button>
                            </div>

                            <?php if ($subscription_details): ?>
                            <div id="decrement-shop-pricing" class="mt-3 <?= !$is_change_shop_count || $subscription_details->shop_limit <= $shop_count ? 'd-none' : '' ?>">
                                <p>Bạn đang thực hiện <span class="text-warning fw-bold">giảm <span class="decrement-shop-count"><?= $subscription_details->shop_limit - $shop_count ?></span> cửa hàng</span></p>
                                <table class="table table-sm text-sm">
                                    <tbody>
                                        <tr>
                                            <th>Thời gian hiệu lực</th>
                                            <td>
                                                <b><?= date('Y-m-d') ?></b> đến <b><?= $subscription_details->end_date ?></b> (<?= calc_subscription_remaining_days($subscription_details->end_date) ?> ngày)
                                            </td>
                                        </tr>
                                        <tr>
                                            <th>Đơn giá theo thời gian hiệu lực</th>
                                            <td>
                                                <span class="decrement-shop-unit-price"><?= number_format(calc_subscription_exchange_remaining_money($subscription_details->begin_date, $subscription_details->end_date, $billing_cycle === 'annually' ? $product_details->addon->price_annually * 12 : $product_details->addon->price_monthly)) ?>đ</span>
                                            </td>
                                        </tr>
                                        <tr class="table-success">
                                            <th>Hoàn trả về ví</th>
                                            <td>
                                                <span class="decrement-shop-price text-success fw-bold">+<?= number_format(($subscription_details->shop_limit - $shop_count) * calc_subscription_exchange_remaining_money($subscription_details->begin_date, $subscription_details->end_date, $billing_cycle === 'annually' ? $product_details->addon->price_annually * 12 : $product_details->addon->price_monthly)) ?>đ</span>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>

                            <div id="increment-shop-pricing" class="mt-3 <?= !$is_change_shop_count || $subscription_details->shop_limit >= $shop_count ? 'd-none' : '' ?>">
                                <div>
                                    <p>Bạn đang thực hiện <span class="text-success fw-bold">thêm <span class="increment-shop-count"><?= $shop_count - $subscription_details->shop_limit ?></span> cửa hàng</span></p>
                                    <table class="table table-sm text-sm">
                                        <tbody>
                                            <tr>
                                                <th>Thời gian hiệu lực</th>
                                                <td>
                                                    <b><?= date('Y-m-d') ?></b> đến <b><?= $subscription_details->end_date ?></b> (<?= calc_subscription_remaining_days($subscription_details->end_date) ?> ngày)
                                                </td>
                                            </tr>
                                            <tr>
                                                <th>Đơn giá theo thời gian hiệu lực</th>
                                                <td>
                                                    <span class="increment-shop-unit-price"><?= number_format(calc_subscription_exchange_remaining_money($subscription_details->begin_date, $subscription_details->end_date, $billing_cycle === 'annually' ? $product_details->addon->price_annually * 12 : $product_details->addon->price_monthly)) ?>đ</span>
                                                </td>
                                            </tr>
                                            <tr class="table-info">
                                                <th>Cần thanh toán thêm</th>
                                                <td>
                                                    <span class="increment-shop-price"><?= number_format(($shop_count - $subscription_details->shop_limit) * calc_subscription_exchange_remaining_money($subscription_details->begin_date, $subscription_details->end_date, $billing_cycle === 'annually' ? $product_details->addon->price_annually * 12 : $product_details->addon->price_monthly)) ?>đ</span>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            
                            <div class="mt-3 <?= !$is_change_shop_count ? 'd-none' : '' ?>" id="ref-shop-pricing">
                                <div class="alert border border-warning">
                                    <div class="alert-message"><i class="bi bi-exclamation-triangle-fill text-warning"></i> Gói sẽ được gia hạn tự động với hóa đơn theo số lượng cửa hàng mới ở chu kỳ thanh toán tiếp theo

                                    <div class="accordion mt-2">
                                        <div class="accordion-item">
                                            <h2 class="accordion-header">
                                                <button class="accordion-button collapsed p-2 text-sm" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne"
                                                    aria-expanded="true" aria-controls="collapseOne">
                                                    Xem bảng giá chu kỳ tiếp theo
                                                </button>
                                            </h2>
                                            <div id="collapseOne" class="accordion-collapse collapse" data-bs-parent="#ref-shop-pricing">
                                                <div class="accordion-body bg-white p-0">
                                                    <?php if ($shop_billing_feature->determineIfSamePriceBetweenProductAndAddon()): ?>
                                                    <table class="table table-sm text-sm mb-0">
                                                        <thead>
                                                            <tr>
                                                                <th></th>
                                                                <th>Cửa hàng</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            <tr>
                                                                <td>Đơn giá</td>
                                                                <td>
                                                                    <span class="shop-addon-unit-price"><?= number_format($billing_cycle === 'annually' ? $product_details->addon->price_annually * 12 : $product_details->addon->price_monthly) ?>đ</span>/cửa hàng/<span class="billing-cycle-text"><?= $billing_cycle === 'annually' ? 'năm' : 'tháng' ?></span></span>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td>Số lượng</td>
                                                                <td>
                                                                    <span class="addon-shop-count"><?= $shop_count ?></span>
                                                                </td>
                                                            </tr>
                                                            <tr class="fw-bold">
                                                                <td class="border-0">Thành tiền</td>
                                                                <td class="border-0">
                                                                    <span class="shop-addon-price">
                                                                        <?= number_format(($billing_cycle === 'annually' ? $product_details->addon->price_annually * 12 : $product_details->addon->price_monthly) * $shop_count) ?>đ /<span class="billing-cycle-text"><?= $billing_cycle === 'annually' ? 'năm' : 'tháng' ?></span>
                                                                    </span>
                                                                </td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                    <?php else: ?>
                                                    <table class="table table-sm text-sm mb-0">
                                                        <thead>
                                                            <tr>
                                                                <th></th>
                                                                <th>Cửa hàng đầu tiên</th>
                                                                <th>Cửa hàng tiếp theo</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            <tr>
                                                                <td>Đơn giá</td>
                                                                <td>
                                                                    <span class="first-shop-addon-price"><?= number_format($billing_cycle === 'annually' ? $product_details->price_annually * 12 : $product_details->price_monthly) ?>đ /<span class="billing-cycle-text"><?= $billing_cycle === 'annually' ? 'năm' : 'tháng' ?></span></span>
                                                                </td>
                                                                <td>
                                                                    <span class="shop-addon-unit-price"><?= number_format($billing_cycle === 'annually' ? $product_details->addon->price_annually * 12 : $product_details->addon->price_monthly) ?>đ</span>/cửa hàng/<span class="billing-cycle-text"><?= $billing_cycle === 'annually' ? 'năm' : 'tháng' ?></span></span>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td>Số lượng</td>
                                                                <td>
                                                                    1
                                                                </td>
                                                                <td>
                                                                    <span class="addon-shop-count"><?= $shop_count - 1 ?></span>
                                                                </td>
                                                            </tr>
                                                            <tr class="fw-bold">
                                                                <td class="border-0">Thành tiền</td>
                                                                <td class="border-0">
                                                                    <span class="first-shop-addon-price"><?= number_format($billing_cycle === 'annually' ? $product_details->price_annually * 12 : $product_details->price_monthly) ?>đ /<span class="billing-cycle-text"><?= $billing_cycle === 'annually' ? 'năm' : 'tháng' ?></span></span>
                                                                </td>
                                                                <td class="border-0">
                                                                    <span class="shop-addon-price">
                                                                        <?php if ($shop_count - 1 > 0): ?>
                                                                            <?= number_format(($billing_cycle === 'annually' ? $product_details->addon->price_annually * 12 : $product_details->addon->price_monthly) * ($shop_count - 1)) ?>đ /<span class="billing-cycle-text"><?= $billing_cycle === 'annually' ? 'năm' : 'tháng' ?></span>
                                                                        <?php else: ?>
                                                                            &mdash;
                                                                        <?php endif ?>
                                                                    </span>
                                                                </td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                    <?php endif ?>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    </div>
                                </div>

                                
                            </div>
                            <?php endif ?>

                            <?php if ($shop_billing_feature->determineIfSamePriceBetweenProductAndAddon()): ?>
                            <div id="shop-pricing" class="<?= $is_change_shop_count ? 'd-none' : '' ?>">
                                <table class="table table-sm text-sm mb-0 mt-2">
                                    <thead>
                                        <tr>
                                            <th></th>
                                            <th>Cửa hàng</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>Đơn giá chu kỳ</td>
                                            <td>
                                                <span class="shop-addon-unit-price"><?=  number_format($billing_cycle === 'annually' ? $product_details->addon->price_annually * 12 : $product_details->addon->price_monthly) ?>đ</span>/cửa hàng/<span class="billing-cycle-text"><?= $billing_cycle === 'annually' ? 'năm' : 'tháng' ?></span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>Số lượng</td>
                                            <td>
                                                <span class="addon-shop-count"><?= $shop_count ?></span>
                                            </td>
                                        </tr>
                                        <tr class="fw-bold table-success">
                                            <td>Thành tiền</td>
                                            <td>
                                                <span class="shop-addon-price">
                                                    <?= number_format(($billing_cycle === 'annually' ? $product_details->addon->price_annually * 12 : $product_details->addon->price_monthly) * $shop_count) ?>đ /<span class="billing-cycle-text"><?= $billing_cycle === 'annually' ? 'năm' : 'tháng' ?></span>
                                                </span>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <?php else: ?>
                            <div id="shop-pricing" class="<?= $is_change_shop_count ? 'd-none' : '' ?>">
                                <table class="table table-sm text-sm mb-0 mt-2">
                                    <thead>
                                        <tr>
                                            <th></th>
                                            <th>Cửa hàng đầu tiên</th>
                                            <th>Cửa hàng tiếp theo</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>Đơn giá chu kỳ</td>
                                            <td>
                                                <span class="first-shop-addon-price"><?= number_format($billing_cycle === 'annually' ? $product_details->price_annually * 12 : $product_details->price_monthly) ?>đ /<span class="billing-cycle-text"><?= $billing_cycle === 'annually' ? 'năm' : 'tháng' ?></span></span>
                                            </td>
                                            <td>
                                                <span class="shop-addon-unit-price"><?= number_format($product_details->addon->price_annually * 12) ?>đ</span>/cửa hàng/<span class="billing-cycle-text"><?= $billing_cycle === 'annually' ? 'năm' : 'tháng' ?></span></span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>Số lượng</td>
                                            <td>
                                                1
                                            </td>
                                            <td>
                                                <span class="addon-shop-count"><?= $shop_count - 1 ?></span>
                                            </td>
                                        </tr>
                                        <tr class="fw-bold table-success">
                                            <td>Thành tiền</td>
                                            <td>
                                                <span class="first-shop-addon-price"><?= number_format($billing_cycle === 'annually' ? $product_details->price_annually * 12 : $product_details->price_monthly) ?>đ /<span class="billing-cycle-text"><?= $billing_cycle === 'annually' ? 'năm' : 'tháng' ?></span></span>
                                            </td>
                                            <td>
                                                <span class="shop-addon-price">
                                                    <?php if ($shop_count - 1 > 0): ?>
                                                        <?= number_format(($billing_cycle === 'annually' ? $product_details->addon->price_annually * 12 : $product_details->addon->price_monthly) * ($shop_count - 1)) ?>đ /<span class="billing-cycle-text"><?= $billing_cycle === 'annually' ? 'năm' : 'tháng' ?></span>
                                                    <?php else: ?>
                                                        &mdash;
                                                    <?php endif ?>
                                                </span>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <?php endif ?>
                            
                            <?php if ($shop_billing_feature->enabled && $can_trial): ?>
                            <div class="alert alert-warning mt-3 mb-0">
                                <div class="alert alert-message mb-0">
                                    <i class="bi bi-exclamation-circle-fill me-2"></i> Không thể thay đổi số lượng cửa hàng trong thời gian dùng thử.
                                </div>
                            </div>
                            <?php endif ?>
                        </div>
                    </div>    
                <?php endif ?>

<!--
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Tùy chọn thêm</h5>
                    </div>
                    <div class="card-body pt-0">
                        <?php foreach($addons as $addon): ?>
                        <div class="">
                            <label class="form-label">
                                <input type="checkbox" name="addon[<?= $addon->id;?>]" value="1"
                                    class="form-check-input" onchange="updateCart('cart_form');">
                                <span class="form-check-label"><?= esc($addon->name);?></span>
                            </label>
                        </div>
                        <?php endforeach;?>
                    </div>
                </div>
-->
        
            </form>

                <div class="card">
                    <div class="card-body">
                        <div class="text-center">
                            <button type="button" onclick="finishCart('cart_form', this)" class="btn btn-primary">Tiếp tục</button>
                        </div>
                        <div class="text-muted text-center pt-4">
                            <p class="mx-auto mb-0" style="max-width:700px"> Bằng việc đặt hàng, tôi chấp nhận <a href="https://sepay.vn/terms-of-service.html" target="_blank">Quy định sử dụng dịch vụ</a> và <a href="https://sepay.vn/privacy.html" target="_blank">Chính sách riêng tư</a> của SePay.</p>
                        </div>
                    </div>
                </div>


            </div>
            <div class="col-lg-5">

                <div class="card" style="position: sticky; top: 0px;">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Thông tin đơn hàng</h5>
                    </div>
                    
                    <div class="card-body py-0" id="cartSummary">
                    <?php include(APPPATH . 'Views/company/cart_sumary.php');?>


                    </div>
                </div>

            </div>
        </div>


    </div>
</main>

<?php include(APPPATH . 'Views/templates/autopay/inc_footer.php');?>


</div>
</div>



<script src="<?php echo base_url();?>/assets/js/bootstrap.bundle.min.js"></script>

<script src="<?php echo base_url();?>/assets/js/jquery-3.5.1.js"></script>

<script src="<?php echo base_url();?>/assets/notyf/notyf.min.js"></script>

<script src="<?php echo base_url();?>/assets/js/app.js?v=1"></script>

<script>
var subscriptionDetails = <?= isset($subscription_details) ? json_encode($subscription_details) : 'null' ?>

// Show #input-auto-renew only when payment_method=Card is selected
$(document).ready(function() {
    function toggleAutoRenew() {
        if ($('input[name="payment_method"]:checked').val() === 'Card') {
            $('#input-auto-renew').show();
        } else {
            $('#input-auto-renew').hide();
        }
    }
    // Initial check
    toggleAutoRenew();
    // Listen for changes
    $('input[name="payment_method"]').on('change', toggleAutoRenew);
});

if (typeof gtag === 'function') {
    gtag('event', 'begin_checkout', {
        currency: 'VND',
        value: <?= $total_money ?>,
        items: [
            {
                item_id: '<?= $product_details->id ?>',
                item_name: '<?= $product_details->name ?>',
                price: <?= $item_price ?>,
                quantity: 1,
            },
        ],
    });
}

function isEmpty(obj) {
    if (typeof obj == "string") {
        if (obj.length > 0)
            return false;
    }
    for (var prop in obj) {
        if (obj.hasOwnProperty(prop))
            return false;
    }

    return true;
}

$.extend($.fn, {

addLoader: function() {
    
        var totalWidth = $(this).width();
        var totalHeight = $(this).height();

        totalWidth += parseInt($(this).css("padding-left"), 10) + parseInt($(this).css("padding-right"), 10);
        totalHeight += parseInt($(this).css("padding-top"), 10) + parseInt($(this).css("padding-bottom"), 10);
        if ($('#preloader').length > 0) {
            $('#preloader').css({
                width: totalWidth + 'px',
                height: totalHeight + 'px'
            });
            $('#preloader').show();
        } else {
            var cont = '<div id="preloader" style="position:absolute;top:' + $(this).position().top + 'px;left:' + $(this).position().left + 'px;width:' + totalWidth + 'px;height:' + totalHeight + 'px;"></div> ';
            $(this).append(cont);
        }
    },

});

function ajax_update(url, params, update, swirl) {
    if (swirl)
        $(update).html('<center><img src="ajax-loading.gif" /></center>');

    if (params != undefined && isEmpty(params)) {
        params = {
            empty1m: 'param'
        };
    }
    $.post(url, params, function(data) {

        var resp = parse_response(data);
        if (!resp) {
            $(update).html('');
            return false;
        } else {

            $(update).html(resp);
        }

    });

}

function apply_coupon() {
    coupon_value = $("input[name=coupon_value]").val();
    $('#coupon_apply').val(coupon_value);
  //  alert('Mã giảm giá không tồn tại');
  updateCart('cart_form');
}

function apply_credit() {
    const creditValue = $("input[name=credit]").val();
    $('#credit_apply').val(creditValue);
    updateCart('cart_form');
}

function updateCart(forms) {
    const currentUrl = new URL(window.location.href);
    const searchParams = new URLSearchParams(currentUrl.search);

    $('#cartSummary').addLoader();
    form_elm = '#' + forms;
    $.ajax({
        url : $(form_elm).attr('action') + '&get_cart_summary=yes' + '&' + searchParams.toString(),
        type: "POST",
        data: $(form_elm).serialize(),
        //dataType: "JSON",
        success: function(data)
        {
            if (data.status == false && data.redirect_to) {
                location.href = data.redirect_to
            } else {
                $('.billing-cycle-full-text').html($('input[name=billing_cycle]:checked').data('billing-cycle-text'))

                <?php if ($is_shop_billing_product && $subscription_details && $product_details->id === $subscription_details->plan_id): ?>
                if ($('input[name=billing_cycle]:checked').val() == '<?= $subscription_details->billing_cycle ?>') {
                    $('.change-plan-icon').addClass('d-none')
                    $('.change-plan-card').addClass('d-none')
                } else {
                    $('.change-plan-icon').removeClass('d-none')
                    $('.change-plan-card').removeClass('d-none')
                    $('#increment-shop-pricing').addClass('d-none')
                    $('#decrement-shop-pricing').addClass('d-none')
                    $('#ref-shop-pricing').addClass('d-none')
                    $('#shop-pricing').removeClass('d-none')
                }
                <?php endif ?>

                $('#cartSummary').html(data);              
            }
        }
    });
}

function finishCart(forms, button) {
    form_elm = '#' + forms;

    $.ajax({
        url : '<?= base_url('company/ajax_add_order') . (($shop_billing_feature->enabled && $can_trial) ? "?is_trial=1" : ""); ?>',
        type: "POST",
        data: $(form_elm).serialize(),
        dataType: "JSON",
        beforeSend: function() {
            $(button).prop('disabled', true).prepend('<span class="spinner-border spinner-border-sm align-middle me-2" role="status" aria-hidden="true"></span>');
        },
        success: function(data)
        {
            if(data.status == true) {
                let coupon = $('#coupon_apply').val() == '' ? null : $('#coupon_apply').val();

                if (typeof gtag === 'function') {
                    gtag('event', 'purchase', {
                        currency: 'VND',
                        value: <?= $total_money ?>,
                        transaction_id: data.invoice_id,
                        coupon: coupon,
                        items: [
                            {
                                item_id: '<?= $product_details->id ?>',
                                item_name: '<?= $product_details->name ?>',
                                price: <?= $item_price ?>,
                                quantity: 1,
                                item_variant: $('input[name=billing_cycle]:checked').val(),
                                coupon: coupon,
                                discount: data.discount,
                            },
                        ],
                    });
                }

                if(data.invoice_id > 0)
                    location.href = '<?= base_url('company/ordersuccess?invoice_id=');?>'+data.invoice_id; 
                else
                    location.href = '<?= base_url('company/ordersuccess');?>';    
            } else {
                notyf.error({
                    message: data.message,
                    dismissible: true
                });
            }
        },
        complete: function() {
            $(button).prop('disabled', false).find('span').remove();
        }
    });
}

<?php if ($product_details->shop_limit > 0): ?>
const isChangeShopCount = <?= isset($is_change_shop_count) && $is_change_shop_count ? 'true' : 'false' ?>

<?php if ($shop_billing_feature->determineIfSamePriceBetweenProductAndAddon()): ?>
$('.shop-slider .shop-range').on('input change', (e) => {
    const amount = parseInt(e.currentTarget.value)

    $('.shop-count').html(amount)

    updateShopChangeUi()

    $('.addon-shop-count').html(amount)
    updateShopCalculation()
})
<?php else: ?>
$('.shop-slider .shop-range').on('input change', (e) => {
    const amount = parseInt(e.currentTarget.value)

    $('.shop-count').html(amount)

    updateShopChangeUi()

    $('.addon-shop-count').html(amount - 1)
    updateShopCalculation()
})
<?php endif ?>

$('.shop-slider .shop-range').on('mouseup', (e) => {
    updateCart('cart_form');
    updateShopCalculation()
})

$('.shop-slider .btn-decrement').on('click', (e) => {
    const range = $(e.currentTarget).parent().find('.shop-range')

    range.val(parseInt(range.val()) - 1)
    range.trigger('input')

    updateCart('cart_form');
    updateShopCalculation()
})

$('.shop-slider .btn-increment').on('click', (e) => {
    const range = $(e.currentTarget).parent().find('.shop-range')

    range.val(parseInt(range.val()) + 1)
    range.trigger('input')

    updateCart('cart_form');
    updateShopCalculation()
})

$('input[name=billing_cycle]').on('change', (e) => {
    const cycle = $(e.currentTarget).val()
    const addonPriceAnnually = parseInt($('input[name=shop_plan]').data('addon-price-annually'))
    const addonPriceMonthly = parseInt($('input[name=shop_plan]').data('addon-price-monthly'))

    $('.shop-addon-unit-price').html(new Intl.NumberFormat('en-US').format(cycle === 'annually' ? addonPriceAnnually * 12 : addonPriceMonthly) + 'đ')
    $('.billing-cycle-text').html(cycle === 'annually' ? 'năm' : 'tháng')

    updateShopCalculation()
    updateShopChangeUi()
})

<?php if ($shop_billing_feature->determineIfSamePriceBetweenProductAndAddon()): ?>
function updateShopCalculation() {
    const cycle = $('input[name=billing_cycle]:checked').val()
    const amount = parseInt($('.shop-slider .shop-range').val())
    const addonPriceAnnually = parseInt($('input[name=shop_plan]').data('addon-price-annually'))
    const addonPriceMonthly = parseInt($('input[name=shop_plan]').data('addon-price-monthly'))
    const priceMonthly = <?= $product_details->price_monthly ?>;
    const priceAnnually = <?= $product_details->price_annually ?>;

    $('.shop-addon-price').html((amount) > 0 ? new Intl.NumberFormat('en-US').format((amount) * (cycle === 'annually' ? addonPriceAnnually * 12 : addonPriceMonthly)) + 'đ /' + (cycle === 'annually' ? 'năm' : 'tháng') : '&mdash;')
    
    var url = new URL(window.location.href)
    url.searchParams.set('shop_count', amount)
    window.history.pushState('', '', url);
}
<?php else: ?>
function updateShopCalculation() {
    const cycle = $('input[name=billing_cycle]:checked').val()
    const amount = parseInt($('.shop-slider .shop-range').val())
    const addonPriceAnnually = parseInt($('input[name=shop_plan]').data('addon-price-annually'))
    const addonPriceMonthly = parseInt($('input[name=shop_plan]').data('addon-price-monthly'))
    const priceMonthly = <?= $product_details->price_monthly ?>;
    const priceAnnually = <?= $product_details->price_annually ?>;

    $('.first-shop-addon-price').html(new Intl.NumberFormat('en-US').format(cycle === 'annually' ? priceAnnually * 12 : priceMonthly) + 'đ /' + (cycle === 'annually' ? 'năm' : 'tháng'))
    $('.shop-addon-price').html((amount - 1) > 0 ? new Intl.NumberFormat('en-US').format((amount - 1) * (cycle === 'annually' ? addonPriceAnnually * 12 : addonPriceMonthly)) + 'đ /' + (cycle === 'annually' ? 'năm' : 'tháng') : '&mdash;')
    
    var url = new URL(window.location.href)
    url.searchParams.set('shop_count', amount)
    window.history.pushState('', '', url);
}
<?php endif ?>

function updateShopChangeUi() {
    if (!isChangeShopCount) return;
    
    const amount = parseInt($('.shop-slider .shop-range').val())

    if ($('#increment-shop-pricing') && subscriptionDetails && $('input[name=billing_cycle]:checked').val() == '<?= $subscription_details ? $subscription_details->billing_cycle : '' ?>') {
        if (amount - subscriptionDetails.shop_limit > 0) {
            const unitPrice = $('.increment-shop-unit-price').html().replace(/[^0-9]+/g, '')

            $('#increment-shop-pricing').removeClass('d-none')
            $('.increment-shop-count').html(amount - subscriptionDetails.shop_limit)
            $('.increment-shop-price').html(new Intl.NumberFormat('en-US').format((amount - subscriptionDetails.shop_limit) * unitPrice) + 'đ')

            $('#decrement-shop-pricing').addClass('d-none')
            $('.decrement-shop-count').html('')
            $('.decrement-shop-price').html('')

            $('#ref-shop-pricing').removeClass('d-none');
            $('#shop-pricing').addClass('d-none');
        } else if (amount - subscriptionDetails.shop_limit < 0) {
            const unitPrice = $('.decrement-shop-unit-price').html().replace(/[^0-9]+/g, '')

            $('#increment-shop-pricing').addClass('d-none')
            $('.increment-shop-count').html('')
            $('.increment-shop-price').html('')

            $('#decrement-shop-pricing').removeClass('d-none')
            $('.decrement-shop-count').html(subscriptionDetails.shop_limit - amount)
            $('.decrement-shop-price').html('+' + new Intl.NumberFormat('en-US').format((subscriptionDetails.shop_limit - amount) * unitPrice) + 'đ')

            $('#ref-shop-pricing').removeClass('d-none');
            $('#shop-pricing').addClass('d-none');
        } else {
            $('#increment-shop-pricing').addClass('d-none')
            $('.increment-shop-count').html('')
            $('.increment-shop-price').html('')

            $('#decrement-shop-pricing').addClass('d-none')
            $('.decrement-shop-count').html('')
            $('.decrement-shop-price').html('')

            $('#ref-shop-pricing').addClass('d-none');
            $('#shop-pricing').removeClass('d-none');
        }
    }
}
<?php endif ?>

<?php if ($company_details->credit_balance > 0): ?>
$(document).on('click', '#btn-open-credit', () => {
    $('#credit-input-group').removeClass('d-none');
    $('#btn-open-credit').addClass('d-none');
    $('#btn-close-credit').removeClass('d-none');
})

$(document).on('click', '#btn-close-credit', () => {
    $('#credit-input-group').addClass('d-none');
    $('#btn-open-credit').removeClass('d-none');
    $('#btn-close-credit').addClass('d-none');
    $('#credit_apply').val('');

    updateCart('cart_form')
})
<?php endif ?>
</script>