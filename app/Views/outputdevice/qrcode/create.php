<?php

$request = service('request');
$bank_account_id = $request->getGet('bank_account_id') ?? null;
$isSpeakerBillingSubscription = is_speaker_billing_subscription();
$isShopBillingSubscription = is_shop_billing_subscription();

if ($isShopBillingSubscription) {
    is_bank_box_support(true);
}

?>
<style>
    input[type="number"]::-webkit-outer-spin-button,
    input[type="number"]::-webkit-inner-spin-button {
        -webkit-appearance: none;
        margin: 0;
    }

    .select2-container--default .select2-selection--single {
        height: 34px;
        border-color: var(--bs-border-color);
        border-radius: 0.375rem;
        display: flex;
        align-items: center;
        background-color: var(--bs-white);
    }
    
    .select2-container--default .select2-selection--single .select2-selection__arrow {
        top: 4px;
    }
    
    #select2-subaccount-select-container {
        color: var(--bs-dark);
    }
    
    .select2-container--default .select2-search--dropdown {
        padding: 0;
    }

    .select2-container--default .select2-search--dropdown .select2-search__field {
        background: transparent;
        color: var(--bs-dark);
        border: none;
        border-bottom: 1px solid var(--bs-border-color);
        padding: 0.25rem 0.5rem;
    }

    .select2-container .select2-results__options::-webkit-scrollbar {
        width: 4px;
        height: 4px;
    }

    .select2-container .select2-results__options::-webkit-scrollbar-thumb {
        background-color: var(--bs-border-color);
    }

    .select2-container .select2-results__options::-webkit-scrollbar-track {
        background-color: var(--bs-light);
    }

    :focus-visible {
        outline: none;
    }
    
    .select2-results__option {
        display: flex;
    }

    .select2-results__option:hover {
        background-color: var(--bs-light) !important;
        opacity: 0.7;
        transition: opacity 0.3s ease;
    }

    .select2-container--default .select2-results__option--highlighted[aria-selected] {
        background-color: var(--bs-light) !important;
        opacity: 1;
        color: var(--bs-dark);
    }
    
    .select2-dropdown {
        border-color: var(--bs-border-color);
        overflow: hidden;
    }

    #showQRModal {
        background-color: black !important;
    }


    .truncate-text {
        display: block;
        width: 100%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .truncate-text-va {
        display: block;
        width: 70%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        position: relative;
        left: 50%;
        transform: translateX(-50%);
    }


    .checked-label-primary:after {
        position: absolute;
        right: 13px;
        border: solid transparent;
        border-width: 15px 0px 10px 18px;
        border-top-color: #3f80ea;
        content: "";

    }

    .checked-label-warning:after {
        position: absolute;
        right: 13px;
        border: solid transparent;
        border-width: 15px 0px 10px 18px;
        border-top-color: #ffeb3b;
        content: "";

    }

    .checked-label-primary,
    .checked-label-warning {
        width: 99px;
        height: 36px;
        padding: 0px 2px;
        font-size: 0.6rem;
        text-align: left;
        overflow: hidden;
        color: white;
        margin: -34px 0px -2px -7px;
    }

    code {
        display: contents;
    }

    .text-decoration-hover-none {
        text-decoration: none !important;

    }

    .box-bank-account.active {

        border: 1px solid #4a90e2 !important;
        box-shadow: 0px 3px 14px -8px black;
    }

    .box-bank-account:hover {

        border: 1px solid #4a90e2 !important;
        box-shadow: 0px 3px 14px -8px black;
    }

    .nav-scroller {
        position: relative;
        z-index: 2;
        height: 4.75rem;
        overflow-y: hidden;
    }

    .qr_img_position {
        top: <?= $data_device['bank_id'] == 19 ? '36.5%' : '31%'; ?>;
        left: 50%;
        transform: translateX(-50%);
    }
</style>

<?php if (($isSpeakerBillingSubscription || $isShopBillingSubscription) && $canContinueConnectBanks): ?>
<?= component_style('_components/bank-account/bank-box') ?>
<?php endif; ?>

<main class="content d-flex justify-content-center mt-4">
    <div class="container-fluid">
        <div class="mx-auto" style="max-width:900px;">
            <div class="card overflow-hidden">
                <h4 class="py-3 border-bottom text-center mb-0">Tạo mã QR nhận thanh toán</h4>
                <div class="card-body p-0">
                    <div class="row g-0">
                        <div class="col-md-5 col-12 order-1 order-md-0">
                            <div class="d-flex justify-content-center">
                                <div class="position-relative">
                                    <?php
                                        $img_bank_path = FCPATH . 'assets/images/outputdevice/step-' . $data_device['brand_name'] . '.png';
                                        $img_bank = file_exists($img_bank_path) ? base_url('assets/images/outputdevice/step-' . $data_device['brand_name'] . '.png') : base_url('assets/images/outputdevice/step-SePay.png');
                                    ?>
                                    <img class="w-100" src="<?= esc($img_bank) ?>" id="preview-mica" alt="<?= esc($data_device['brand_name']) ?>" />
                                    <div class="position-absolute qr_img_position">
                                        <img decoding="async" id="img_qr_code" class="img-fluid rounded p-2" src="https://qr.sepay.vn/img?acc=&bank=&amount=&des=" alt="">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-7 col-12 d-flex flex-column justify-content-between order-0 order-md-1">
                            <div>
                                <div class="d-flex py-2 justify-content-center align-items-center border-bottom w-100 bg-light" id="qr_brand_logo" style="height:60px;object-fit: contain;">
                                    <img id="qr_logo" class="img-fluid" src="" alt="<?= esc($data_device['brand_name']) ?>" style="width: 50px; height: 50px;display: none;">
                                    <div class="spinner-border spinner-border-sm text-secondary" role="status"></div>
                                </div>
                                <div class="table-responsive">
                                    <table class="table m-0">
                                        <tbody>
                                            <tr style="pointer-events: none;">
                                                <th scope="row" style="width: 200px;">Ngân hàng</th>
                                                <td id="qr_brand_name"><div class="spinner-border spinner-border-sm text-secondary" role="status"></div></td>
                                            </tr>
                                            <tr style="pointer-events: none;">
                                                <th scope="row">Thụ hưởng</th>
                                                <td><span class="fw-bold" id="qr_account_holder_name"><div class="spinner-border spinner-border-sm text-secondary" role="status"></div></span></td>
                                            </tr>
                                            <tr>
                                                <th scope="row" class="d-none d-sm-table-cell" style="pointer-events: none;">Tài khoản ngân hàng</th>
                                                <td colspan="2">
                                                    <p class="d-block d-sm-none mb-2 fw-bold">Tài khoản ngân hàng</p>
                                                    <div class="d-flex align-items-center">
                                                        <select class="form-control select2" id="choices-bank-input" style="width: 100%;"></select>
                                                        <?php if (($isSpeakerBillingSubscription || $isShopBillingSubscription) && $canContinueConnectBanks): ?>
                                                        <button class="btn btn-success ms-2" type="button" onclick="openBankBox(handleBankBoxDoneCallback)"><i class="bi bi-plus-circle-fill"></i></button>
                                                        <?php endif ?>
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr class="subaccount-block <?= $isSpeakerBillingSubscription || $isShopBillingSubscription ? 'd-none' : '' ?>">
                                                <th scope="row" class="d-none d-sm-table-cell <?= $isSpeakerBillingSubscription || $isShopBillingSubscription ? 'subaccount-label-format' : '' ?>" style="pointer-events: none;">
                                                    <?= $isSpeakerBillingSubscription || $isShopBillingSubscription ? 'Số tài khoản <br> nhận thanh toán' : 'Tài khoản ảo (VA)' ?>
                                                </th>
                                                <td colspan="2">
                                                    <p class="d-block d-sm-none mb-2 fw-bold"><?= $isSpeakerBillingSubscription || $isShopBillingSubscription ? 'Số tài khoản nhận thanh toán' : 'Tài khoản ảo (VA)' ?></p>
                                                    <div class="d-flex align-items-center">
                                                        <select class="form-control select2" id="subaccount-select" style="width: 100%;"></select>
                                                        <button class="btn btn-success ms-2" type="button" onclick="openCreateVaModal()"><i class="bi bi-plus-circle-fill"></i></button>
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr class="warning-primary-qr d-none">
                                                <td colspan="2" class="p-0">
                                                    <div class="alert alert-warning text-sm mb-0 rounded-0">
                                                        <div class="alert-message">
                                                            <p class="mb-0"><i class="bi bi-exclamation-triangle-fill text-warning me-1"></i> Tùy chọn này loa sẽ phát thông báo cả các giao dịch không liên quan thanh toán tại cửa hàng như trả lãi tiền gửi (nếu có)...</p>
                                                        </div>
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr class="des-block" style="display:none;">
                                                <th scope="row" style="pointer-events: none;">Nội dung chuyển khoản</th>
                                                <td><span class="fw-bold" id="qr_des_tkp"></span><span class="fw-bold" id="qr_des"></span></td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <div class="p-3 d-flex justify-content-between">
                                <a href="<?= base_url('outputdevice/qrcode/' . $data_device['id']) ?>" class="btn btn-light">
                                    Trở về
                                </a>
                                <button type="submit" class="btn btn-primary btn-continue btn-step-3">
                                    Thêm QR
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>

<div class="offcanvas offcanvas-end" tabindex="-1" id="createVaOffCanvas" aria-labelledby="offcanvasRightLabel">
    <div class="offcanvas-header">
        <h5 id="offcanvasRightLabel"><?= $isSpeakerBillingSubscription || $isShopBillingSubscription ? 'Tạo <span class="subaccount-label">số tài khoản nhận thanh toán</span>' : 'Thêm tài khoản ảo (VA)' ?></h5>
        <button type="button" class="btn-close text-reset" data-bs-dismiss="offcanvas" aria-label="Close"></button>
    </div>
    <div class="offcanvas-body"></div>
</div>

<div class="modal fade" id="appAddModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="appAddModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title va-modal-title" id="vaAddModalLabel">Thêm ứng dụng</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>

            <?= form_open(base_url("ocb/ajax_add_app"), 'id="add-app"') ?>
            <input type="hidden" name="bank_account_id" value="">

            <div class="modal-body m-lg-3">
                <div class="mb-3 form-group d-flex flex-column">
                    <label for="client_id" class="form-label fw-bold">Client ID <span class="text-danger">(*)</span></label>
                    <input type="text" class="form-control" name="client_id">
                    <div class="invalid-feedback"></div>
                </div>

                <div class="mb-3 form-group d-flex flex-column">
                    <label for="client_id" class="form-label fw-bold">Client Secret <span class="text-danger">(*)</span></label>
                    <div class="input-group input-password-group">
                        <input type="password" class="form-control" name="client_secret">
                        <button class="btn btn-outline-secondary btn-toggle" show="true" type="button">
                            <i class="bi bi-eye"></i>
                        </button>
                    </div>
                    <div class="invalid-feedback"></div>
                </div>

                <div class="row mb-3">
                    <div class="col">
                        <div class="form-group d-flex flex-column">
                            <label for="client_id" class="form-label fw-bold">Tên đăng nhập <span class="text-danger">(*)</span></label>
                            <input type="text" class="form-control" name="username">
                            <div class="invalid-feedback"></div>
                        </div>
                    </div>
                    <div class="col">
                        <div class="form-group d-flex flex-column">
                            <label for="exampleFormControlInput2" class="form-label">Mật khẩu <span class="text-danger">(*)</span></label>
                            <div class="input-group input-password-group">
                                <input type="password" class="form-control" name="password">
                                <button class="btn btn-outline-secondary btn-toggle" show="true" type="button">
                                    <i class="bi bi-eye"></i>
                                </button>
                            </div>
                            <div class="invalid-feedback"></div>
                        </div>
                    </div>
                </div>

                <div class="mb-3 form-group">
                    <label for="client_cert" class="form-label fw-bold">Chứng chỉ máy khách <span class="text-danger">(*)</span></label>
                    <textarea class="form-control" name="client_cert" rows="5"></textarea>
                    <div class="invalid-feedback"></div>
                </div>

                <div class="mb-3 form-group">
                    <label for="client_cert" class="form-label fw-bold">Khóa riêng tư <span class="text-danger">(*)</span></label>
                    <textarea class="form-control" name="private_key" rows="5"></textarea>
                    <div class="invalid-feedback"></div>
                </div>

                <div class="mb-3 form-group">
                    <label for="label" class="form-label fw-bold">Tên gợi nhớ</label>
                    <input type="text" class="form-control" name="label" value="">
                    <div class="invalid-feedback"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="submit" class="btn btn-light btn-test d-flex align-items-center" style="gap: 0.25rem">
                    <div class="spinner-border text-dark loader" style="width: 16px; height: 16px; display: none;" role="status"></div> Thử kết nối
                </button>
                <button type="submit" class="btn btn-primary btn-save d-flex align-items-center" style="gap: 0.25rem">
                    <div class="spinner-border text-light loader" style="width: 16px; height: 16px; display: none;" role="status"></div> Thêm
                </button>
            </div>
            <?= form_close() ?>
        </div>
    </div>
</div>

<?php if ($isSpeakerBillingSubscription || $isShopBillingSubscription): ?>
<?= component('_components/bank-account/bank-box') ?>
<?php endif; ?>

<script src="<?= base_url('assets/js/bootstrap.bundle.min.js') ?>"></script>
<script src="<?= base_url('assets/js/jquery-3.5.1.js') ?>"></script>
<script src="<?= base_url('assets/notyf/notyf.min.js') ?>"></script>
<script src="<?= base_url('assets/js/jquery.validate.min.js') ?>"></script>
<script src="<?= base_url('assets/js/jquery.validate.additional-methods.min.js') ?>"></script>
<script src="<?= base_url('assets/js/app.js?v=1') ?>"></script>
<script src="<?= base_url('assets/js/select2.min.js') ?>"></script>
<script>
    const OUTPUT_DEVICE_ID = <?= esc($data_device['id']) ?>;

    $('#appAddModal').on('shown.bs.modal', function () {
        $('#createVaOffCanvas').offcanvas('hide');
    });

    $('#appAddModal').on('hidden.bs.modal', function () {
        $('#createVaOffCanvas').offcanvas('show');
    });

    document.addEventListener('bankAppOCBCreated', function() {
        openCreateVaModal();
    });

    document.addEventListener('bankSubAccountCreated', function() {
        $('#createVaOffCanvas').offcanvas('hide');
        loadAllVa();
    });

    const openCreateVaModal = () => {
        let selectedBankAccountId = $('#choices-bank-input').find(':selected').data('bank_account_id');
        $.ajax({
            type: 'POST',
            url: '<?= base_url($isShopBillingSubscription ? 'store/ajax_get_create_va_modal' : 'outputdevice/ajax_get_create_va_modal') ?>/' + selectedBankAccountId,
            data: {
                '<?= csrf_token() ?>': '<?= csrf_hash() ?>',
                'output_device_id': '<?= $data_device['id'] ?>'
            },
            success: function(response) {
                $('#createVaOffCanvas').find('.offcanvas-body').html(response.data);
                $('#createVaOffCanvas').offcanvas('show');
            },
            error: function() {
                notyf.error({
                    message: "Có lỗi xảy ra, vui lòng thử lại sau.",
                });
            }
        })
    }

    // Show more/less functionality
    $('#btn-show-more').on('click', function() {
        $('fieldset').removeClass('d-none').hide().slideDown();
        $('#btn-show-more').addClass('d-none');
        $('#btn-show-less').removeClass('d-none');
    });

    $('#btn-show-less').on('click', function() {
        $('fieldset').slideUp(function() {
            $(this).addClass('d-none');
        });
        $('#btn-show-more').removeClass('d-none');
        $('#btn-show-less').addClass('d-none');
    });

    let des_tkp_sub_account = "";
    let selectedSmsConnected = 0;
    let selectedApiConnected = 0;

    function initializeBankSelect() {
        $('#choices-bank-input').select2({
            placeholder: "Chọn ngân hàng",
            "language": {
                "noResults": function(){
                    return "Không tìm thấy kết quả";
                }
            },
            allowClear: false,
            templateSelection: function(option) {
                if (!option.id) {
                    return option.text;
                }

                let logoPath = $(option.element).data('logo');
                let accountNumber = option.text;

                if (logoPath) {
                    return $(
                        `<span class="d-flex align-items-center text-dark">
                            <img src="${logoPath}" style="width: 24px; height: 24px; margin-right: 8px;">
                            <span>${accountNumber}</span>
                        </span>`
                    );
                }
                return accountNumber;
            },
            templateResult: function(option) {
                if (!option.id) {
                    return option.text;
                }

                let logoPath = $(option.element).data('logo');
                let accountNumber = option.text;

                if (logoPath) {
                    return $(
                        `<span>
                            <img src="${logoPath}" style="width: 24px; height: 24px; margin-right: 8px;">
                            ${accountNumber}
                        </span>`
                    );
                }
                return accountNumber;
            }
        }).on('select2:open', function() {
            $('body').css('overflow', 'hidden');
        }).on('select2:close', function() {
            $('body').css('overflow', '');
        });
    }

    function loadBankAccounts(defaultBankAccountId = null) {
        $("#qr_brand_name").html('<div class="spinner-border spinner-border-sm text-secondary" role="status"></div>');
        $("#qr_account_holder_name").html('<div class="spinner-border spinner-border-sm text-secondary" role="status"></div>');

        $("#img_qr_code").addClass("opacity-50");
        
        $.ajax({
            url: "<?= base_url('outputdevice/ajax_get_bank_accounts') ?>/" + <?= $data_device['id'] ?>,
            type: "GET",
            dataType: "JSON",
            beforeSend: function() {
                $('#choices-bank-input').empty();
                $('#choices-bank-input').append('<option value="">Đang tải...</option>');
                
                $('#subaccount-select').prop('disabled', true);
                $('.btn-step-3').prop('disabled', true);
            },
            success: function(response) {
                if (response.status) {
                    const bankAccounts = response.data || [];
                    const selectElement = $('#choices-bank-input');
                    selectElement.empty();
                    
                    if (bankAccounts.length > 0) {
                        $('.empty-bank-accounts').hide();
                        $('.bank-information').show();
                        bankAccounts.forEach(bank => {
                            const option = $('<option>', {
                                value: bank.id,
                                'data-account_holder_name': bank.account_holder_name,
                                'data-brand_name': bank.brand_name || '',
                                'data-bank_account_id': bank.id || '',
                                'data-bank_sms_connected': bank.bank_sms_connected,
                                'data-bank_api_connected': bank.bank_api_connected,
                                'data-logo': '<?= base_url() ?>/assets/images/banklogo/' + bank.icon_path,
                                text: bank.account_number,
                            });
                            selectElement.append(option);
                        });

                        initializeBankSelect();
                        
                        if (defaultBankAccountId) {
                            $('#choices-bank-input').val(defaultBankAccountId).trigger('change.select2');
                            var firstOption = $('#choices-bank-input option:selected');
                        } else {
                            var firstOption = $('#choices-bank-input option:first');
                            $('#choices-bank-input').val(firstOption.val()).trigger('change.select2');
                        }
                        
                        selectedSmsConnected = firstOption.data("bank_sms_connected") || 0;
                        selectedApiConnected = firstOption.data("bank_api_connected") || 0;
                        let selectedBrand = firstOption.data("brand_name");
                        
                        renderPreviewMica(selectedBrand)
                        
                        let selectedLogo = firstOption.data("logo");
                        let selectedAhn = firstOption.data("account_holder_name");
                        
                        $("#qr_brand_name").text(selectedBrand);
                        $("#qr_logo").attr("src", selectedLogo).show();
                        $("#qr_logo").siblings('.spinner-border').remove();
                        $("#qr_account_holder_name").text(selectedAhn);
                    } else {
                        $("#qr_brand_name").text("Không có");
                        $("#qr_logo").attr("src", "<?= base_url('assets/images/banklogo/default.png') ?>");
                        $("#qr_account_holder_name").text("Không có");
                        $('.empty-bank-accounts').show();
                        $('.bank-information').hide();
                    }
                    loadAllVa();
                } else {
                    $("#qr_brand_name").text("Lỗi");
                    $("#qr_logo").attr("src", "<?= base_url('assets/images/banklogo/default.png') ?>");
                    $("#qr_account_holder_name").text("Lỗi");
                    
                    notyf.error({
                        message: response.message || "Không thể tải danh sách ngân hàng"
                    });
                }
            },
            error: function(xhr) {
                $("#qr_brand_name").text("Lỗi");
                $("#qr_logo").attr("src", "<?= base_url('assets/images/banklogo/default.png') ?>");
                $("#qr_account_holder_name").text("Lỗi");
                
                notyf.error({
                    message: "Lỗi khi tải danh sách ngân hàng. Vui lòng thử lại sau."
                });
            },
            complete: function() {
                $('#subaccount-select').prop('disabled', false);
                $('.btn-step-3').prop('disabled', false);
                $("#img_qr_code").removeClass("opacity-50");
            }
        });
    }

    $(document).ready(function() {
        
        loadBankAccounts(<?= esc($bank_account_id)?>);

        $('#choices-bank-input').select2({
            placeholder: "Chọn ngân hàng",
            "language": {
                "noResults": function(){
                    return "Không tìm thấy kết quả";
                }
            },
            allowClear: false,
            templateResult: function(option) {
                if (!option.id) {
                    return option.text;
                }

                let logoPath = $(option.element).data('logo'); // Lấy đường dẫn logo từ data-logo
                let accountNumber = option.text;

                if (logoPath) {
                    return $(
                        `<span>
                            <img src="${logoPath}" style="width: 24px; height: 24px; margin-right: 8px;">
                            ${accountNumber}
                        </span>`
                    );
                }
                return accountNumber;
            }
        }).on('select2:open', function() {

            $('body').css('overflow', 'hidden');

        }).on('select2:close', function() {

            $('body').css('overflow', '');
        });

        const firstOption = $('#choices-bank-input option:first');

        $('#choices-bank-input').val(firstOption.val());
        $('#choices-bank-input').trigger('change.select2');
        selectedSmsConnected = firstOption.data("bank_sms_connected") || 0;
        selectedApiConnected = firstOption.data("bank_api_connected") || 0;
        let selectedBrandFr = firstOption.data("brand_name");
        let selectedLogoFr = firstOption.data("logo");
        let selectedAhnFr = firstOption.data("account_holder_name");
        $("#qr_brand_name").text(selectedBrandFr)
        $("#qr_logo").attr("src", selectedLogoFr)
        $("#qr_account_holder_name").text(selectedAhnFr)

        $('#choices-bank-input').on('change', function(e) {
            $('.warning-primary-qr').addClass('d-none');
          
            loadAllVa();
            let selectedValue = $(this).val() || "";

            let selectedOption = $('#choices-bank-input option:selected');

            //reset va
            $("#subaccount-select").val("");
            $(".des-input").val("");
            $("#qr_des").text("");
            $("#qr_des_tkp").text("");
            des_tkp_sub_account = "";

            if (selectedOption.length > 0) {
                // Đảm bảo selectedOption tồn tại trước khi truy cập data
                let selectedBrand = selectedOption.data("brand_name") || "";
                
                renderPreviewMica(selectedBrand);
                
                if (typeof handleSubAccountInputUI === 'function') {
                    handleSubAccountInputUI(selectedBrand);
                }

                selectedSmsConnected = selectedOption.data("bank_sms_connected") || 0;
                selectedApiConnected = selectedOption.data("bank_api_connected") || 0;

                let selectedLogo = selectedOption.data("logo") || "";
                let selectedAhn = selectedOption.data("account_holder_name") || "";
                let selectedText = selectedOption.text() || "";

                if (selectedValue) {
                    $("#qr_brand_name").text(selectedBrand);
                    $("#qr_logo").attr("src", selectedLogo);
                    $("#qr_account_holder_name").text(selectedAhn);
                } else {
                    notyf.error({
                        message: "Lỗi lấy thông tin QR!",
                    });
                }
            } else {
                console.error("Không tìm thấy option được chọn.");
            }
        });


        $('#subaccount-select').on('change', function(e) {
            let selectedValue = $(this).val();
            let selectedValueParent = $("#choices-bank-input").val();
            $("#img_qr_code").removeClass("d-none");
            $("#img_no_qr_code").addClass("d-none");
            $("#text_no_qr_code").addClass("d-none");
            
            let selectedOption = $('#choices-bank-input option:selected');
            
            if (selectedValue == "") {
                if (['VPBank', 'TPBank', 'VietinBank', 'ACB', 'MBBank', 'ABBANK'].includes(selectedOption.data('brand_name')) || selectedOption.data('bank_sms_connected')) {
                    $('.warning-primary-qr').removeClass('d-none');
                    renderImgQr(selectedValueParent);
                    return;
                }
              
                if ($('#choices-bank-input option:selected').length > 0) {

                    $('#choices-bank-input option:selected').trigger("change");
                } else {
                    console.error("Không có bank nào được chọn");
                }
                return
            } else {
                $('.warning-primary-qr').addClass('d-none');
            }

            let selectedBrand = selectedOption.data("brand_name");
            let selectedLogo = selectedOption.data("logo");
            let selectedAhn = selectedOption.data("account_holder_name");
            let selectedText = selectedOption.text();

            // check type va
            let selectedOptionVa = $('#subaccount-select option:selected'); // Truy cập <option> được chọn
            let accType = selectedOptionVa.data('acc_type'); // Lấy giá trị data-acc_type


            if (selectedOptionVa.data('is_decal') && ['BIDV', 'OCB'].includes(selectedOption.data('brand_name'))) {
                handleCreateVaFromOutputDeviceDecalManually();
            }

            // check va bank special
            if (selectedBrand == "TPBank" || selectedBrand == "VietinBank") {
                // swap account_number
                selectedValue = selectedValueParent;
            }
            // check va bank special sms
            if (selectedSmsConnected == 1 && accType == "Virtual") {
                // swap account_number sms
                selectedValue = selectedValueParent;
            }

            if (selectedSmsConnected == 1 && accType == "Real") {
                // swap account_number sms
                if (selectedBrand != "TPBank" && selectedBrand != "VietinBank") {
                    selectedValue = selectedValue;
                }
            }

            if (selectedApiConnected == 1 && accType == "Virtual") {
                // swap account_number sms
                selectedValue = selectedValueParent;
            }


            if (selectedValue) {
                renderImgQr(selectedValueParent);
                $(".noti-connect-bank").hide();

                $("#qr_brand_name").text(selectedBrand)
                $("#qr_logo").attr("src", selectedLogo)
                $("#qr_account_holder_name").text(selectedAhn)

            } else {
                notyf.error({
                    message: "Lỗi lấy thông tin QR!"
                })
            }
        });




        // Ngăn người dùng xóa tùy chọn
        $('#choices-bank-input').on('select2:unselecting', function(e) {
            e.preventDefault();
        });
        // Ngăn người dùng xóa tùy chọn
        $('#subaccount-select').on('select2:unselecting', function(e) {
            e.preventDefault();
        });


        $(".add-amount").on("click", function() {
            $(".amount-input-group").removeClass("d-none").hide().slideDown();
            $(".amount-input").focus();
            $(".amount-input-group").addClass("d-flex");
        });
        $(".add-des").on("click", function() {
            $(".des-input-group").removeClass("d-none").hide().slideDown();
            $(".des-input").focus();
            $(".des-input-group").addClass("d-flex");
        });

        // Sự kiện click vào nút "Submit"
        $(".btn-submit-amount").on("click", function() {
            const amount = $(".amount-input").val().trim();
            if (!amount || isNaN(amount) || amount < 0) {
                $(".amount-input-group").slideUp(function() {
                    $(".amount-block").hide();
                    $(".amount-input-group").removeClass("d-flex");
                });
                // return;
            }

            let imgElement = $("#img_qr_code");
            let currentSrc = imgElement.attr("src");

            // Tạo đối tượng URLSearchParams từ URL hiện tại
            let url = new URL(currentSrc);
            let params = new URLSearchParams(url.search);

            // Thêm hoặc thay thế tham số amount
            params.set("amount", sanitizeInput(amount));

            // Cập nhật lại URL với tham số mới
            url.search = params.toString();
            imgElement.attr("src", url.toString().replace(/%2B/g, "+"));
            $("#qr_amount").text(new Intl.NumberFormat('vi-VN').format(amount) + ' đ');
            if (amount > 0) {

                $(".amount-block").show();
            }
            $(".amount-input-group").slideUp(function() {
                $(".amount-input-group").removeClass("d-flex");
                return

            });



        });

        $(".amount-input").on("keydown", function(event) {
            if (event.key == "Enter") {
                $(".btn-submit-amount").trigger("click");
            }
        });

        // Sự kiện click vào nút "Submit"
        $(".btn-submit-des").on("click", function() {
            let brand_name = $("#qr_brand_name").text();
            const des = sanitizeInput($(".des-input").val().trim());

            let imgElement = $("#img_qr_code");
            let currentSrc = imgElement.attr("src");

            // Tạo đối tượng URLSearchParams từ URL hiện tại
            let url = new URL(currentSrc);
            let params = new URLSearchParams(url.search);

            // Thêm hoặc thay thế tham số amount
            params.set("des", sanitizeInput($("#qr_des_tkp").text()) + sanitizeInput(des));

            // Cập nhật lại URL với tham số mới
            url.search = params.toString();
            imgElement.attr("src", url.toString().replace(/%2B/g, "+"));
            $("#qr_des").text(des);
            if (des != "") {
                checkParamInUrl('des');
                // $(".des-block").show();
            } else {
                // $(".des-block").hide();
            }
            $(".des-input-group").slideUp(function() {
                $(".des-input-group").removeClass("d-flex");
                return

            });



        });

        $(".des-input").on("keydown", function(event) {
            if (event.key == "Enter") {
                $(".btn-submit-des").trigger("click");
            }
        });

    });
    function renderImgQr(bankAccountId) {
        if (! bankAccountId) {
            return;
        }
        
        let selectedOption = $('#choices-bank-input option[value="' + bankAccountId + '"]');
        
        if (!selectedOption.length) {
            selectedOption = $('#choices-bank-input option:selected');
        }
        
        let brand_name = selectedOption.data("brand_name");
        let account_number = selectedOption.text();
        
        if (!brand_name || !account_number) {
            return;
        }
        
        if (typeof handleSubAccountInputUI === 'function') {
            handleSubAccountInputUI(brand_name);
        }
        
        if (brand_name == "LPBank") {
            brand_name = "LienVietPostBank";
        }

        if (brand_name == "BVBank") {
            brand_name = "VietCapitalBank";
        }

        const encodedBrandName = sanitizeInput(brand_name);
        let encodedAccountNumber = sanitizeInput(account_number);

        let des = "";
        if (selectedApiConnected == 1) {
            switch (brand_name) {
                case "VietinBank":
                    des = "SEVQR ";
                    break;
                case "ABBANK":
                    des = "LocVang ";
                    break;
                default:
                    des = "";
                    break;
            }
        }

        let selectedVA = $("#subaccount-select option:selected");
        let valueTKP = selectedVA.data('sub_account');
        let des_tkp_sub_account = "";
        
        if (valueTKP) {
            let accType = selectedVA.data('acc_type');
            
            if(accType == "Real") {
                encodedAccountNumber = valueTKP;
            }

            if ((selectedSmsConnected == 1 && accType == "Virtual") || 
                (selectedApiConnected == 1 && accType == "Virtual") || 
                ["VietinBank", "TPBank", "VPBank"].includes(brand_name)) {
                des_tkp_sub_account = `TKP${valueTKP} `;
            }

            if (['VietinBank', 'ABBANK'].includes(brand_name)) {
                $("#qr_des_tkp").text(`${des}${des_tkp_sub_account}`);
            } else {
                $("#qr_des_tkp").text(des_tkp_sub_account);
            }
            checkParamInUrl('des');
        } else {
            if (brand_name == "VietinBank") {
                $("#qr_des_tkp").text(selectedApiConnected == 1 ? "SEVQR " : "");
            } else if (brand_name == "ABBANK") {
                $("#qr_des_tkp").text(selectedApiConnected == 1 ? "LocVang " : "");
            } else {
                $(".des-input").val("");
                $("#qr_des").text("");
                $(".des-block").hide();
                $("#qr_des_tkp").text('');
            }
        }

        let currentUrl = $("#img_qr_code").attr("src");

        if (currentUrl) {
            let urlParams = new URLSearchParams(currentUrl.split('?')[1]);

            urlParams.set("acc", encodedAccountNumber);
            urlParams.set("bank", encodedBrandName);

            let finalDes = `${$("#qr_des_tkp").text()}${$("#qr_des").text()}`;
            urlParams.set("des", sanitizeInput(finalDes).replace(/%20/g, "+"));

            if (!urlParams.has("amount")) {
                urlParams.set("amount", "");
            }

            let updatedUrl = `${currentUrl.split('?')[0]}?${urlParams.toString()}`;
            $("#img_qr_code").attr("src", updatedUrl.replace(/%2B/g, "+"));
        } else {
            notyf.error({
                message: "Đã có lỗi khi tạo QR, Hãy liên hệ kỹ thuật!"
            });
        }

        checkParamInUrl('des');
    }

    // Hàm kiểm tra tính hợp lệ của URL
    function isValidUrl(url) {
        try {
            new URL(url);
            return true;
        } catch (e) {
            return false;
        }
    }


    function sanitizeInput(input) {
        // Bảng thay thế ký tự có dấu thành không dấu
        const accentMap = {
            'á': 'a',
            'à': 'a',
            'ả': 'a',
            'ã': 'a',
            'ạ': 'a',
            'ă': 'a',
            'ắ': 'a',
            'ằ': 'a',
            'ẳ': 'a',
            'ẵ': 'a',
            'ặ': 'a',
            'â': 'a',
            'ấ': 'a',
            'ầ': 'a',
            'ẩ': 'a',
            'ẫ': 'a',
            'ậ': 'a',
            'é': 'e',
            'è': 'e',
            'ẻ': 'e',
            'ẽ': 'e',
            'ẹ': 'e',
            'ê': 'e',
            'ế': 'e',
            'ề': 'e',
            'ể': 'e',
            'ễ': 'e',
            'ệ': 'e',
            'í': 'i',
            'ì': 'i',
            'ỉ': 'i',
            'ĩ': 'i',
            'ị': 'i',
            'ó': 'o',
            'ò': 'o',
            'ỏ': 'o',
            'õ': 'o',
            'ọ': 'o',
            'ô': 'o',
            'ố': 'o',
            'ồ': 'o',
            'ổ': 'o',
            'ỗ': 'o',
            'ộ': 'o',
            'ơ': 'o',
            'ớ': 'o',
            'ờ': 'o',
            'ở': 'o',
            'ỡ': 'o',
            'ợ': 'o',
            'ú': 'u',
            'ù': 'u',
            'ủ': 'u',
            'ũ': 'u',
            'ụ': 'u',
            'ư': 'u',
            'ứ': 'u',
            'ừ': 'u',
            'ử': 'u',
            'ữ': 'u',
            'ự': 'u',
            'ý': 'y',
            'ỳ': 'y',
            'ỷ': 'y',
            'ỹ': 'y',
            'ỵ': 'y',
            'đ': 'd',
            'Á': 'A',
            'À': 'A',
            'Ả': 'A',
            'Ã': 'A',
            'Ạ': 'A',
            'Ă': 'A',
            'Ắ': 'A',
            'Ằ': 'A',
            'Ẳ': 'A',
            'Ẵ': 'A',
            'Ặ': 'A',
            'Â': 'A',
            'Ấ': 'A',
            'Ầ': 'A',
            'Ẩ': 'A',
            'Ẫ': 'A',
            'Ậ': 'A',
            'É': 'E',
            'È': 'E',
            'Ẻ': 'E',
            'Ẽ': 'E',
            'Ẹ': 'E',
            'Ê': 'E',
            'Ế': 'E',
            'Ề': 'E',
            'Ể': 'E',
            'Ễ': 'E',
            'Ệ': 'E',
            'Í': 'I',
            'Ì': 'I',
            'Ỉ': 'I',
            'Ĩ': 'I',
            'Ị': 'I',
            'Ó': 'O',
            'Ò': 'O',
            'Ỏ': 'O',
            'Õ': 'O',
            'Ọ': 'O',
            'Ô': 'O',
            'Ố': 'O',
            'Ồ': 'O',
            'Ổ': 'O',
            'Ỗ': 'O',
            'Ộ': 'O',
            'Ơ': 'O',
            'Ớ': 'O',
            'Ờ': 'O',
            'Ở': 'O',
            'Ỡ': 'O',
            'Ợ': 'O',
            'Ú': 'U',
            'Ù': 'U',
            'Ủ': 'U',
            'Ũ': 'U',
            'Ụ': 'U',
            'Ư': 'U',
            'Ứ': 'U',
            'Ừ': 'U',
            'Ử': 'U',
            'Ữ': 'U',
            'Ự': 'U',
            'Ý': 'Y',
            'Ỳ': 'Y',
            'Ỷ': 'Y',
            'Ỹ': 'Y',
            'Ỵ': 'Y',
            'Đ': 'D'
        };

        // Loại bỏ dấu
        input = input.split('').map(char => accentMap[char] || char).join('');

        // Escape ký tự đặc biệt để tránh XSS
        input = input.replace(/</g, "&lt;")
            .replace(/>/g, "&gt;")
            .replace(/&/g, "&amp;")
            .replace(/"/g, "&quot;")
            .replace(/'/g, "&#039;");

        return input;
    }

    function checkParamInUrl(paramName) {
        try {
            let url = $("#img_qr_code").attr("src");
            let urlObj = new URL(url); // Chuyển URL thành đối tượng
            let urlParams = new URLSearchParams(urlObj.search); // Lấy phần query string
            let paramValue = urlParams.get(paramName); // Lấy giá trị của tham số cần kiểm tra

            if (paramValue !== null && paramValue !== '') {
                $(".des-block").show();
                return true;
            } else {
                $(".des-block").hide();
                return false;
            }
        } catch (error) {
            $(".des-block").hide();
            return false;
        }
    }

    function loadAllVa () {
        let selectedBankAccountId = $('#choices-bank-input').find(':selected').data('bank_account_id');

        if (! selectedBankAccountId) {
            return;
        }

        $.ajax({
            url: "<?= base_url('outputdevice/ajax_get_bank_sub_accounts')?>" + '/' + selectedBankAccountId + '?output_device_id=' + <?= $data_device['id'] ?>,
            type: "GET",
            dataType: "JSON",
            beforeSend: function() {
                $('#subaccount-select').empty();
                $('#subaccount-select').append('<option value="">Đang tải...</option>');
            },
            success: function(res, textStatus, jqXHR) {
                if (res.status == true) {
                    // Tìm select cần render
                    let selectElement = $('#subaccount-select');
                    // Làm sạch các option cũ
                    selectElement.empty();
                    
                    const selectedBank = $('#choices-bank-input').find(':selected');
                    
                    if (['VPBank', 'TPBank', 'VietinBank', 'ACB', 'MBBank', 'ABBANK'].includes(selectedBank.data('brand_name')) || selectedBank.data('bank_sms_connected')) {
                        selectElement.append($('<option>', {
                            value: '',
                            text: 'Không chọn',
                        }));
                    }
    
                    if (res.data.length == 0){
                        if (['VPBank', 'TPBank', 'VietinBank', 'ACB', 'MBBank', 'ABBANK'].includes(selectedBank.data('brand_name')) || selectedBank.data('bank_sms_connected')) {
                            $('.warning-primary-qr').removeClass('d-none');
                            renderImgQr(selectedBank.data('bank_account_id'));
                        } else {
                            $('#img_qr_code').addClass('d-none');
                            $('.warning-primary-qr').addClass('d-none');
                        }
                    }else{
                        // Lặp qua các tài khoản phụ đã lọc và render chúng vào trong select
                        res.data.forEach((item, index) => {
                            let option = $('<option>', {
                                value: item.id,
                                'data-account_holder_name': item.account_holder_name,
                                'data-bank_sub_account_id': item.id,
                                'data-sub_account': item.sub_account,
                                'data-brand_name': item.brand_name,
                                'data-acc_type': item.acc_type,
                                'data-logo': '<?= base_url() ?>' + '/assets/images/banklogo/' + item.icon_path,
                                'data-is_decal': item.is_decal,
                                text: item.sub_account + (item.label ? ' ' + "(" + item.label + ")" : '') // Thêm item.label nếu có
                            });
    
                            selectElement.append(option);
                            $('#img_qr_code').removeClass('d-none');
    
                            if (index === 0) {
                                if (['BIDV', 'OCB'].includes(item.brand_name) && item.is_decal) {
                                    handleCreateVaFromOutputDeviceDecalManually();
                                }
                                
                                selectElement.val(item.id).trigger('change.select2');
                                renderImgQr(selectedBank.data('bank_account_id'));
                            }
                        });
                        
                        if (res.data.length === 1 && res.data[0].is_decal) {
                            renderImgQr(selectedBank.data('bank_account_id'));
                        }
                    }
                    // Gọi lại select2 sau khi đã render các option mới
                    selectElement.select2({
                        allowClear: false,
                        "language": {
                            "noResults": function(){
                                return "Không tìm thấy kết quả";
                            }
                        },
                        templateResult: function(option) {
                            if (!option.id) {
                                return option.text;
                            }
                            var logoPath = $(option.element).data('logo');
                            var accountNumber = option.text;
    
                            if (logoPath) {
                                return $(
                                    `<span>
                                        ${accountNumber}
                                    </span>`
                                );
                            }
                            return accountNumber;
                        }
                    });
                };
            },
            error: function(err, textStatus, errorThrown) {
                if (err.status == 403) {
                    notyf.error({
                        message: "Hãy nhấn F5 tải lại trang để thử lại!"
                    })
                }
                if (err.status == 404) {
                    notyf.error({
                        message: err.responseJSON.message
                    })
                }
                if (err.status == 422) {
                    notyf.error({
                        message: err.responseJSON.message || "Lỗi dữ liệu!"
                    })
                }
                if (err.status == 423) {
                    notyf.error({
                        message: err.responseJSON.message || "Lỗi dữ liệu!"
                    })
                }
                if (err.status == 500) {
                    notyf.error({
                        message: err.responseJSON.message || "Lỗi hệ thống hãy liên hệ kỹ thuật!"
                    })
                }
            },
            complete: (xhr, textStatus) => {
                let newCsrfToken = xhr.getResponseHeader('X-CSRF-TOKEN');
                if (newCsrfToken) {
                    updateCsrfToken(newCsrfToken);
                }
                
                $('#subaccount-select').prop('disabled', false);
            }
        })
    }

    $(".btn-step-3").on("click", function() {
        if ($('#subaccount-select option:selected').data('is_decal') && ['OCB', 'BIDV'].includes($('#subaccount-select option:selected').data('brand_name'))) {
            handleCreateVaFromOutputDeviceDecalManually();
            return;
        }
        
        let text_brand = $("#qr_brand_name").text();
        if ($("#img_qr_code").hasClass("d-none") && !['VPBank', 'TPBank', 'ACB', 'VietinBank', 'MBBank', 'ABBANK'].includes(text_brand)) {
            notyf.error(`Vui lòng chọn tài khoản ảo (VA)`);
            return;
        }

        let option_bank_account = $("#choices-bank-input").find("option:selected");
        let bank_account_id = option_bank_account.data("bank_account_id");
        //let selectedOptionVa = $('#subaccount-select option:selected'); 
        let option_bank_sub_account = $("#subaccount-select").find("option:selected");

        let bank_sub_account_id = option_bank_sub_account.data("bank_sub_account_id");
        if (!bank_sub_account_id && !['VPBank', 'TPBank', 'ACB', 'VietinBank', 'MBBank', 'ABBANK'].includes(text_brand)) {
            notyf.error(`Vui lòng chọn tài khoản ảo (VA)`);
            return;
        }

        $.ajax({
            url: "/outputdevice/ajax_create_qr",
            type: "POST",
            dataType: "JSON",
            data: {
                '<?= csrf_token() ?>': '<?= csrf_hash() ?>',
                "output_device_id": "<?= $data_device['id'] ?>",
                "bank_account_id": bank_account_id,
                "sub_account_id": bank_sub_account_id,
            },
            success: function(res, textStatus, jqXHR) {
                if (res.code === 200) {
                    window.location.href = '<?= base_url('outputdevice/qrcode').'/'.$data_device['id']?>';
                };

            },
            error: function(err, textStatus, errorThrown) {
                if (err.status == 403) {
                    notyf.error({
                        message: "Hãy nhấn F5 tải lại trang để thử lại!"
                    })
                }
                if (err.status == 404) {
                    notyf.error({
                        message: err.responseJSON.message
                    })
                }
                if (err.status == 422) {
                    notyf.error({
                        message: err.responseJSON.message || "Lỗi dữ liệu!"
                    })
                }
                if (err.status == 423) {
                    notyf.error({
                        message: err.responseJSON.message || "Lỗi dữ liệu!"
                    })
                }
                if (err.status == 500) {
                    notyf.error({
                        message: err.responseJSON.message || "Lỗi hệ thống hãy liên hệ kỹ thuật!"
                    })
                }
            },
        });
    });
    
    function renderPreviewMica(brandName) {
        let imagePath = $('#preview-mica').attr('src').replace(/step\-.*/, `step-${brandName}.png`);

        $.get(imagePath)
        .done(function() {
            $("#preview-mica").attr("src", imagePath);
        })
        .fail(function() {
            $("#preview-mica").attr("src", "<?= base_url() ?>/assets/images/outputdevice/step-SePay.png");
        });
    }
    
    function handleCreateVaFromOutputDeviceDecalManually() {
        openCreateVaModal();
    }
</script>

<?php if ($isSpeakerBillingSubscription || $isShopBillingSubscription): ?>
<?= component_script('_components/bank-account/bank-box') ?>

<script>
function handleBankBoxDoneCallback(id, context) {
    $.ajax({
        url: "/outputdevice/ajax_create_qr",
        type: "POST",
        dataType: "JSON",
        data: {
            '<?= csrf_token() ?>': '<?= csrf_hash() ?>',
            "output_device_id": "<?= $data_device['id'] ?>",
            "bank_account_id": id,
            "sub_account_id": context.first_va_id,
        },
        success: function(res, textStatus, jqXHR) {
            if (res.code === 200) {
                window.location.href = '<?= base_url('outputdevice/qrcode').'/'.$data_device['id']?>';
            };
        },
        error: function(err, textStatus, errorThrown) {
            if (err.status == 403) {
                notyf.error({
                    message: "Hãy nhấn F5 tải lại trang để thử lại!"
                })
            }
            if (err.status == 404) {
                notyf.error({
                    message: err.responseJSON.message
                })
            }
            if (err.status == 422) {
                notyf.error({
                    message: err.responseJSON.message || "Lỗi dữ liệu!"
                })
            }
            if (err.status == 423) {
                notyf.error({
                    message: err.responseJSON.message || "Lỗi dữ liệu!"
                })
            }
            if (err.status == 500) {
                notyf.error({
                    message: err.responseJSON.message || "Lỗi hệ thống hãy liên hệ kỹ thuật!"
                })
            }
        },
    })
}

$('select').on('select2:open', function() {
    $('.select2-search--dropdown .select2-search__field').attr('placeholder', 'Nhập để tìm kiếm...');
});
</script>

<?php if ($isSpeakerBillingSubscription || $isShopBillingSubscription): ?>
<script>
function handleSubAccountInputUI(brandName) {
    $('.subaccount-block').removeClass('d-none');
    $('.subaccount-label-format').html(['VPBank', 'TPBank', 'VietinBank', 'ABBANK'].includes(brandName) ? 'Mã thanh toán' : 'Số tài khoản<br>nhận thanh toán');
    $('.subaccount-label').html(['VPBank', 'TPBank', 'VietinBank', 'ABBANK'].includes(brandName) ? 'Mã thanh toán' : 'Số tài khoản nhận thanh toán');
}
</script>
<?php endif ?>

<?php endif; ?>