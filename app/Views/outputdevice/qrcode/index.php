<?= $this->extend('Views/outputdevice/layout') ?>
<?= $this->section('content') ?>
<div class="card-body">
    <div class="d-flex align-items-center">
        <h4 class="mb-0">Danh sách mã QR</h4>

        <div class="ms-auto">
            <?php if (in_array($company_details->role, ['Admin', 'SuperAdmin'])) { ?>
                <a href="<?= base_url('outputdevice/createqr/' . $data_device['id']) ?>" class="btn btn-sm btn-primary" data-bs-toggle="tooltip" data-bs-title="Tạo mã QR"><i class="bi bi-plus-lg"></i> Tạo mã QR</a>
            <?php } ?>
        </div>
    </div>

    <div class="mt-3">
        <div class="row align-items-stretch g-3">
            <?php if (!empty($data_detail_integration_bank)) { ?>
                <?php foreach ($data_detail_integration_bank as $key => $value) { ?>
                    <div class="col-12 col-sm-6 col-md-4 h-100">
                        <div class="card border mb-0 shadow-none">
                            <div class="position-relative">
                                <div class="d-flex align-items-center justify-content-center p-2">
                                    <img src="<?= base_url() ?>/assets/images/banklogo/<?= $value['icon_path']?>" alt="<?= $value['brand_name']?>" width="30px">
                                    <span class="ms-2"><?= $value['brand_name']?></span>
                                </div>
                                <div class="qr_img_position px-2">
                                    <img decoding="async" id="img_qr_code" class="img-fluid rounded p-2" src="<?= $value['qr_url'] ?>" alt="QR Code">
                                </div>
                            </div>
                            <h5 class="text-center text-primary">
                                Số TK: <?= $value['account_number'] ?>
                            </h5>
                            <?php if ($value['sub_account']): ?>
                            <h5 class="text-center text-primary">
                                <?= is_speaker_billing_subscription() ? 'Mã TT' : 'Số VA' ?>: <?= $value['sub_account'] ?>
                            </h5>
                            <?php else: ?>
                            <h5 class="text-center text-primary">
                                &nbsp;
                            </h5>
                            <?php endif ?>
                            <div class="px-2 py-2 d-flex align-items-center justify-content-center">
                                <a class="btn btn-outline-primary print-link" href="javascript:;" data-brand-name="<?= $value['brand_name'] ?>" data-src="<?= $value['qr_url'] ?>" data-account-number="<?= $value['account_number'] ?>" data-name-account-number="<?= $value['account_holder_name'] ?>">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-printer" viewBox="0 0 16 16">
                                    <path d="M2.5 8a.5.5 0 1 0 0-1 .5.5 0 0 0 0 1"/>
                                    <path d="M5 1a2 2 0 0 0-2 2v2H2a2 2 0 0 0-2 2v3a2 2 0 0 0 2 2h1v1a2 2 0 0 0 2 2h6a2 2 0 0 0 2-2v-1h1a2 2 0 0 0 2-2V7a2 2 0 0 0-2-2h-1V3a2 2 0 0 0-2-2zM4 3a1 1 0 0 1 1-1h6a1 1 0 0 1 1 1v2H4zm1 5a2 2 0 0 0-2 2v1H2a1 1 0 0 1-1-1V7a1 1 0 0 1 1-1h12a1 1 0 0 1 1 1v3a1 1 0 0 1-1 1h-1v-1a2 2 0 0 0-2-2zm7 2v3a1 1 0 0 1-1 1H5a1 1 0 0 1-1-1v-3a1 1 0 0 1 1-1h6a1 1 0 0 1 1 1"/>
                                </svg>
                                    <span>In mã QR</span>
                                </a>
                                <button type="button" class="btn btn-outline-danger ms-2 d-flex align-items-center" onclick="openModalDeleteVA(<?= $value['integration_id'] ?>)">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                                        class="bi bi-x-octagon-fill" viewBox="0 0 16 16">
                                        <path d="M11.46.146A.5.5 0 0 0 11.107 0H4.893a.5.5 0 0 0-.353.146L.146 4.54A.5.5 0 0 0 0 4.893v6.214a.5.5 0 0 0 .146.353l4.394 4.394a.5.5 0 0 0 .353.146h6.214a.5.5 0 0 0 .353-.146l4.394-4.394a.5.5 0 0 0 .146-.353V4.893a.5.5 0 0 0-.146-.353zm-6.106 4.5L8 7.293l2.646-2.647a.5.5 0 0 1 .708.708L8.707 8l2.647 2.646a.5.5 0 0 1-.708.708L8 8.707l-2.646 2.647a.5.5 0 0 1-.708-.708L7.293 8 4.646 5.354a.5.5 0 1 1 .708-.708"></path>
                                    </svg>
                                    <span class="ms-2">Gỡ QR</span>
                                </button>
                            </div>
                        </div>
                    </div>
                <?php } ?>
            <?php } else { ?>
                <div class="col-12">
                    <div class="text-center py-5">
                        <i class="bi bi-exclamation-triangle" style="font-size: 3rem;"></i>
                        <h4>Không có mã QR nào được tạo cho thiết bị này</h4>
                        <p class="text-secondary">Hãy tạo mã QR để sử dụng</p>
                    </div>
                </div>
            <?php } ?>
        </div>
    </div>
</div>

<div class="modal fade" id="deleteModalVA" tabindex="-1">
    <div class="modal-dialog modal-sm modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body text-center py-4">
                <i class="bi bi-exclamation-triangle text-danger" style="font-size: 3rem;"></i>
                <h3 class="fs-4">Bạn có chắc chắn hủy liên kết mã QR với loa ?</h3>
                <div class="text-secondary">
                    Tất cả thông tin liên quan sẽ bị xóa!
                </div>
            </div>
            <div class="modal-footer">
                <div class="row w-100">
                    <input type="hidden" name="integration_id" id="integration_id">
                    <div class="d-flex justify-content-between align-items-center" style="gap: 0.5rem;">
                        <button type="button" class="btn w-100" data-bs-dismiss="modal">
                            Hủy
                        </button>
                        <button type="button" class="btn btn-primary w-100" onclick="removeDeviceVA()">
                            Xác nhận
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?= $this->endSection() ?>

    <?= $this->section('scripts') ?>

    <script>
        $(document).ready(function() {
            // check alert
            let alert = <?= isset($alert) ? json_encode($alert) : 'null'; ?>;

            if (alert !== null && alert.type === "success") {
                notyf.success({
                    message: alert.message
                });
            } else if (alert !== null && alert.type === "error") {
                notyf.error({
                    message: alert.message
                });
            }

        });

        let csrfToken = $('meta[name="csrf-token"]').attr('content');
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': csrfToken
            }
        });


        function updateCsrfToken(csrfToken) {
            $('meta[name="csrf-token"]').attr('content', csrfToken);
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': csrfToken
                }
            });
        }

        const deleteModalVA = new bootstrap.Modal(document.getElementById('deleteModalVA'), {
            keyboard: false
        });

        function openModalDeleteVA(integration_id) {
            $('#integration_id').val(integration_id);
            deleteModalVA.show();
        }

        function removeDeviceVA() {
            let integration_id = $('#integration_id').val();
            $.ajax({
                url: '<?= base_url('outputdevice/ajax_delete_integration') ?>' + '/' +  integration_id,
                type: 'GET',
                success: function(res) {
                    if (res.code == 200) {
                        window.location.reload();
                    }
                },
                error: function(err) {
                    let errorMsg = "Lỗi hệ thống, Hãy liên hệ kỹ thuật!";
                    if (err.status === 403) errorMsg = "Hãy nhấn F5 tải lại trang để thử lại!";
                    if (err.status === 404 || err.status === 422 || err.status === 423) {
                        errorMsg = err.responseJSON.message || "Lỗi dữ liệu!";
                    }
                    notyf.error({
                        message: errorMsg
                    });
                },
                complete: function(xhr) {
                    let newCsrfToken = xhr.getResponseHeader("X-CSRF-TOKEN");
                    if (newCsrfToken) updateCsrfToken(newCsrfToken);
                }
            });
        }

        $(".print-link").on("click", function() {
            let qrImageSrc = $(this).data("src");
            let brandName = $(this).data("brand-name");
            let isAbbank = brandName.toLowerCase().includes('abbank');
            let nameAccountNumber = $(this).data("name-account-number");
            let accountNumber = $(this).data("account-number");

            const printWindow = window.open('', '_blank');
            printWindow.document.write(`
                <html>
                    <head>
                        <title>In mã QR</title>
                        <style>
                            @page {
                                size: A4;
                                margin: 0;
                            }
                            body {
                                font-family: Arial, sans-serif;
                                margin: 0;
                                padding: 0;
                                width: 210mm;
                                height: 297mm;
                                display: flex;
                                flex-direction: column;
                                align-items: center;
                                position: relative;
                            }
                            .qr-info {
                                position: absolute;
                                bottom: ${isAbbank ? '15mm' : '26mm'};
                                left: 0;
                                width: 100%;
                                text-align: center;
                                font-size: 9pt;
                                z-index: 1000;
                            }
                            .qr-info p {
                                margin: 0mm 0;
                                padding: 0 3mm;
                            }
                            .qr-info p:first-child {
                                font-weight: bold;
                                text-transform: uppercase;
                                font-size: 11pt;
                                z-index: 1000;
                            }
                            .qr-container {
                                text-align: center;
                                width: 80mm;
                                height: 134mm;
                                position: absolute;
                                border: 1mm dashed #000;
                                left: 50%;
                                transform: translateX(-50%);
                                box-sizing: border-box;
                                margin-top: 10mm;
                            }
                            .qr-code {
                                width: 55mm;
                                height: 50mm;
                                position: absolute;
                                left: 50%;
                                bottom: ${isAbbank ? '25mm' : '36mm'};
                                transform: translateX(-50%);
                                object-fit: contain;
                                margin-bottom: -3mm;
                            }
                        </style>
                    </head>
                    <body>
                        <div class="qr-container">
                            <div class="qr-info">
                                ${nameAccountNumber ? `<p>${nameAccountNumber}</p>` : ''}
                                ${accountNumber ? `<p>Số tài khoản: ${accountNumber}</p>` : ''}
                            </div>
                            <img src="${qrImageSrc}" alt="QR Code" class="qr-code">
                        </div>
                    </body>
                </html>
            `);
            printWindow.document.close();
            printWindow.focus();
            setTimeout(() => {
                printWindow.print();
            }, 500);
        });
    </script>
    <?= $this->endSection('scripts') ?>