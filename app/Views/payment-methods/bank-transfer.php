<?= $this->extend('layouts/default') ?>

<?= $this->section('styles') ?>
<style>
/* Clean, modern styling for bank transfer page */
.bank-transfer-container {
    max-width: 1200px;
    margin: 0 auto;
}

.page-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    padding: 2rem;
    margin-bottom: 2rem;
    border: 1px solid #dee2e6;
}

.section-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    border: 1px solid #e9ecef;
    margin-bottom: 2rem;
    overflow: hidden;
}

.section-header {
    background: #f8f9fa;
    padding: 1.5rem 2rem;
    border-bottom: 1px solid #e9ecef;
    margin: 0;
}

.section-header h5 {
    margin: 0;
    color: #495057;
    font-weight: 600;
}

.section-header .badge {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
}

.account-card {
    padding: 1.5rem 2rem;
    border-bottom: 1px solid #f1f3f4;
    transition: all 0.2s ease;
}

.account-card:last-child {
    border-bottom: none;
}

.account-card:hover {
    background: #f8f9fa;
}

.account-card.default-account {
    background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%);
    border-left: 4px solid #28a745;
}

.bank-logo {
    width: 56px;
    height: 56px;
    border-radius: 8px;
    object-fit: contain;
    border: 2px solid #e9ecef;
    background: white;
}

.bank-logo-placeholder {
    width: 56px;
    height: 56px;
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.account-info h6 {
    font-size: 1.1rem;
    font-weight: 600;
    color: #212529;
    margin-bottom: 0.5rem;
}

.account-details {
    color: #6c757d;
    font-size: 0.95rem;
    margin-bottom: 0.5rem;
}

.account-status {
    font-size: 0.85rem;
    color: #6c757d;
}

.status-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-weight: 500;
    margin-left: 0.5rem;
}

.action-buttons {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.action-buttons .btn {
    font-size: 0.875rem;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    font-weight: 500;
}

.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: #6c757d;
}

.empty-state-icon {
    font-size: 4rem;
    color: #adb5bd;
    margin-bottom: 1.5rem;
}

.primary-cta {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border: none;
    padding: 0.75rem 2rem;
    font-weight: 600;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,123,255,0.3);
    transition: all 0.2s ease;
}

.primary-cta:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,123,255,0.4);
}

/* Mobile optimizations */
@media (max-width: 768px) {
    .page-header {
        padding: 1.5rem;
        text-align: center;
    }

    .section-header {
        padding: 1rem 1.5rem;
    }

    .account-card {
        padding: 1rem 1.5rem;
    }

    .bank-logo, .bank-logo-placeholder {
        width: 48px;
        height: 48px;
    }

    .action-buttons {
        justify-content: stretch;
    }

    .action-buttons .btn {
        flex: 1;
        min-width: 0;
    }

    .account-info h6 {
        font-size: 1rem;
    }
}

@media (max-width: 576px) {
    .page-header {
        padding: 1rem;
    }

    .page-header h1 {
        font-size: 1.5rem;
    }

    .empty-state {
        padding: 2rem 1rem;
    }

    .empty-state-icon {
        font-size: 3rem;
    }
}
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<div class="bank-transfer-container">
    <!-- Page Header -->
    <div class="page-header">
        <nav aria-label="breadcrumb" class="mb-3">
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item"><a href="<?= base_url('paymentmethods') ?>">Phương thức thanh toán</a></li>
                <li class="breadcrumb-item active" aria-current="page">Chuyển khoản ngân hàng</li>
            </ol>
        </nav>

        <div class="d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center gap-3">
            <div>
                <h1 class="h2 mb-2">Chuyển khoản ngân hàng</h1>
                <p class="text-muted mb-0">Quản lý tài khoản ngân hàng để nhận thanh toán từ khách hàng</p>
            </div>
            <a href="<?= base_url('bankaccount/connect') ?>" class="btn btn-primary primary-cta">
                <i class="bi bi-plus-circle me-2"></i>Kết nối tài khoản mới
            </a>
        </div>
    </div>

    <?php if (! $payment_method || ! $payment_method->active): ?>
        <!-- Payment Method Inactive Warning -->
        <div class="section-card">
            <div class="empty-state">
                <div class="empty-state-icon text-warning">
                    <i class="bi bi-exclamation-triangle"></i>
                </div>
                <h4 class="mb-3">Phương thức thanh toán chưa được kích hoạt</h4>
                <p class="mb-4">
                    Vui lòng kích hoạt phương thức thanh toán chuyển khoản ngân hàng từ trang chính để sử dụng tính năng này.
                </p>
                <a href="<?= base_url('paymentmethods') ?>" class="btn btn-warning">
                    <i class="bi bi-arrow-left me-2"></i>Quay lại trang chính
                </a>
            </div>
        </div>
    <?php elseif (empty($bankProfiles)): ?>
        <!-- Empty State -->
        <div class="section-card">
            <div class="empty-state">
                <div class="empty-state-icon">
                    <i class="bi bi-bank"></i>
                </div>
                <h4 class="mb-3">Chưa có tài khoản ngân hàng nào được kết nối</h4>
                <p class="mb-4">
                    Để nhận thanh toán qua chuyển khoản ngân hàng, bạn cần kết nối ít nhất một tài khoản ngân hàng.
                    Tài khoản được đặt làm mặc định sẽ hiển thị trên cổng thanh toán SePay.
                </p>
                <a href="<?= base_url('bankaccount/connect') ?>" class="btn btn-primary primary-cta">
                    <i class="bi bi-plus-circle me-2"></i>Kết nối tài khoản đầu tiên
                </a>
            </div>
        </div>
    <?php else: ?>
        <!-- Connected Accounts Section -->
        <div class="section-card">
            <div class="section-header">
                <h5>Tài khoản đã kết nối</h5>
                <span class="badge bg-primary"><?= count($bankProfiles) ?> tài khoản</span>
            </div>

            <?php foreach ($bankProfiles as $profile): ?>
                <div class="account-card <?= $profile->default ? 'default-account' : '' ?>">
                    <div class="d-flex flex-column flex-lg-row align-items-start gap-3">
                        <!-- Bank Logo and Info -->
                        <div class="d-flex align-items-center flex-grow-1">
                            <div class="me-3">
                                <?php if ($profile->type === 'NAPAS_VIETQR'): ?>
                                    <img src="<?= base_url('assets/images/banklogo/napas247.png') ?>" alt="NAPAS VietQR" class="bank-logo">
                                <?php elseif (!empty($profile->logo_path)): ?>
                                    <img src="<?= base_url('assets/images/banklogo/' . $profile->logo_path) ?>" alt="<?= esc($profile->brand_name) ?>" class="bank-logo">
                                <?php elseif (!empty($profile->icon_path)): ?>
                                    <img src="<?= base_url('assets/images/banklogo/' . $profile->icon_path) ?>" alt="<?= esc($profile->brand_name) ?>" class="bank-logo">
                                <?php else: ?>
                                    <div class="bank-logo-placeholder">
                                        <i class="bi bi-bank"></i>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <div class="account-info flex-grow-1">
                                <div class="d-flex align-items-center flex-wrap gap-2 mb-1">
                                    <h6 class="mb-0"><?= esc($profile->brand_name) ?></h6>
                                    <?php if ($profile->default): ?>
                                        <span class="status-badge bg-success text-white">
                                            <i class="bi bi-star-fill me-1"></i>Mặc định
                                        </span>
                                    <?php endif; ?>
                                </div>
                                <div class="account-details mb-2">
                                    <strong><?= esc($profile->account_number) ?></strong>
                                    <?php if (!empty($profile->account_holder_name)): ?>
                                        - <?= esc($profile->account_holder_name) ?>
                                    <?php endif; ?>
                                </div>
                                <div class="account-status">
                                    <?php if ($profile->default): ?>
                                        Tài khoản này sẽ hiển thị trên cổng thanh toán SePay
                                    <?php else: ?>
                                        Nhấn "Đặt làm mặc định" để hiển thị trên cổng thanh toán
                                    <?php endif; ?>
                                </div>

                                <!-- VA Configuration Status -->
                                <?php if ($profile->type === 'BANK_ACCOUNT'): ?>
                                    <?php if ($profile->requires_va): ?>
                                        <?php if ($profile->branch_profile && $profile->branch_profile->bank_sub_account_id): ?>
                                            <div class="mt-2">
                                                <span class="status-badge bg-success-subtle text-success">
                                                    <i class="bi bi-check-circle me-1"></i>VA đã được cấu hình
                                                </span>
                                            </div>
                                        <?php else: ?>
                                            <div class="mt-2">
                                                <span class="status-badge bg-warning-subtle text-warning">
                                                    <i class="bi bi-exclamation-triangle me-1"></i>Cần cấu hình VA
                                                </span>
                                            </div>
                                        <?php endif; ?>
                                    <?php else: ?>
                                        <?php if ($profile->branch_profile && $profile->branch_profile->bank_sub_account_id): ?>
                                            <div class="mt-2">
                                                <span class="status-badge bg-info-subtle text-info">
                                                    <i class="bi bi-info-circle me-1"></i>VA tùy chọn đã được cấu hình
                                                </span>
                                            </div>
                                        <?php endif; ?>
                                    <?php endif; ?>
                                <?php elseif ($profile->type === 'NAPAS_VIETQR'): ?>
                                    <?php if ($profile->branch_profile): ?>
                                        <div class="mt-2">
                                            <span class="status-badge bg-success-subtle text-success">
                                                <i class="bi bi-check-circle me-1"></i>Branch đã được cấu hình
                                            </span>
                                        </div>
                                    <?php else: ?>
                                        <div class="mt-2">
                                            <span class="status-badge bg-warning-subtle text-warning">
                                                <i class="bi bi-exclamation-triangle me-1"></i>Cần cấu hình branch
                                            </span>
                                        </div>
                                    <?php endif; ?>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="action-buttons">
                            <!-- Configure VA/Branch Button -->
                            <?php if ($profile->type === 'BANK_ACCOUNT'): ?>
                                <?php if ($profile->requires_va || (!empty($profile->available_vas))): ?>
                                    <button type="button" class="btn btn-outline-info configure-va-btn"
                                            data-profile-id="<?= $profile->id ?>"
                                            data-profile-type="BANK_ACCOUNT"
                                            data-requires-va="<?= $profile->requires_va ? 'true' : 'false' ?>"
                                            data-bs-toggle="tooltip" data-bs-placement="top"
                                            title="<?= $profile->requires_va ? 'Cấu hình VA (bắt buộc)' : 'Cấu hình VA (tùy chọn)' ?>">
                                        <i class="bi bi-gear me-1"></i>Cấu hình VA
                                    </button>
                                <?php endif; ?>
                            <?php elseif ($profile->type === 'NAPAS_VIETQR'): ?>
                                <button type="button" class="btn btn-outline-info configure-branch-btn"
                                        data-profile-id="<?= $profile->id ?>"
                                        data-profile-type="NAPAS_VIETQR"
                                        data-bs-toggle="tooltip" data-bs-placement="top"
                                        title="Cấu hình branch">
                                    <i class="bi bi-gear me-1"></i>Cấu hình Branch
                                </button>
                            <?php endif; ?>

                            <?php if (! $profile->default): ?>
                                <button type="button" class="btn btn-outline-primary set-default-btn" data-profile-id="<?= $profile->id ?>">
                                    <i class="bi bi-star me-1"></i>Đặt làm mặc định
                                </button>
                                <button type="button" class="btn btn-outline-danger remove-profile-btn" data-profile-id="<?= $profile->id ?>" data-bs-toggle="tooltip" data-bs-placement="top" title="Xóa tài khoản thụ hưởng">
                                    <i class="bi bi-trash"></i>
                                </button>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    <?php endif; ?>

    <?php
    $connectedAccountIds = array_column($bankProfiles, 'bank_account_id');
    $availableAccounts = array_filter($available_bank_accounts, function ($account) use ($connectedAccountIds) {
        return ! in_array($account->id, $connectedAccountIds);
    });
    ?>

        <!-- Available Accounts Section -->
        <?php if (! empty($availableAccounts)): ?>
            <div class="section-card">
                <div class="section-header">
                    <h5>Tài khoản có thể thêm</h5>
                    <span class="badge bg-secondary"><?= count($availableAccounts) ?> tài khoản</span>
                </div>

                <?php foreach ($availableAccounts as $account): ?>
                    <div class="account-card">
                        <div class="d-flex flex-column flex-lg-row align-items-start gap-3">
                            <!-- Bank Logo and Info -->
                            <div class="d-flex align-items-center flex-grow-1">
                                <div class="me-3">
                                    <?php if (!empty($account->logo_path)): ?>
                                        <img src="<?= base_url('assets/images/banklogo/' . $account->logo_path) ?>" alt="<?= esc($account->brand_name) ?>" class="bank-logo">
                                    <?php elseif (! empty($account->icon_path)): ?>
                                        <img src="<?= base_url('assets/images/banklogo/' . $account->icon_path) ?>" alt="<?= esc($account->brand_name) ?>" class="bank-logo">
                                    <?php else: ?>
                                        <div class="bank-logo-placeholder">
                                            <i class="bi bi-bank"></i>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <div class="account-info flex-grow-1">
                                    <h6 class="mb-1"><?= esc($account->brand_name) ?></h6>
                                    <div class="account-details mb-2">
                                        <strong><?= esc($account->account_number) ?></strong>
                                        - <?= esc($account->account_holder_name) ?>
                                    </div>
                                    <div class="account-status">
                                        Sẵn sàng để thêm vào profile thanh toán
                                    </div>
                                </div>
                            </div>

                            <!-- Action Button -->
                            <div class="action-buttons">
                                <button type="button" class="btn btn-success add-profile-btn" data-account-id="<?= $account->id ?>">
                                    <i class="bi bi-plus-circle me-1"></i>Thêm vào profile
                                </button>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</div>

<!-- VA Configuration Modal -->
<div class="modal fade" id="configureVaModal" tabindex="-1" aria-labelledby="configureVaModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="configureVaModalLabel">Cấu hình Virtual Account (VA)</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="configureVaForm">
                <div class="modal-body">
                    <input type="hidden" id="va_profile_id" name="profile_id">
                    <input type="hidden" id="va_profile_type" name="profile_type" value="BANK_ACCOUNT">

                    <div class="alert alert-info" id="va_info_alert">
                        <i class="bi bi-info-circle me-2"></i>
                        <span id="va_info_text"></span>
                    </div>

                    <div class="mb-3">
                        <label for="bank_sub_account_id" class="form-label">
                            Chọn Virtual Account (VA) <span class="text-danger" id="va_required_indicator">*</span>
                        </label>
                        <select class="form-select" id="bank_sub_account_id" name="bank_sub_account_id">
                            <option value="">-- Chọn VA --</option>
                        </select>
                        <div class="form-text">
                            Virtual Account được sử dụng để nhận thanh toán cho profile này.
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                    <button type="submit" class="btn btn-primary">Lưu cấu hình</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Branch Configuration Modal -->
<div class="modal fade" id="configureBranchModal" tabindex="-1" aria-labelledby="configureBranchModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="configureBranchModalLabel">Cấu hình Branch cho NAPAS VietQR</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="configureBranchForm">
                <div class="modal-body">
                    <input type="hidden" id="branch_profile_id" name="profile_id">
                    <input type="hidden" id="branch_profile_type" name="profile_type" value="NAPAS_VIETQR">

                    <div class="alert alert-info">
                        <i class="bi bi-info-circle me-2"></i>
                        Branch sẽ được tự động chọn dựa trên cấu hình của công ty.
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Xác nhận cấu hình</label>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="confirm_branch_config" required>
                            <label class="form-check-label" for="confirm_branch_config">
                                Tôi xác nhận muốn cấu hình branch cho profile NAPAS VietQR này
                            </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                    <button type="submit" class="btn btn-primary">Lưu cấu hình</button>
                </div>
            </form>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    $(document).ready(function() {
        $('.set-default-btn').on('click', function() {
            const profileId = $(this).data('profile-id');
            const $button = $(this);

            $.ajax({
                url: '<?= base_url('paymentmethods/setDefaultBankProfile') ?>',
                method: 'POST',
                data: {
                    '<?= csrf_token() ?>': '<?= csrf_hash() ?>',
                    profile_id: profileId
                },
                beforeSend: function() {
                    $button.prop('disabled', true).find('i').replaceWith('<span class="spinner-border spinner-border-sm me-1 align-middle" role="status" aria-hidden="true"></span>');
                },
                success: function(response) {
                    if (response.status) {
                        location.reload()
                    } else {
                        notyf.error(response.message);
                    }
                },
                error: function() {
                    notyf.error('Có lỗi xảy ra, vui lòng thử lại');
                },
                complete: function() {
                    $button.prop('disabled', false).find('.spinner-border').replaceWith('<i class="bi bi-star me-1"></i>');
                }
            });
        });

        $('.add-profile-btn').on('click', function() {
            const accountId = $(this).data('account-id');
            const $button = $(this);

            $.ajax({
                url: '<?= base_url('paymentmethods/addBankProfile') ?>',
                method: 'POST',
                data: {
                    '<?= csrf_token() ?>': '<?= csrf_hash() ?>',
                    bank_account_id: accountId,
                    is_default: <?= empty($bankProfiles) ? '1' : '0' ?>
                },
                beforeSend: function() {
                    $button.prop('disabled', true).find('i').replaceWith('<span class="spinner-border spinner-border-sm me-1 align-middle" role="status" aria-hidden="true"></span>');
                },
                success: function(response) {
                    if (response.status) {
                        location.reload();
                    } else {
                        notyf.error(response.message);
                    }
                },
                error: function() {
                    notyf.error('Có lỗi xảy ra, vui lòng thử lại');
                },
                complete: function() {
                    $button.prop('disabled', false).find('.spinner-border').replaceWith('<i class="bi bi-plus-circle me-1"></i>');
                }
            });
        });

        $('.remove-profile-btn').on('click', function() {
            const profileId = $(this).data('profile-id');
            const $button = $(this);

            if (!confirm('Bạn có chắc chắn muốn xóa tài khoản thụ hưởng này không?')) {
                return;
            }

            $.ajax({
                url: '<?= base_url('paymentmethods/removeBankProfile') ?>',
                method: 'POST',
                data: {
                    '<?= csrf_token() ?>': '<?= csrf_hash() ?>',
                    profile_id: profileId
                },
                beforeSend: function() {
                    $button.prop('disabled', true).find('i').replaceWith('<span class="spinner-border spinner-border-sm align-middle" role="status" aria-hidden="true"></span>');
                },
                success: function(response) {
                    if (response.status) {
                        location.reload();
                    } else {
                        notyf.error(response.message);
                    }
                },
                error: function() {
                    notyf.error('Có lỗi xảy ra, vui lòng thử lại');
                },
                complete: function() {
                    $button.prop('disabled', false).find('.spinner-border').replaceWith('<i class="fas fa-link-slash"></i>');
                }
            });
        });

        // Configure VA Modal
        $('.configure-va-btn').on('click', function() {
            const profileId = $(this).data('profile-id');
            const profileType = $(this).data('profile-type');
            const requiresVa = $(this).data('requires-va') === 'true';

            $('#va_profile_id').val(profileId);
            $('#va_profile_type').val(profileType);

            // Update modal content based on requirements
            if (requiresVa) {
                $('#va_info_text').text('Ngân hàng này yêu cầu bắt buộc phải cấu hình VA để có thể nhận thanh toán.');
                $('#va_info_alert').removeClass('alert-info').addClass('alert-warning');
                $('#va_required_indicator').show();
                $('#bank_sub_account_id').attr('required', true);
            } else {
                $('#va_info_text').text('Cấu hình VA là tùy chọn cho ngân hàng này. Bạn có thể chọn VA để tối ưu hóa việc nhận thanh toán.');
                $('#va_info_alert').removeClass('alert-warning').addClass('alert-info');
                $('#va_required_indicator').hide();
                $('#bank_sub_account_id').attr('required', false);
            }

            // Load available VAs for this profile
            loadAvailableVAs(profileId);

            $('#configureVaModal').modal('show');
        });

        // Configure Branch Modal
        $('.configure-branch-btn').on('click', function() {
            const profileId = $(this).data('profile-id');
            const profileType = $(this).data('profile-type');

            $('#branch_profile_id').val(profileId);
            $('#branch_profile_type').val(profileType);

            $('#configureBranchModal').modal('show');
        });

        // Load available VAs
        function loadAvailableVAs(profileId) {
            // Find the profile data from the page
            const profileData = <?= json_encode($bankProfiles) ?>;
            const profile = profileData.find(p => p.id == profileId);

            const $select = $('#bank_sub_account_id');
            $select.empty().append('<option value="">-- Chọn VA --</option>');

            if (profile && profile.available_vas) {
                profile.available_vas.forEach(va => {
                    const label = va.label ? ` - ${va.label}` : '';
                    const holderName = va.sub_holder_name ? ` (${va.sub_holder_name})` : '';
                    $select.append(`<option value="${va.id}">${va.sub_account}${label}${holderName}</option>`);
                });
            }

            // Set current selection if exists
            if (profile && profile.branch_profile && profile.branch_profile.bank_sub_account_id) {
                $select.val(profile.branch_profile.bank_sub_account_id);
            }
        }

        // Handle VA configuration form submission
        $('#configureVaForm').on('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            formData.append('<?= csrf_token() ?>', '<?= csrf_hash() ?>');

            $.ajax({
                url: '<?= base_url('paymentmethods/configureBranchProfile') ?>',
                method: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                beforeSend: function() {
                    $('#configureVaForm button[type="submit"]').prop('disabled', true).text('Đang lưu...');
                },
                success: function(response) {
                    if (response.status) {
                        notyf.success(response.message);
                        $('#configureVaModal').modal('hide');
                        setTimeout(() => location.reload(), 1500);
                    } else {
                        notyf.error(response.message || 'Có lỗi xảy ra');
                    }
                },
                error: function() {
                    notyf.error('Có lỗi xảy ra, vui lòng thử lại');
                },
                complete: function() {
                    $('#configureVaForm button[type="submit"]').prop('disabled', false).text('Lưu cấu hình');
                }
            });
        });

        // Handle Branch configuration form submission
        $('#configureBranchForm').on('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            formData.append('<?= csrf_token() ?>', '<?= csrf_hash() ?>');

            $.ajax({
                url: '<?= base_url('paymentmethods/configureBranchProfile') ?>',
                method: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                beforeSend: function() {
                    $('#configureBranchForm button[type="submit"]').prop('disabled', true).text('Đang lưu...');
                },
                success: function(response) {
                    if (response.status) {
                        notyf.success(response.message);
                        $('#configureBranchModal').modal('hide');
                        setTimeout(() => location.reload(), 1500);
                    } else {
                        notyf.error(response.message || 'Có lỗi xảy ra');
                    }
                },
                error: function() {
                    notyf.error('Có lỗi xảy ra, vui lòng thử lại');
                },
                complete: function() {
                    $('#configureBranchForm button[type="submit"]').prop('disabled', false).text('Lưu cấu hình');
                }
            });
        });

        // Initialize tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    });
</script>
<?= $this->endSection() ?>