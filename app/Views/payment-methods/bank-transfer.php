<?= $this->extend('layouts/default') ?>

<?= $this->section('content') ?>
<style>
    @media (max-width: 575.98px) {
        .list-group-item .d-flex.gap-2 {
            flex-direction: column;
            gap: 0.5rem !important;
        }

        .list-group-item .btn {
            width: 100% !important;
        }

        .list-group-item .d-flex.justify-content-end {
            justify-content: stretch !important;
        }
    }
</style>

<div class="container my-3">
    <div class="d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center mb-4 gap-3">
        <div class="flex-grow-1">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb mb-2">
                    <li class="breadcrumb-item"><a href="<?= base_url('paymentmethods') ?>">Phương thức thanh toán</a></li>
                    <li class="breadcrumb-item active" aria-current="page"><PERSON>yể<PERSON> khoản ngân hàng</li>
                </ol>
            </nav>
            <h1 class="h3 text-gray-800 mb-1">Chuyể<PERSON> khoản ngân hàng</h1>
            <p class="text-muted mb-0">Quản lý tài khoản ngân hàng để nhận thanh toán</p>
        </div>
        <div class="flex-shrink-0 w-md-auto">
            <a href="<?= base_url('bankaccount/connect') ?>" class="btn btn-primary w-100 w-md-auto">
                <i class="bi bi-plus-circle me-2"></i><span class="d-none d-sm-inline">Kết nối tài khoản ngân hàng</span><span class="d-sm-none">Kết nối ngân hàng</span>
            </a>
        </div>
    </div>

    <?php if (empty($bankProfiles)): ?>
        <div class="row">
            <div class="col-12">
                <div class="card border-left-info shadow h-100 mb-4">
                    <div class="card-body text-center py-5">
                        <div class="mb-4">
                            <i class="bi bi-bank text-info" style="font-size: 4rem;"></i>
                        </div>
                        <h4 class="text-gray-800 mb-3">Chưa có tài khoản ngân hàng nào được kết nối</h4>
                        <p class="text-muted mb-4">
                            Để nhận thanh toán qua chuyển khoản ngân hàng, bạn cần kết nối ít nhất một tài khoản ngân hàng.
                            Tài khoản được đặt làm mặc định sẽ hiển thị trên cổng thanh toán SePay.
                        </p>
                        <a href="<?= base_url('bankaccount/connect') ?>" class="btn btn-primary btn-lg">
                            <i class="bi bi-plus-circle me-2"></i>Kết nối tài khoản ngân hàng đầu tiên
                        </a>
                    </div>
                </div>
            </div>
        </div>
    <?php else: ?>
        <div class="mb-4">
            <div class="list-group list-group-flush">
                <?php foreach ($bankProfiles as $profile): ?>
                    <div class="list-group-item p-3">
                        <div class="d-flex flex-column flex-sm-row align-items-start align-items-sm-center gap-3">
                            <div class="d-flex align-items-center flex-grow-1 w-100 w-sm-auto">
                                <div class="flex-shrink-0 me-3">
                                    <?php if ($profile->type === 'NAPAS_VIETQR'): ?>
                                        <img src="<?= base_url('assets/images/banklogo/napas247.png') ?>" alt="NAPAS VietQR" class="rounded" style="width: 48px; height: 48px; object-fit: contain;">
                                    <?php elseif (!empty($profile->logo_path)): ?>
                                        <img src="<?= base_url('assets/images/banklogo/' . $profile->logo_path) ?>" alt="<?= esc($profile->brand_name) ?>" class="rounded" style="width: 48px; height: 48px; object-fit: contain;">
                                    <?php elseif (!empty($profile->icon_path)): ?>
                                        <img src="<?= base_url('assets/images/banklogo/' . $profile->icon_path) ?>" alt="<?= esc($profile->brand_name) ?>" class="rounded" style="width: 48px; height: 48px; object-fit: contain;">
                                    <?php else: ?>
                                        <div class="bg-primary bg-opacity-10 rounded d-flex align-items-center justify-content-center" style="width: 48px; height: 48px;">
                                            <i class="bi bi-bank text-primary fs-4"></i>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <div class="flex-grow-1 min-w-0">
                                    <div class="d-flex flex-column flex-sm-row align-items-start align-items-sm-center mb-1 gap-2">
                                        <h6 class="mb-0 text-truncate"><?= esc($profile->brand_name) ?></h6>
                                        <?php if ($profile->default): ?>
                                            <span class="badge bg-success">Mặc định</span>
                                        <?php endif; ?>
                                    </div>
                                    <p class="text-muted mb-1 text-break">
                                        <strong><?= esc($profile->account_number) ?></strong> - <?= esc($profile->account_holder_name) ?>
                                    </p>
                                    <small class="text-muted d-block">
                                        <?php if ($profile->default): ?>
                                            <span class="d-none d-sm-inline">Tài khoản này sẽ hiển thị trên cổng thanh toán SePay</span>
                                            <span class="d-sm-none">Hiển thị trên cổng thanh toán</span>
                                        <?php else: ?>
                                            <span class="d-none d-sm-inline">Nhấn "Đặt làm mặc định" để hiển thị trên cổng thanh toán</span>
                                            <span class="d-sm-none">Chưa được đặt làm mặc định</span>
                                        <?php endif; ?>
                                    </small>
                                </div>
                            </div>
                            <div class="d-flex gap-2 justify-content-end w-100">
                                <?php if (! $profile->default): ?>
                                    <button type="button" class="btn btn-outline-primary btn-sm set-default-btn" data-profile-id="<?= $profile->id ?>">
                                        <i class="bi bi-star me-1"></i><span class="d-none d-md-inline">Đặt làm mặc định</span><span class="d-md-none">Mặc định</span>
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary btn-sm remove-profile-btn" data-profile-id="<?= $profile->id ?>" data-bs-toggle="tooltip" data-bs-placement="top" title="Xóa tài khoản thụ hưởng">
                                        <i class="fas fa-link-slash"></i>
                                    </button>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    <?php endif; ?>

    <?php
    $connectedAccountIds = array_column($bankProfiles, 'bank_account_id');
    $availableAccounts = array_filter($available_bank_accounts, function ($account) use ($connectedAccountIds) {
        return ! in_array($account->id, $connectedAccountIds);
    });
    ?>

    <?php if (! empty($availableAccounts)): ?>
        <div>
            <h4 class="mb-3">Tài khoản ngân hàng có thể thêm</h4>
            <div class="list-group list-group-flush">
                <?php foreach ($availableAccounts as $account): ?>
                    <div class="list-group-item p-3">
                        <div class="d-flex flex-column flex-sm-row align-items-start align-items-sm-center gap-3">
                            <div class="d-flex align-items-center flex-grow-1 w-100 w-sm-auto">
                                <div class="flex-shrink-0 me-3">
                                    <?php if (!empty($account->logo_path)): ?>
                                        <img src="<?= base_url('assets/images/banklogo/' . $account->logo_path) ?>" alt="<?= esc($account->brand_name) ?>" class="rounded" style="width: 48px; height: 48px; object-fit: contain;">
                                    <?php elseif (! empty($account->icon_path)): ?>
                                        <img src="<?= base_url('assets/images/banklogo/' . $account->icon_path) ?>" alt="<?= esc($account->brand_name) ?>" class="rounded" style="width: 48px; height: 48px; object-fit: contain;">
                                    <?php else: ?>
                                        <div class="bg-secondary bg-opacity-10 rounded d-flex align-items-center justify-content-center" style="width: 48px; height: 48px;">
                                            <i class="bi bi-bank text-secondary fs-4"></i>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <div class="flex-grow-1 min-w-0">
                                    <h6 class="mb-1 text-truncate"><?= esc($account->brand_name) ?></h6>
                                    <p class="text-muted mb-0 text-break">
                                        <strong><?= esc($account->account_number) ?></strong> - <?= esc($account->account_holder_name) ?>
                                    </p>
                                </div>
                            </div>
                            <div class="d-flex justify-content-end w-100">
                                <button type="button" class="btn btn-outline-success btn-sm add-profile-btn" data-account-id="<?= $account->id ?>">
                                    <i class="bi bi-plus-circle me-1"></i><span class="d-none d-sm-inline">Thêm vào tài khoản thụ hưởng</span><span class="d-sm-none">Thêm</span>
                                </button>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    <?php endif; ?>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    $(document).ready(function() {
        $('.set-default-btn').on('click', function() {
            const profileId = $(this).data('profile-id');
            const $button = $(this);

            $.ajax({
                url: '<?= base_url('paymentmethods/setDefaultBankProfile') ?>',
                method: 'POST',
                data: {
                    '<?= csrf_token() ?>': '<?= csrf_hash() ?>',
                    profile_id: profileId
                },
                beforeSend: function() {
                    $button.prop('disabled', true).find('i').replaceWith('<span class="spinner-border spinner-border-sm me-1 align-middle" role="status" aria-hidden="true"></span>');
                },
                success: function(response) {
                    if (response.status) {
                        location.reload()
                    } else {
                        notyf.error(response.message);
                    }
                },
                error: function() {
                    notyf.error('Có lỗi xảy ra, vui lòng thử lại');
                },
                complete: function() {
                    $button.prop('disabled', false).find('.spinner-border').replaceWith('<i class="bi bi-star me-1"></i>');
                }
            });
        });

        $('.add-profile-btn').on('click', function() {
            const accountId = $(this).data('account-id');
            const $button = $(this);

            $.ajax({
                url: '<?= base_url('paymentmethods/addBankProfile') ?>',
                method: 'POST',
                data: {
                    '<?= csrf_token() ?>': '<?= csrf_hash() ?>',
                    bank_account_id: accountId,
                    is_default: <?= empty($bankProfiles) ? '1' : '0' ?>
                },
                beforeSend: function() {
                    $button.prop('disabled', true).find('i').replaceWith('<span class="spinner-border spinner-border-sm me-1 align-middle" role="status" aria-hidden="true"></span>');
                },
                success: function(response) {
                    if (response.status) {
                        location.reload();
                    } else {
                        notyf.error(response.message);
                    }
                },
                error: function() {
                    notyf.error('Có lỗi xảy ra, vui lòng thử lại');
                },
                complete: function() {
                    $button.prop('disabled', false).find('.spinner-border').replaceWith('<i class="bi bi-plus-circle me-1"></i>');
                }
            });
        });

        $('.remove-profile-btn').on('click', function() {
            const profileId = $(this).data('profile-id');
            const $button = $(this);

            if (!confirm('Bạn có chắc chắn muốn xóa tài khoản thụ hưởng này không?')) {
                return;
            }

            $.ajax({
                url: '<?= base_url('paymentmethods/removeBankProfile') ?>',
                method: 'POST',
                data: {
                    '<?= csrf_token() ?>': '<?= csrf_hash() ?>',
                    profile_id: profileId
                },
                beforeSend: function() {
                    $button.prop('disabled', true).find('i').replaceWith('<span class="spinner-border spinner-border-sm align-middle" role="status" aria-hidden="true"></span>');
                },
                success: function(response) {
                    if (response.status) {
                        location.reload();
                    } else {
                        notyf.error(response.message);
                    }
                },
                error: function() {
                    notyf.error('Có lỗi xảy ra, vui lòng thử lại');
                },
                complete: function() {
                    $button.prop('disabled', false).find('.spinner-border').replaceWith('<i class="fas fa-link-slash"></i>');
                }
            });
        });
    });
</script>
<?= $this->endSection() ?>