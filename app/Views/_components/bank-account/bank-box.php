<?php 

if (!is_bank_box_support()) return; 

$defaultVAV = $defaultVAV ?? '';

?>

<?php if (isset($style) && $style): ?>
    <style>
        #bank-box .tab button {
            color: #000;
            border: none;
            background: transparent;
            padding: 0.25rem 1rem;
            border-radius: 0.3rem;
        }

        #bank-box .tab button:hover {
            border: none !important;
        }

        #bank-box .tab button.active {
            background-color: #fff;
            --tw-shadow: 0 1px 3px 0 rgba(0, 0, 0, .1), 0 1px 2px -1px rgba(0, 0, 0, .1);
            --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
            box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
        }

        #bank-box .nav-tabs .nav-link {
            color: #000;
            border: none;
            background: transparent;
            border-radius: 0.3rem;
            padding: 0.25rem 1rem;
            color: var(--text-white);
        }

        #bank-box .nav-tabs .nav-link.active {
            background-color: var(--bs-white);
            --tw-shadow: 0 1px 3px 0 rgba(0, 0, 0, .1), 0 1px 2px -1px rgba(0, 0, 0, .1);
            --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
            box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
            color: var(--bs-dark);
        }
        
        #bank-box .form-check:has(input[name="create_va_option"]:checked) {
            --bs-border-opacity: 1;
            border-color: rgba(var(--bs-primary-rgb), var(--bs-border-opacity)) !important;
        }
    </style>
    <link rel="stylesheet" href="<?= base_url('assets/tom-select/tom-select.bootstrap5.css') ?>">
    <?php unset($style);
    return; ?>
<?php endif; ?>

<?php if (isset($script) && $script): ?>
<script src="<?= base_url('assets/js/jquery.validate.min.js') ?>"></script>
<script src="<?= base_url('assets/js/jquery.validate.additional-methods.min.js') ?>"></script>
<script src="<?= base_url('assets/tom-select/tom-select.complete.min.js') ?>"></script>

<script>
    function bankBox_handleBeforeUnload(event) {
        event.preventDefault();
        event.returnValue = true;
    }

    const bankBoxDom = document.getElementById('bank-box');

    const bankBoxModal = new bootstrap.Modal(bankBoxDom, {
        keyboard: false,
        backdrop: 'static'
    });

    const sendRequestEnterpriseBankBoxModal = new bootstrap.Modal(document.getElementById('send-request-enteprise-bank-box'), {
        keyboard: false,
        backdrop: 'static'
    });

    const agreementBankBoxModal = new bootstrap.Modal(document.getElementById('agreement-bank-box-modal'), {
        keyboard: false,
        backdrop: 'static'
    });

    const agreementBankBoxModalDom = $('#agreement-bank-box-modal');

    const BANK_BOX = {
        schema: null,
        context: null,
        bankAccountId: null,
        currentStep: 'choose-bank',
        requestConnectFormValidator: null,
        confirmConnectFormValidator: null,
        requestCreateVaFormValidator: null,
        confirmCreateVaFormValidator: null,
        webview: null,
        webviewInterval: null,
        confirmConnectInterval: null,
        defaultVAV: '<?= $defaultVAV ?>',
        doneCallbackInterval: null,
        doneCallbackSuccess: false,
        doneCallback: (id, context) => {}
    }

    bankBoxDom.addEventListener('hidden.bs.modal', event => {
        window.removeEventListener('beforeunload', bankBox_handleBeforeUnload);
        $(bankBoxDom).find('.btn-close').show();
        bankBox_renderChooseBank();
    })

    document.getElementById('agreement-bank-box-modal').addEventListener('hidden.bs.modal', event => {
        $('#bank-box [id="bank-account-connect-agreement"]').trigger('click');
    })

    function bankBox_renderChooseBank() {
        bankBox_setCurrentStep('choose-bank');
        $('#bank-box .step').addClass('d-none');
        $('#bank-box .step-choose-bank').removeClass('d-none');
    }

    function bankBox_loading(state = true) {
        if (state) {
            $('#bank-box .loading-screen').addClass('d-flex').removeClass('d-none');
        } else {
            $('#bank-box .loading-screen').removeClass('d-flex').addClass('d-none');
        }
    }

    function bankBox_handleChooseBank(event, brandName, bankAccountType) {
        if (brandName === 'ABBANK') {
            bankBoxModal.show();
        }

        bankBox_authorizeConnect(brandName, bankAccountType);
    }

    async function bankBox_authorizeConnect(brandName, bankAccountType, confirm = false) {
        bankBox_loading()
        
        return await $.ajax({
            url: `<?= base_url('bankBox/ajax_authorize_bank_account_connect') ?>/${brandName}`,
            method: 'POST',
            data: {
                type: bankAccountType,
                '<?= esc(csrf_token()) ?>': '<?= esc(csrf_hash()) ?>',
                output_device_id: typeof OUTPUT_DEVICE_ID !== 'undefined' ? OUTPUT_DEVICE_ID : null
            },
            success: (res) => {
                if (!res?.schema && res.status) {
                    bankBox_loading(false);                        

                    notyf.error({
                        message: 'Đã có lỗi xảy ra, vui lòng tải lại trang.',
                        dismissible: true
                    });

                    return false;
                }

                BANK_BOX.schema = res.schema;
                
                if (res.status) {
                    bankBox_loading(false);                        
                    
                    $('[data-brand-name]').text(BANK_BOX.schema.bank.brand_name)
                    
                    if (BANK_BOX.schema.can_online) {
                        bankBox_renderRequestConnectFormWithOtp()
                    } else {
                        bankBox_bankBox_renderRequestConnectFormWithTicket()
                    }
                    
                    return;
                }
                
                if (confirm) {
                    bankBox_loading(false)
                    
                    return;
                }
                
                if (res?.webview) {
                    bankBox_openWebview(
                        res.webview,
                        BANK_BOX.schema.authorize_connect_webview.width, 
                        BANK_BOX.schema.authorize_connect_webview.height,
                        {
                            closeCallback: () => {
                                if (BANK_BOX.schema.authorize_connect_type === 'redirect') {
                                    return bankBox_authorizeConnect(brandName, bankAccountType, confirm = true);
                                }
                            },
                        }
                    );
                    
                    return;
                }
                
                notyf.error({
                    message: res.message,
                    dismissible: true
                });

                bankBox_loading(false)
            },
            error: (err) => {
                bankBox_loading(false)
                
                if (err.status === 403) {
                    notyf.error({
                        message: 'Đã có lỗi xảy ra, vui lòng tải lại trang.',
                        dismissible: true
                    });
                    return false;
                }

                notyf.error({
                    message: 'Đã có lỗi xảy ra, vui lòng liên hệ SePay để được hỗ trợ.',
                    dismissible: true
                });
            }
        });
    }

    function bankBox_bankBox_renderRequestConnectFormWithTicket() {
        bankBox_setCurrentStep('request-connect');

        $('#bank-box .step').addClass('d-none');

        const step = $('#bank-box .step-request-connect-form');
        step.removeClass('d-none');

        step.html(`
        <div class="text-center w-100 mb-4">
            <div class="d-flex justify-content-center align-items-center w-100 mb-3">
                <div>
                    <img src="https://my.sepay.vn/assets/images/logo/sepay-icon.png" alt="SePay" style="height: 50px;">
                </div>
                <div style="height: 0.1rem; width: 32px; margin-right: -0.1rem; margin-left: 1rem;" class="bg-secondary"></div>
                <i class="bi bi-plugin fs-1 text-secondary"></i>
                <div style="height: 0.1rem; width: 32px; margin-left: -0.1rem; margin-right: 1rem;" class="bg-secondary"></div>
                <div>
                    <img src="<?= base_url('assets/images/banklogo') ?>/${BANK_BOX.schema.bank.icon_path}" alt="" style="height: 50px;">
                </div>
            </div>
            <h4 class="modal-title">Liên kết tài khoản <span data-bank-name="">${BANK_BOX.schema.bank.brand_name}</span> với SePay</h4>
        </div>
        <div class="row align-items-stretch">
            <div class="col-md-6">
                <div class="card mb-0">
                    <div class="card-body">
                        <h5 class="card-title">Chưa được mở API</h5>
                        <p class="card-text">Hãy điền thông tin bên dưới để được tư vấn.</p>
                        <button type="button" class="btn btn-primary" onclick="sendRequestEnterpriseBankBoxModal.show()">Gửi thông tin</button>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card mb-0">
                    <div class="card-body">
                        <h5 class="card-title">Đã mở API</h5>
                        <p class="card-text">Tôi đã được ngân hàng ${BANK_BOX.schema.bank.brand_name} mở API và sẵn sàng kết nối.</p>
                        <a href="<?= base_url('ticket/create') ?>" class="btn btn-primary">Tạo yêu cầu hỗ trợ</a>
                    </div>
                </div>
            </div>
        </div>
    `)
    }

    function bankBox_renderRequestConnectFormWithOtp() {
        bankBox_setCurrentStep('request-connect');

        $('#bank-box .step').addClass('d-none');

        const step = $('#bank-box .step-request-connect-form');
        step.removeClass('d-none');

        let additionalFieldsHtml = '';

        if (Object.keys(BANK_BOX.schema.additional_fields).length > 0) {
            Object.keys(BANK_BOX.schema.additional_fields).forEach((name) => {
                const field = BANK_BOX.schema.additional_fields[name];
                const type = field.type || 'text';

                if (field.enabled) {
                    switch (type) {
                        case 'text':
                            additionalFieldsHtml += `
                                <div class="mb-3 col">
                                    <div class="d-flex flex-column mb-2">
                                        <label for="bank-account-connect-${name}" class="form-label fw-bold mb-0">${field.label} ${field.rules?.required ? '<span class="text-danger">*</span>' : ''}</label>
                                        ${field.help_text ? `<small class="form-text text-muted d-block">${field.help_text}</small>` : ''}
                                    </div>
                                    <input type="text" class="form-control" id="bank-account-connect-${name}" name=${name} ${field.placeholder ? `placeholder="${field.placeholder}"` : ''}>
                                    <div class="invalid-feedback"></div>
                                </div>
                            `;
                            break;
                        case 'select':
                            additionalFieldsHtml += `
                                <div class="mb-3 col">
                                    <div class="d-flex flex-column mb-2">
                                        <label for="bank-account-connect-${name}" class="form-label fw-bold mb-0">${field.label} ${field.rules?.required ? '<span class="text-danger">*</span>' : ''}</label>
                                        ${field.help_text ? `<small class="form-text text-muted d-block">${field.help_text}</small>` : ''}
                                    </div>
                                    <select class="form-select ${field.className}" id="bank-account-connect-${name}" name=${name}>
                                        <option value="">- Chọn -</option>
                                        ${Object.keys(field.options).map(option => `<option value="${option}">${field.options[option]}</option>`).join('')}
                                    </select>
                                    <div class="invalid-feedback"></div>
                                </div>
                            `;
                            break;
                    }
                }
            });
        }

        step.html(`
            <div class="text-center w-100 mb-4">
                <div class="d-flex justify-content-center align-items-center w-100 mb-3">
                    <div>
                        <img src="https://my.sepay.vn/assets/images/logo/sepay-icon.png" alt="SePay" style="height: 50px;">
                    </div>
                    <div style="height: 0.1rem; width: 32px; margin-right: -0.1rem; margin-left: 1rem;" class="bg-secondary"></div>
                    <i class="bi bi-plugin fs-1 text-secondary"></i>
                    <div style="height: 0.1rem; width: 32px; margin-left: -0.1rem; margin-right: 1rem;" class="bg-secondary"></div>
                    <div>
                        <img src="<?= base_url('assets/images/banklogo') ?>/${BANK_BOX.schema.bank.icon_path}" alt="" style="height: 50px;">
                    </div>
                </div>
                <h4 class="modal-title">Liên kết tài khoản <span data-bank-name="">${BANK_BOX.schema.bank.brand_name}</span> với SePay</h4>
            </div>
            
            ${BANK_BOX.schema.request_connect_content ?? ''}

            <form class="form-request-connect" action="<?= base_url('bankBox/ajax_request_bank_account_connect') ?>/${BANK_BOX.schema.bank.brand_name}" method="post">
                <input type="hidden" name="<?= csrf_token() ?>" value="<?= csrf_hash() ?>">
                <input type="hidden" name="type" value="${BANK_BOX.schema.type}">

                ${BANK_BOX.schema.fields.account_number.enabled ? `
                <div class="mb-3">
                    <div class="d-flex flex-column mb-2">
                        <label for="bank-account-connect-account-number" class="form-label fw-bold mb-0">${BANK_BOX.schema.fields.account_number.label} <span class="text-danger">*</span></label>
                        <small class="form-text text-muted d-block">${BANK_BOX.schema.fields.account_number.help_text}</small>
                    </div>
                    <input type="${BANK_BOX.schema.fields.account_number.input_type || 'number'}" class="form-control" id="bank-account-connect-account-number" name="account_number" required>
                    <div class="invalid-feedback"></div>
                </div>
                ` : ''}

                ${BANK_BOX.schema.fields.account_holder_name.enabled ? `
                <div class="mb-3">
                    <div class="d-flex flex-column mb-2">
                    <label for="bank-account-connect-account-holder-name" class="form-label fw-bold mb-0">${BANK_BOX.schema.fields.account_holder_name.label} <span class="text-danger">*</span></label>
                    <small class="form-text text-muted d-block">${BANK_BOX.schema.fields.account_holder_name.help_text}</small>
                    </div>
                    <input type="text" class="form-control" id="bank-account-connect-account-holder-name" name="account_holder_name" ${BANK_BOX.schema.can_lookup_account_holder_name ? 'readonly' : ''} style="text-transform: uppercase;">
                    <div class="invalid-feedback"></div>
                </div>
                ` : ''}

                ${BANK_BOX.schema.fields.identification_number.enabled || BANK_BOX.schema.fields.phone_number.enabled ? `<div class="row">
                    ${BANK_BOX.schema.fields.identification_number.enabled ? `
                    <div class="mb-3 col">
                        <div class="d-flex flex-column mb-2">
                            <label for="bank-account-connect-identification-number" class="form-label fw-bold mb-0">${BANK_BOX.schema.fields.identification_number.label} <span class="text-danger">*</span></label>
                            ${BANK_BOX.schema.fields.identification_number.help_text ? `<small class="form-text text-muted d-block">${BANK_BOX.schema.fields.identification_number.help_text}</small>` : ''}
                        </div>
                        <input type="text" class="form-control" id="bank-account-connect-identification-number" name="identification_number">
                        <div class="invalid-feedback"></div>
                    </div>
                    ` : ''}

                    ${BANK_BOX.schema.fields.phone_number.enabled ? `
                    <div class="mb-3 col">
                        <div class="d-flex flex-column mb-2">
                            <label for="bank-account-connect-phone-number" class="form-label fw-bold mb-0">${BANK_BOX.schema.fields.phone_number.label} <span class="text-danger">*</span></label>
                            ${BANK_BOX.schema.fields.phone_number.help_text ? `<small class="form-text text-muted d-block">${BANK_BOX.schema.fields.phone_number.help_text}</small>` : ''}
                        </div>
                        <input type="number" class="form-control" id="bank-account-connect-phone-number" name="phone_number">
                        <div class="invalid-feedback"></div>
                    </div>
                    ` : ''}
                </div>` : ''}

                ${BANK_BOX.schema.fields.vav.enabled && BANK_BOX.defaultVAV ? `
                    <input type="hidden" id="bank-account-connect-vav" name="vav" value="${BANK_BOX.defaultVAV}">
                ` : ''}

                ${additionalFieldsHtml}

                ${BANK_BOX.schema.fields.label.enabled ? `
                <div class="mb-3 col">
                    <div class="d-flex flex-column mb-2">
                        <label for="bank-account-connect-label" class="form-label fw-bold mb-0">${BANK_BOX.schema.fields.label.label}</label>
                    </div>
                    <input type="text" class="form-control" id="bank-account-connect-label" name="label">
                    <div class="invalid-feedback"></div>
                </div>
                ` : ''}

                ${BANK_BOX.schema?.agreement?.required ? `
                <div class="mb-3">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" value="" id="bank-account-connect-agreement">
                        ${BANK_BOX.schema.agreement.type === 'modal' ? `
                            <label class="form-check-label" for="bank-account-connect-agreement">
                                Tôi đồng ý với <a href="#" onClick="agreementBankBoxModal.show()">các điều khoản và điều kiện dịch vụ của SePay và ngân hàng ${BANK_BOX.schema.bank.brand_name}</a>
                            </label>
                        ` : `<label class="form-check-label" for="bank-account-connect-agreement">
                            Tôi đồng ý với <a href="${BANK_BOX.schema.agreement.url}" target="_blank" rel="noreferrer">các điều khoản và điều kiện dịch vụ của SePay và ngân hàng ${BANK_BOX.schema.bank.brand_name}</a>
                        </label>`}
                    </div>
                </div>
                ` : ''}

                <button type="submit" class="btn btn-primary d-block w-100 btn-lg btn-save mt-3" ${BANK_BOX.schema?.agreement?.required ? 'disabled' : ''}>
                    <div class="spinner-border loader" style="width: 16px; height: 16px; display: none;" role="status"></div>
                    Liên kết
                </button>
            </form>
        `)

        agreementBankBoxModalDom.find('.modal-body').html(BANK_BOX.schema.agreement.content)

        const form = $('#bank-box .form-request-connect');

        const rules = {};

        const messages = {}

        if (BANK_BOX.schema.fields.account_number.enabled) {
            rules.account_number = {
                required: BANK_BOX.schema.fields.account_number.rules.required ?? true,
                maxlength: BANK_BOX.schema.fields.account_number.rules.max_length ?? 20,
            }

            if (BANK_BOX.schema.fields.account_number.input_type === 'text') {
                $(form).find('[name=account_number]').on('change paste keyup keydown', (e) => {
                    const value = $(e.target).val();
                    const allowedChars = /^[0-9a-zA-Z]*$/;

                    if (value && !allowedChars.test(value)) {
                        $(e.target).val(value.replace(/[^0-9a-zA-Z]/g, ''));
                    }
                })
            }
        }

        if (BANK_BOX.schema.fields.account_holder_name.enabled) {
            rules.account_holder_name = {
                required: BANK_BOX.schema.fields.account_number.rules.required ?? true,
                maxlength: BANK_BOX.schema.fields.account_number.rules.max_length ?? 20,
            }
            
            $(form).find('[name=account_holder_name]').on('change paste keyup keydown', () => {
                $(form).find('[name=account_holder_name]').val(
                    $(form).find('[name=account_holder_name]').val().toLowerCase()
                        .replace(/(à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ)/g, 'a')
                        .replace(/(è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ)/g, 'e')
                        .replace(/(ì|í|ị|ỉ|ĩ)/g, 'i')
                        .replace(/(ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ)/g, 'o')
                        .replace(/(ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ)/g, 'u')
                        .replace(/(ỳ|ý|ỵ|ỷ|ỹ)/g, 'u')
                        .replace(/(đ)/g, 'd')
                    .toUpperCase())
            })
        }

        if (BANK_BOX.schema.fields.identification_number.enabled) {
            rules.identification_number = {
                required: BANK_BOX.schema.fields.identification_number.rules.required ?? true,
                maxlength: BANK_BOX.schema.fields.identification_number.rules.max_length ?? 20
            };
        }

        if (BANK_BOX.schema.fields.phone_number.enabled) {
            rules.phone_number = {
                required: BANK_BOX.schema.fields.phone_number.rules.required ?? true,
                minlength: BANK_BOX.schema.fields.phone_number.rules.min_length ?? 10,
                maxlength: BANK_BOX.schema.fields.phone_number.rules.max_length ?? 11
            };
        }

        if (BANK_BOX.schema.fields.label.enabled) {
            rules.label = {
                maxlength: BANK_BOX.schema.fields.label.rules.max_length ?? 11
            };
        }
        
        Object.keys(BANK_BOX.schema.fields).forEach(key => {
            const field = BANK_BOX.schema.fields[key];

            rules[key] = {};
            messages[key] = {};

            if (field.rules?.required) {
                rules[key].required = true;
                messages[key].required = `Trường ${field.label} là bắt buộc.`;
            }

            if (field.rules?.min_length) {
                rules[key].minlength = field.rules.min_length;
                messages[key].minlength = `Trường ${field.label} phải có ít nhất ${field.rules.min_length} ký tự.`;
            }

            if (field.rules?.max_length) {
                rules[key].maxlength = field.rules.max_length;
                messages[key].maxlength = `Trường ${field.label} phải có tối đa ${field.rules.max_length} ký tự.`;
            }
        });

        Object.keys(BANK_BOX.schema.additional_fields).forEach(key => {
            const field = BANK_BOX.schema.additional_fields[key];

            rules[key] = {};
            messages[key] = {};

            if (field.rules?.required) {
                rules[key].required = true;
                messages[key].required = `Trường ${field.label} là bắt buộc.`;
            }

            if (field.rules?.min_length) {
                rules[key].minlength = field.rules.min_length;
                messages[key].minlength = `Trường ${field.label} phải có ít nhất ${field.rules.min_length} ký tự.`;
            }

            if (field.rules?.max_length) {
                rules[key].maxlength = field.rules.max_length;
                messages[key].maxlength = `Trường ${field.label} phải có tối đa ${field.rules.max_length} ký tự.`;
            }
        });

        $(form).find('#bank-account-connect-agreement').on('change', function() {
            const agreementInput = $(this);

            if (agreementInput.prop('checked')) {
                $(form).find('button[type="submit"]').prop('disabled', false);
            } else {
                $(form).find('button[type="submit"]').prop('disabled', true);
            }
        });

        BANK_BOX.requestConnectFormValidator = $(form).validate({
            submitHandler: function(form) {
                const formData = $(form).serializeArray();

                $(form).find('.btn-save').prop('disabled', true)
                $(form).find('.btn-save .loader').show();

                const submitted = () => {
                    $(form).find('.btn-save').prop('disabled', false)
                    $(form).find('.btn-save .loader').hide();
                }

                $.ajax({
                    url: $(form).attr('action'),
                    method: 'POST',
                    data: formData,
                    dataType: "JSON",
                    success: (res, status, request) => {
                        if (res.status == true) {
                            bankBox_unmountedRequestConnectForm()

                            BANK_BOX.context = res.context
                            
                            if (res?.schema) {
                              BANK_BOX.schema = res.schema;
                            }

                            if (BANK_BOX.schema.confirm_connect_type === 'callback') {
                                BANK_BOX.confirmConnectInterval = setInterval(() => {
                                    confirmConnectWithCallback()
                                }, 2000);
                            }

                            if (res?.webview) {
                                let webviewWidth, webviewHeight
                                
                                if (res?.otp) {
                                    webviewWidth = BANK_BOX.schema.confirm_connect_webview.width;
                                    webviewHeight = BANK_BOX.schema.confirm_connect_webview.height;
                                } else {
                                    webviewWidth = BANK_BOX.schema.request_connect_webview.width;
                                    webviewHeight = BANK_BOX.schema.request_connect_webview.height;
                                }

                                bankBox_openWebview(res.webview, webviewWidth, webviewHeight, {
                                    closeCallback: () => {
                                        if (BANK_BOX.schema.confirm_connect_type === 'redirect') {
                                            return confirmConnectWithRedirect();
                                        }
                                    }
                                })

                                return;
                            }

                            submitted();

                            if (res?.id) {
                                if (BANK_BOX.webview && !BANK_BOX.webview.closed) {
                                    BANK_BOX.webview.close();
                                }

                                bankBox_renderDoneNotification(res?.id);
                            }

                            if (res?.otp) {
                                bankBox_renderConfirmConnectFormWithOtp();
                            }
                        } else {
                            submitted();

                            notyf.error({
                                message: res.message,
                                dismissible: true
                            });
                        }
                    },
                    error: (err) => {
                        submitted();

                        if (err.status === 400) {
                            for (const field in err.responseJSON.messages) {
                                const fieldElement = $(form).find(`*[name=${field}]`);
                                if (fieldElement.length) {
                                    fieldElement.addClass('is-invalid');
                                    fieldElement.parent().children('.invalid-feedback').html(err.responseJSON.messages[field]);
                                }
                            }
                            
                            if (err.responseJSON.messages.vav && BANK_BOX.schema.fields.vav.enabled && BANK_BOX.defaultVAV) {
                                notyf.error({
                                    message: 'Đã có lỗi xảy ra, vui lòng liên hệ SePay để được hỗ trợ',
                                    dismissible: true
                                });
                            }
                            
                            return;
                        }

                        if (err.status === 403) {
                            notyf.error({
                                message: 'Đã có lỗi xảy ra, vui lòng tải lại trang',
                                dismissible: true
                            });
                            return;
                        }

                        notyf.error({
                            message: err.responseJSON.messages.error,
                            dismissible: true
                        });
                    }
                })
            },
            errorElement: 'div',
            rules,
            messages,
            highlight: function(input) {
                $(input).addClass('is-invalid');
            },
            unhighlight: function(input) {
                $(input).removeClass('is-invalid');
            },
            errorPlacement: function(error, input) {
                $(input).parent().children('.invalid-feedback').html(error);
            }
        })

        if (BANK_BOX.schema.can_lookup_account_holder_name) {
            form.find('[name=account_number]').on('blur', () => {
                lookupAccountHolderName()
            });

            const lookupAccountHolderName = () => {
                const accountHolderNameInput = form.find('[name=account_holder_name]');
                const accountNumber = form.find('[name=account_number]').val().trim();

                if (!accountNumber) {
                    accountHolderNameInput.val('');
                    return;
                }

                accountHolderNameInput.val('')
                accountHolderNameInput.attr('placeholder', 'Đang kiểm tra...');

                $.ajax({
                    url: `<?= base_url('bankBox/ajax_lookup_account_holder_name') ?>/${BANK_BOX.schema.bank.brand_name}`,
                    method: 'POST',
                    data: {
                        account_number: accountNumber,
                        '<?= esc(csrf_token()) ?>': '<?= esc(csrf_hash()) ?>'
                    },
                    success: (res) => {
                        if (!res.status && !res?.account_holder_name) {
                            accountHolderNameInput.val('');

                            notyf.error({
                                message: res.message,
                                dismissible: true
                            });
                            return;
                        }

                        accountHolderNameInput.val(res.account_holder_name);
                    },
                    error: (err) => {
                        accountHolderNameInput.val('');

                        if (err.status === 400) {
                            for (const field in err.responseJSON.messages) {
                                $(form).find(`[name=${field}]`).addClass('is-invalid')
                                $(form).find(`[name=${field}]`).parent().children('.invalid-feedback').html(err.responseJSON.messages[field])
                            }
                            return;
                        }

                        if (err.responseJSON.messages.vav && BANK_BOX.defaultVAV) {
                            notyf.error({
                                message: 'Đã có lỗi xảy ra, vui lòng liên hệ SePay để được hỗ trợ',
                                dismissible: true
                            });
                            return;
                        }

                        if (err.status === 403) {
                            notyf.error({
                                message: 'Đã có lỗi xảy ra, vui lòng tải lại trang',
                                dismissible: true
                            });
                            return;
                        }

                        notyf.error({
                            message: 'Đã có lỗi xảy ra, vui lòng liên hệ SePay để được hỗ trợ',
                            dismissible: true
                        });
                    },
                    complete: () => {
                        accountHolderNameInput.attr('placeholder', '');
                    }
                });
            }
        }

        function confirmConnectWithRedirect() {
            $.ajax({
                url: `<?= base_url('bankBox/ajax_check_confirm_bank_account_connect') ?>/${BANK_BOX.schema.bank.brand_name}`,
                method: 'POST',
                data: {
                    '<?= esc(csrf_token()) ?>': '<?= esc(csrf_hash()) ?>'
                },
                success: (res) => {
                    if (res.status == true) {
                        BANK_BOX.context = res?.context

                        if (res?.id && BANK_BOX.schema.create_va_option.support) {
                            bankBox_renderRequestCreateVaForm(res?.id)
                        } else if (res?.id) {
                            bankBox_renderDoneNotification(res?.id);
                        }
                    } else {
                        notyf.error({
                            message: 'Xác thực liên kết tài khoản ngân hàng không thành công',
                            dismissible: true
                        });
                    }
                },
                error: (err) => {
                    if (err.status === 403) {
                        notyf.error({
                            message: 'Đã có lỗi xảy ra, vui lòng tải lại trang',
                            dismissible: true
                        });
                        return;
                    }

                    notyf.error({
                        message: 'Đã có lỗi xảy ra, vui lòng liên hệ SePay để được hỗ trợ.',
                        dismissible: true
                    });
                }
            });
        }

        function confirmConnectWithCallback() {
            if (!BANK_BOX.webview || BANK_BOX.webview.closed) {
                clearInterval(BANK_BOX.webviewInterval);
            }

            $.ajax({
                url: `<?= base_url('bankBox/ajax_confirm_bank_account_connect') ?>/${BANK_BOX.schema.bank.brand_name}`,
                method: 'POST',
                data: {
                    '<?= esc(csrf_token()) ?>': '<?= esc(csrf_hash()) ?>'
                },
                success: (res) => {
                    if (res.status == true) {
                        BANK_BOX.context = res.context

                        if (BANK_BOX.webview && !BANK_BOX.webview.closed) {
                            BANK_BOX.webview.close();
                        }
                        
                        if (res?.id && BANK_BOX.schema.create_va_option.support) {
                            bankBox_renderRequestCreateVaForm(res?.id)
                        } else if (res?.id) {
                            bankBox_renderDoneNotification(res?.id);
                        }
                        clearInterval(BANK_BOX.confirmConnectInterval);
                    }
                },
                error: (err) => {
                    if (err.status === 403) {
                        notyf.error({
                            message: 'Đã có lỗi xảy ra, vui lòng tải lại trang',
                            dismissible: true
                        });
                        return;
                    }

                    notyf.error({
                        message: 'Đã có lỗi xảy ra, vui lòng liên hệ SePay để được hỗ trợ.',
                        dismissible: true
                    });

                    clearInterval(BANK_BOX.confirmConnectInterval);
                }
            });
        }

        function bankBox_initTomSelect() {
            document.querySelectorAll('.tom-select').forEach(function(element) {
                new TomSelect(element, {
                    allowEmptyOption: false,
                    searchField: ['text', 'value'],
                    plugins: ['dropdown_input']
                });
            });
        }

        bankBox_initTomSelect();
    }

    function bankBox_unmountedRequestConnectForm() {
        delete BANK_BOX.requestConnectFormValidator
    }

    function bankBox_renderConfirmConnectFormWithOtp() {
        bankBox_setCurrentStep('confirm-connect');

        $('#bank-box .step').addClass('d-none');

        const step = $('#bank-box .step-confirm-connect-form');
        step.removeClass('d-none');

        step.html(`
            <div class="text-center w-100 mb-4">
                <div class="d-flex justify-content-center align-items-center w-100 mb-3">
                    <div>
                        <img src="https://my.sepay.vn/assets/images/logo/sepay-icon.png" alt="SePay" style="height: 50px;">
                    </div>
                    <div style="height: 0.1rem; width: 32px; margin-right: -0.1rem; margin-left: 1rem;" class="bg-secondary"></div>
                    <i class="bi bi-plugin fs-1 text-secondary"></i>
                    <div style="height: 0.1rem; width: 32px; margin-left: -0.1rem; margin-right: 1rem;" class="bg-secondary"></div>
                    <div>
                        <img src="<?= base_url('assets/images/banklogo') ?>/${BANK_BOX.schema.bank.icon_path}" alt="" style="height: 50px;">
                    </div>
                </div>
                <h4 class="modal-title">Liên kết tài khoản <span data-bank-name="">${BANK_BOX.schema.bank.brand_name}</span> với SePay</h4>
            </div>
            <form class="form-confirm-connect" action="<?= base_url('bankBox/ajax_confirm_bank_account_connect') ?>/${BANK_BOX.schema.bank.brand_name}" method="post">
                <input type="hidden" name="<?= csrf_token() ?>" value="<?= csrf_hash() ?>">

                <div class="alert alert-info">
                    <div class="alert-message">${BANK_BOX.schema.otp.help_text
                        .replace(':account_number', BANK_BOX.context.account_number)
                        .replace(':phone_number', BANK_BOX.context.phone_number)
                    }</div>
                </div>

                <div class="mb-3 py-4" style="width: 300px; max-width: 100%; margin-left: auto; margin-right: auto;">
                    <label for="bank-account-connect-otp" class="form-label fw-bold">Nhập OTP</label>
                    <input type="number" class="form-control" id="bank-account-connect-otp" name="otp">
                    <div class="invalid-feedback"></div>

                    <button type="submit" class="btn btn-primary d-block w-100 mt-3 btn-lg btn-save">
                        <div class="spinner-border loader" style="width: 16px; height: 16px; display: none;" role="status"></div>
                        Xác thực OTP
                    </button>
                </div>

            </form>
            <hr>
            <div class="mb-0">
            Trong trường hợp không nhận được mã OTP hoặc mã OTP hết hiệu lực, bạn có thể nhấn
                <form method="post" action="<?= base_url('bankBox/ajax_retry_request_bank_account_connect') ?>/${BANK_BOX.schema.bank.brand_name}" class="d-inline-block form-retry-request-connect">
                    <input type="hidden" name="<?= csrf_token() ?>" value="<?= csrf_hash() ?>">

                    <button type="submit" class="btn btn-sm btn-outline-secondary rounded d-inline-flex align-items-center btn-save">
                        <div class="spinner-border text-black loader me-1" style="width: 14px; height: 14px; display: none;" role="status"></div>
                        Gửi lại mã
                    </button>
                </fom>
            </div>
        `)

        const form = $('#bank-box .form-confirm-connect');

        BANK_BOX.confirmConnectFormValidator = $(form).validate({
            submitHandler: function(form) {
                $(form).find('.btn-save').prop('disabled', true)
                $(form).find('.btn-save .loader').show();

                const formData = $(form).serializeArray();

                $.ajax({
                    url: $(form).attr('action'),
                    method: 'POST',
                    data: formData,
                    dataType: "JSON",
                    success: (res, status, request) => {
                        $(form).find('.btn-save').prop('disabled', false)
                        $(form).find('.btn-save .loader').hide();

                        if (res.status == true) {
                            bankBox_unmountedConfirmConnectForm()

                            BANK_BOX.context = res?.context

                            if (res?.id && BANK_BOX.schema.create_va_option.support) {
                                bankBox_renderRequestCreateVaForm(res?.id)
                            } else {
                                bankBox_renderDoneNotification(res?.id)
                            }
                        } else {
                            notyf.error({
                                message: res.message,
                                dismissible: true
                            });
                        }
                    },
                    error: (err) => {
                        $(form).find('.btn-save').prop('disabled', false)
                        $(form).find('.btn-save .loader').hide();

                        if (err.status === 400) {
                            for (const field in err.responseJSON.messages) {
                                $(form).find(`*[name=${field}]`).addClass('is-invalid')
                                $(form).find(`*[name=${field}]`).parent().children('.invalid-feedback').html(err.responseJSON.messages[field])
                            }
                            return;
                        }

                        if (err.status === 403) {
                            notyf.error({
                                message: 'Đã có lỗi xảy ra, vui lòng tải lại trang',
                                dismissible: true
                            });
                            return;
                        }

                        notyf.error({
                            message: 'Đã có lỗi xảy ra, vui lòng liên hệ SePay để được hỗ trợ',
                            dismissible: true
                        });
                    }
                })
            },
            errorElement: 'div',
            rules: {
                otp: {
                    required: true,
                    digits: true,
                }
            },
            messages: {
                otp: {
                    required: "Trường mã OTP là bắt buộc",
                    digits: "Trường mã OTP không hợp lệ",
                },
            },
            highlight: function(input) {
                $(input).addClass('is-invalid');
            },
            unhighlight: function(input) {
                $(input).removeClass('is-invalid');
            },
            errorPlacement: function(error, input) {
                $(input).parent().children('.invalid-feedback').html(error);
            }
        })

        $('#bank-box .form-retry-request-connect').on('submit', (e) => {
            e.preventDefault();

            const form = e.target

            $(form).find('.btn-save').prop('disabled', true)
            $(form).find('.btn-save .loader').show();

            $.ajax({
                url: $(form).attr('action'),
                method: 'POST',
                data: $(form).serialize(),
                dataType: "JSON",
                success: (res, status, request) => {
                    $(form).find('.btn-save').prop('disabled', false)
                    $(form).find('.btn-save .loader').hide();

                    if (res.status == true) {
                        BANK_BOX.context = res?.context;

                        notyf.success({
                            message: 'Đã gửi lại mã OTP',
                            dismissible: true
                        });
                    } else {
                        notyf.error({
                            message: res.message,
                            dismissible: true
                        });
                    }
                },
                error: (err) => {
                    $(form).find('.btn-save').prop('disabled', false)
                    $(form).find('.btn-save .loader').hide();

                    if (err.status === 403) {
                        notyf.error({
                            message: 'Đã có lỗi xảy ra, vui lòng tải lại trang',
                            dismissible: true
                        });
                        return;
                    }

                    notyf.error({
                        message: 'Đã có lỗi xảy ra, vui lòng liên hệ SePay để được hỗ trợ',
                        dismissible: true
                    });
                }
            })
        })

        $(document).on('click', '#bank-box .btn-try-request', () => {
            $('#bank-box .form-request-connect').submit();
        });
    }

    function bankBox_unmountedConfirmConnectForm() {
        delete BANK_BOX.confirmConnectFormValidator
    }
    
    function bankBox_renderRequestCreateVaForm(id) {
        bankBox_reduceForceClose();
        
        bankBox_setCurrentStep('request-create-va');

        $('#bank-box .step').addClass('d-none');

        const step = $('#bank-box .step-request-create-va-form');
        step.removeClass('d-none');

        step.html(`
            <div class="text-center w-100 mb-4">
                <div class="d-flex justify-content-center align-items-center w-100 mb-3">
                    <div>
                        <img src="https://my.sepay.vn/assets/images/logo/sepay-icon.png" alt="SePay" style="height: 50px;">
                    </div>
                    <div style="height: 0.1rem; width: 32px; margin-right: -0.1rem; margin-left: 1rem;" class="bg-secondary"></div>
                    <i class="bi bi-qr-code fs-1 text-secondary"></i>
                    <div style="height: 0.1rem; width: 32px; margin-left: -0.1rem; margin-right: 1rem;" class="bg-secondary"></div>
                    <div>
                        <img src="<?= base_url('assets/images/banklogo') ?>/${BANK_BOX.schema.bank.icon_path}" alt="" style="height: 50px;">
                    </div>
                </div>
                <h4 class="modal-title">${BANK_BOX.schema.create_va_option.title.replace(':brand_name', BANK_BOX.schema.bank.brand_name)}</h4>
            </div>
            
            ${BANK_BOX.schema.create_va_option.content}
            
            <div class="mt-4 input-create-va-option">
                <div class="row row-cols-1 g-2">
                    <div class="col ${BANK_BOX.schema.create_va_option.options.true.default ? 'order-1' : 'order-2'}">
                        <div class="form-check border p-3 rounded-3 d-flex">
                            <div class="my-auto">
                                <input class="form-check-input" type="radio" name="create_va_option" id="form-request-create-va-form-true" value="1" ${BANK_BOX.schema.create_va_option.options.true.default ? 'checked' : ''} style="width: 20px; height: 20px; margin: 0">
                            </div>
                            <label class="form-check-label ms-3" for="form-request-create-va-form-true">
                                <h5 class="d-flex align-items-center mb-1">${BANK_BOX.schema.create_va_option.options.true.label}</h5>
                                <div class="text-muted">${BANK_BOX.schema.create_va_option.options.true.description}</div>
                            </label>
                        </div>
                    </div>
                    <div class="col ${BANK_BOX.schema.create_va_option.options.false.default ? 'order-1' : 'order-2'}">
                        <div class="form-check border p-3 rounded-3 d-flex">
                            <div class="my-auto">
                                <input class="form-check-input" type="radio" name="create_va_option" id="form-request-create-va-form-false" value="0" ${BANK_BOX.schema.create_va_option.options.false.default ? 'checked' : ''} style="width: 20px; height: 20px; margin: 0">
                            </div>
                            <label class="form-check-label ms-3" for="form-request-create-va-form-false">
                                <h5 class="d-flex align-items-center mb-1">${BANK_BOX.schema.create_va_option.options.false.label}</h5>
                                <div class="text-muted">${BANK_BOX.schema.create_va_option.options.false.description.replace(':account_number', BANK_BOX.context.account_number)}</div>
                            </label>
                        </div>
                    </div>
                </div>
                
                <div class="help-text mt-2"></div>
            </div>
            
            <button type="button" class="btn btn-primary d-block w-100 mt-3 btn-lg btn-skip-create-va ${BANK_BOX.schema.create_va_option.options.false.default ? '' : 'd-none'}">
                Tiếp tục
            </button>
            
            <form class="form-request-create-va ${BANK_BOX.schema.create_va_option.options.true.default ? '' : 'd-none'}" action="${BANK_BOX.schema.create_va_option.is_official ? `<?= base_url('bankBox/ajax_request_create_official_va') ?>/${BANK_BOX.schema.bank.brand_name}` : `<?= base_url('bankBox/ajax_create_inhouse_va') ?>/${BANK_BOX.schema.bank.brand_name}`}" method="post">
                <input type="hidden" name="<?= csrf_token() ?>" value="<?= csrf_hash() ?>">
                <div class="d-flex justify-content-center items-align-center">
                    <button type="submit" class="btn btn-primary d-block w-100 mt-3 btn-lg btn-save">
                        <div class="spinner-border loader" style="width: 16px; height: 16px; display: none;" role="status"></div>
                        Tiếp tục
                    </button>
                </div>
            </form>
        `)
        
        $(document).on('change', '#bank-box input[name=create_va_option]', () => {
            if ($('#bank-box input[name=create_va_option]:checked').val() == '1') {
                $('#bank-box .input-create-va-option .help-text').html(BANK_BOX.schema.create_va_option.options.true?.help_text ?? '')
                $('#bank-box .form-request-create-va').removeClass('d-none');
                $('#bank-box .btn-skip-create-va').addClass('d-none');
            } else {
                $('#bank-box .input-create-va-option .help-text').html(BANK_BOX.schema.create_va_option.options.false?.help_text ?? '')
                $('#bank-box .form-request-create-va').addClass('d-none');
                $('#bank-box .btn-skip-create-va').removeClass('d-none');
            }
        })
        
        $(document).on('click', '#bank-box .btn-skip-create-va', () => {
            bankBox_renderDoneNotification(BANK_BOX.context.bank_account_id)
        })

        const form = $('#bank-box .form-request-create-va');

        BANK_BOX.requestCreateVaFormValidator = $(form).validate({
            submitHandler: function(form) {
                $(form).find('.btn-save').prop('disabled', true)
                $(form).find('.btn-save .loader').show();

                const formData = $(form).serializeArray();

                $.ajax({
                    url: $(form).attr('action'),
                    method: 'POST',
                    data: formData,
                    dataType: "JSON",
                    success: (res, status, request) => {
                        $(form).find('.btn-save').prop('disabled', false)
                        $(form).find('.btn-save .loader').hide();

                        if (res.status == true) {
                            bankBox_unmountedRequestCreateVaForm()

                            BANK_BOX.context = res?.context

                            if (res?.id && res?.va_id) {
                                bankBox_renderDoneNotification(res.id, res.va_id)
                                return;
                            }
                            
                            if (res?.otp) {
                                bankBox_renderConfirmCreateVaFormWithOtp()
                                return;
                            }
                        } else {
                            notyf.error({
                                message: res.message,
                                dismissible: true
                            });
                        }
                    },
                    error: (err) => {
                        $(form).find('.btn-save').prop('disabled', false)
                        $(form).find('.btn-save .loader').hide();

                        if (err.status === 400) {
                            for (const field in err.responseJSON.messages) {
                                $(form).find(`*[name=${field}]`).addClass('is-invalid')
                                $(form).find(`*[name=${field}]`).parent().children('.invalid-feedback').html(err.responseJSON.messages[field])
                            }
                            return;
                        }

                        if (err.status === 403) {
                            notyf.error({
                                message: 'Đã có lỗi xảy ra, vui lòng tải lại trang',
                                dismissible: true
                            });
                            return;
                        }

                        notyf.error({
                            message: 'Đã có lỗi xảy ra, vui lòng liên hệ SePay để được hỗ trợ',
                            dismissible: true
                        });
                    }
                })
            },
            errorElement: 'div',
            rules: {
                otp: {
                    required: true,
                    digits: true,
                }
            },
            messages: {
                otp: {
                    required: "Trường mã OTP là bắt buộc",
                    digits: "Trường mã OTP không hợp lệ",
                },
            },
            highlight: function(input) {
                $(input).addClass('is-invalid');
            },
            unhighlight: function(input) {
                $(input).removeClass('is-invalid');
            },
            errorPlacement: function(error, input) {
                $(input).parent().children('.invalid-feedback').html(error);
            }
        })

        $('#bank-box .form-retry-request-connect').on('submit', (e) => {
            e.preventDefault();

            const form = e.target

            $(form).find('.btn-save').prop('disabled', true)
            $(form).find('.btn-save .loader').show();

            $.ajax({
                url: $(form).attr('action'),
                method: 'POST',
                data: $(form).serialize(),
                dataType: "JSON",
                success: (res, status, request) => {
                    $(form).find('.btn-save').prop('disabled', false)
                    $(form).find('.btn-save .loader').hide();

                    if (res.status == true) {
                        BANK_BOX.context = res?.context;

                        notyf.success({
                            message: 'Đã gửi lại mã OTP',
                            dismissible: true
                        });
                    } else {
                        notyf.error({
                            message: res.message,
                            dismissible: true
                        });
                    }
                },
                error: (err) => {
                    $(form).find('.btn-save').prop('disabled', false)
                    $(form).find('.btn-save .loader').hide();

                    if (err.status === 403) {
                        notyf.error({
                            message: 'Đã có lỗi xảy ra, vui lòng tải lại trang',
                            dismissible: true
                        });
                        return;
                    }

                    notyf.error({
                        message: 'Đã có lỗi xảy ra, vui lòng liên hệ SePay để được hỗ trợ',
                        dismissible: true
                    });
                }
            })
        })

        $(document).on('click', '#bank-box .btn-try-request', () => {
            $('#bank-box .form-request-connect').submit();
        });
    }

    function bankBox_unmountedRequestCreateVaForm() {
        delete BANK_BOX.requestCreateVaFormValidator;
    }
    
    function bankBox_renderConfirmCreateVaFormWithOtp() {
        bankBox_reduceForceClose();
      
        bankBox_setCurrentStep('confirm-create-va');

        $('#bank-box .step').addClass('d-none');

        const step = $('#bank-box .step-confirm-create-va-form');
        step.removeClass('d-none');

        step.html(`
            <div class="text-center w-100 mb-4">
                <div class="d-flex justify-content-center align-items-center w-100 mb-3">
                    <div>
                        <img src="https://my.sepay.vn/assets/images/logo/sepay-icon.png" alt="SePay" style="height: 50px;">
                    </div>
                    <div style="height: 0.1rem; width: 32px; margin-right: -0.1rem; margin-left: 1rem;" class="bg-secondary"></div>
                    <i class="bi bi-qr-code fs-1 text-secondary"></i>
                    <div style="height: 0.1rem; width: 32px; margin-left: -0.1rem; margin-right: 1rem;" class="bg-secondary"></div>
                    <div>
                        <img src="<?= base_url('assets/images/banklogo') ?>/${BANK_BOX.schema.bank.icon_path}" alt="" style="height: 50px;">
                    </div>
                </div>
                <h4 class="modal-title">Tạo QR nhận thanh toán cho tài khoản <span data-bank-name="">${BANK_BOX.schema.bank.brand_name}</span></h4>
            </div>
            <form class="form-confirm-create-va" action="<?= base_url('bankBox/ajax_confirm_create_official_va') ?>/${BANK_BOX.schema.bank.brand_name}" method="post">
                <input type="hidden" name="<?= csrf_token() ?>" value="<?= csrf_hash() ?>">

                <div class="alert alert-info">
                    <div class="alert-message">${BANK_BOX.schema.create_va_option.otp.help_text
                        .replace(':account_number', BANK_BOX.context.account_number)
                        .replace(':phone_number', BANK_BOX.context.phone_number)
                    }</div>
                </div>

                <div class="mb-3 py-4" style="width: 300px; max-width: 100%; margin-left: auto; margin-right: auto;">
                    <label for="form-confirm-create-va-otp" class="form-label fw-bold">Nhập OTP</label>
                    <input type="number" class="form-control" id="form-confirm-create-va-otp" name="otp">
                    <div class="invalid-feedback"></div>

                    <button type="submit" class="btn btn-primary d-block w-100 mt-3 btn-lg btn-save">
                        <div class="spinner-border loader" style="width: 16px; height: 16px; display: none;" role="status"></div>
                        Xác thực OTP
                    </button>
                </div>

            </form>
            <hr>
            <div class="mb-0">
            Trong trường hợp không nhận được mã OTP hoặc mã OTP hết hiệu lực, bạn có thể nhấn
                <form method="post" action="<?= base_url('bankBox/ajax_retry_request_create_official_va') ?>/${BANK_BOX.schema.bank.brand_name}" class="d-inline-block form-retry-request-connect">
                    <input type="hidden" name="<?= csrf_token() ?>" value="<?= csrf_hash() ?>">

                    <button type="submit" class="btn btn-sm btn-outline-secondary rounded d-inline-flex align-items-center btn-save">
                        <div class="spinner-border text-black loader me-1" style="width: 14px; height: 14px; display: none;" role="status"></div>
                        Gửi lại mã
                    </button>
                </fom>
            </div>
        `)

        const form = $('#bank-box .form-confirm-create-va');

        BANK_BOX.confirmCreateVaFormValidator = $(form).validate({
            submitHandler: function(form) {
                $(form).find('.btn-save').prop('disabled', true)
                $(form).find('.btn-save .loader').show();

                const formData = $(form).serializeArray();

                $.ajax({
                    url: $(form).attr('action'),
                    method: 'POST',
                    data: formData,
                    dataType: "JSON",
                    success: (res, status, request) => {
                        $(form).find('.btn-save').prop('disabled', false)
                        $(form).find('.btn-save .loader').hide();

                        if (res.status == true) {
                            bankBox_unmountedConfirmConnectForm()

                            BANK_BOX.context = res?.context

                            if (res?.id && res?.va_id) {
                                bankBox_renderDoneNotification(res.id, res.va_id)
                                return
                            }
                            
                            throw new Error('Unexpected response')
                        } else {
                            notyf.error({
                                message: res.message,
                                dismissible: true
                            });
                        }
                    },
                    error: (err) => {
                        $(form).find('.btn-save').prop('disabled', false)
                        $(form).find('.btn-save .loader').hide();

                        if (err.status === 400) {
                            for (const field in err.responseJSON.messages) {
                                $(form).find(`*[name=${field}]`).addClass('is-invalid')
                                $(form).find(`*[name=${field}]`).parent().children('.invalid-feedback').html(err.responseJSON.messages[field])
                            }
                            return;
                        }

                        if (err.status === 403) {
                            notyf.error({
                                message: 'Đã có lỗi xảy ra, vui lòng tải lại trang',
                                dismissible: true
                            });
                            return;
                        }

                        notyf.error({
                            message: 'Đã có lỗi xảy ra, vui lòng liên hệ SePay để được hỗ trợ',
                            dismissible: true
                        });
                    }
                })
            },
            errorElement: 'div',
            rules: {
                otp: {
                    required: true,
                    digits: true,
                }
            },
            messages: {
                otp: {
                    required: "Trường mã OTP là bắt buộc",
                    digits: "Trường mã OTP không hợp lệ",
                },
            },
            highlight: function(input) {
                $(input).addClass('is-invalid');
            },
            unhighlight: function(input) {
                $(input).removeClass('is-invalid');
            },
            errorPlacement: function(error, input) {
                $(input).parent().children('.invalid-feedback').html(error);
            }
        })

        $('#bank-box .form-retry-request-connect').on('submit', (e) => {
            e.preventDefault();

            const form = e.target

            $(form).find('.btn-save').prop('disabled', true)
            $(form).find('.btn-save .loader').show();

            $.ajax({
                url: $(form).attr('action'),
                method: 'POST',
                data: $(form).serialize(),
                dataType: "JSON",
                success: (res, status, request) => {
                    $(form).find('.btn-save').prop('disabled', false)
                    $(form).find('.btn-save .loader').hide();

                    if (res.status == true) {
                        BANK_BOX.context = res?.context;

                        notyf.success({
                            message: 'Đã gửi lại mã OTP',
                            dismissible: true
                        });
                    } else {
                        notyf.error({
                            message: res.message,
                            dismissible: true
                        });
                    }
                },
                error: (err) => {
                    $(form).find('.btn-save').prop('disabled', false)
                    $(form).find('.btn-save .loader').hide();

                    if (err.status === 403) {
                        notyf.error({
                            message: 'Đã có lỗi xảy ra, vui lòng tải lại trang',
                            dismissible: true
                        });
                        return;
                    }

                    notyf.error({
                        message: 'Đã có lỗi xảy ra, vui lòng liên hệ SePay để được hỗ trợ',
                        dismissible: true
                    });
                }
            })
        })

        $(document).on('click', '#bank-box .btn-try-request', () => {
            $('#bank-box .form-request-connect').submit();
        });
    }

    function bankBox_unmountedConfirmCreateVaForm() {
        delete BANK_BOX.confirmCreateVaFormValidator
    }
    
    function bankBox_reduceForceClose() {
        window.addEventListener('beforeunload', bankBox_handleBeforeUnload);
      
        $(bankBoxDom).find('.btn-close').hide()
    }

    function bankBox_renderDoneNotification(id, vaId = null) {
        bankBox_reduceForceClose();
      
        if (BANK_BOX.webview && !BANK_BOX.webview.closed) {
            BANK_BOX.webview.close();
        }

        if (BANK_BOX.webviewInterval) {
            clearInterval(BANK_BOX.webviewInterval)
        }

        BANK_BOX.bankAccountId = id;

        bankBox_setCurrentStep('done');

        $('#bank-box .step').addClass('d-none');

        const step = $('#bank-box .step-done');
        step.removeClass('d-none');

        step.html(`
            <div class="text-center w-100 pt-4">
                <div class="d-flex justify-content-center align-items-center w-100 mb-3">
                    <div>
                        <img src="https://my.sepay.vn/assets/images/logo/sepay-icon.png" alt="SePay" style="height: 50px;">
                    </div>
                    <div style="height: 0.1rem; width: 32px; margin-right: -0.1rem; margin-left: 1rem;" class="bg-success"></div>
                    <i class="bi bi-check-circle-fill fs-1 text-success"></i>
                    <div style="height: 0.1rem; width: 32px; margin-left: -0.1rem; margin-right: 1rem;" class="bg-success"></div>
                    <div>
                        <img src="<?= base_url('assets/images/banklogo') ?>/${BANK_BOX.schema.bank.icon_path}" alt="" style="height: 50px;">
                    </div>
                </div>
                <h4 class="modal-title mb-2">Liên kết tài khoản thành công</h4>
                <div class="alert alert-success mt-5 mb-0">
                    <div class="alert-message">
                        Tài khoản ngân hàng ${BANK_BOX.schema.bank.brand_name} <b>${BANK_BOX.context.account_number}</b> đã được liên kết thành công với SePay.
                    </div>
                </div>

                <div class="w-100 mt-4">
                    <button type="button" class="btn btn-primary btn-lg" id="btn-bank-box-complete">
                        <span class="spinner-border loader" style="width: 16px; height: 16px; display: none;" role="status"></span>
                        Hoàn tất liên kết
                    </button>
                </div>
            </div>
        `)
        
        $('#btn-bank-box-complete').click(event => {
            const btn = $(event.target);
            
            btn.prop('disabled', true);
            btn.find('.loader').show();
            
            if (BANK_BOX.bankAccountId) {
                window.removeEventListener('beforeunload', bankBox_handleBeforeUnload);
                BANK_BOX.doneCallback(BANK_BOX.bankAccountId, BANK_BOX.context);
            }
            
            BANK_BOX.doneCallbackInterval = setInterval(() => {
                if (BANK_BOX.doneCallbackSuccess) {
                    bankBoxModal.hide();
                    btn.prop('disabled', false);
                    btn.find('.loader').hide();
                    clearInterval(BANK_BOX.doneCallbackInterval);
                }
            }, 100)
        })
    }

    function bankBox_setCurrentStep(step) {
        BANK_BOX.currentStep = step;
        bankBox_togglePrevStepButton();
    }

    function bankBox_togglePrevStepButton() {
        if (['choose-bank', 'request-create-va', 'done'].includes(BANK_BOX.currentStep)) {
            $('#bank-box .btn-prev-step').addClass('d-none');
        } else {
            $('#bank-box .btn-prev-step').removeClass('d-none');
        }
    }

    $('#bank-box .btn-prev-step').click(function() {
        if (BANK_BOX.webview && !BANK_BOX.webview.closed) {
            BANK_BOX.webview.close();
            clearInterval(BANK_BOX.webviewInterval);
        }

        if (BANK_BOX.currentStep === 'request-connect') {
            bankBox_renderChooseBank();
        } else if (BANK_BOX.currentStep === 'confirm-connect') {
            bankBox_renderRequestConnectFormWithOtp();
            clearInterval(BANK_BOX.confirmConnectInterval);
        } else if (BANK_BOX.currentStep === 'confirm-create-va') {
            bankBox_renderRequestCreateVaForm();
        }
    });
    
    $('#form-send-request-enterprise-bank-account-connect [name=has_account]').on('input change', (e) => {
        if ($(e.currentTarget).val() === "1") {
            $('.account-info').removeClass('d-none');
        } else {
            $('.account-info').addClass('d-none');
            $('#form-send-request-enterprise-bank-account-connect').validate().resetForm();
            $('#form-send-request-enterprise-bank-account-connect').find('.is-invalid').removeClass('is-invalid');
            $('#form-send-request-enterprise-bank-account-connect').find('.invalid-feedback').empty();
        }
    })

    $('#form-send-request-enterprise-bank-account-connect').validate({
        submitHandler: function(form) {
            $(form).find('.btn-save').prop('disabled', true)
            $(form).find('.btn-save .loader').show();

            $.ajax({
                url: $(form).attr('action') + '/' + BANK_BOX.schema.bank.brand_name,
                method: 'POST',
                data: $(form).serialize(),
                dataType: "JSON",
                success: (res) => {
                    if (res.status === true) {
                        sendRequestEnterpriseBankBoxModal.hide()

                        notyf.success({
                            message: res.message,
                            dismissible: true
                        });

                        $(form).find('.account-info').addClass('d-none');
                        $(form).trigger('reset');
                    } else {
                        notyf.error({
                            message: res.message,
                            dismissible: true
                        });
                    }
                },
                error: (err) => {
                    if (err.status === 400) {
                        for (const field in err.responseJSON.messages) {
                            $(form).find(`[name=${field}]`).addClass('is-invalid')
                            $(form).find(`[name=${field}]`).parents('.form-group').addClass('is-invalid')
                                .children('.invalid-feedback').show().html(`
                                <div id="${field}-error" class="error">${err.responseJSON.messages[field]}</div>
                            `)
                        }
                        return;
                    }

                    if (err.status === 403) {
                        notyf.error({
                            message: 'Đã có lỗi xảy ra, vui lòng tải lại trang',
                            dismissible: true
                        });
                        return;
                    }

                    notyf.error({
                        message: 'Đã có lỗi xảy ra, vui lòng liên hệ SePay để được hỗ trợ',
                        dismissible: true
                    });
                },
                complete: () => {
                    $(form).find('.btn-save').prop('disabled', false)
                    $(form).find('.btn-save .loader').hide();
                }
            })
        },
        errorElement: 'div',
        rules: {
            "company_name": {
                "required": true,
                "minlength": 5,
                "maxlength": 100,
            },
            phone_number: {
                "required": true,
                "digits": true,
                "minlength": 10,
                "maxlength": 20,
            },
            has_account: {
                "required": true,
            },
            account_number: {
                required: function(element) {
                    return $('#form-send-request-enterprise-bank-account-connect [name=has_account]').val() === "1";
                },
                minlength: 1,
                maxlength: 50,
            },
            branch_name: {
                required: function(element) {
                    return $('#form-send-request-enterprise-bank-account-connect [name=has_account]').val() === "1";
                },
                minlength: 1,
                maxlength: 100,
            }
        },
        messages: {
            company_name: {
                required: 'Trường tên tổ chức/cá nhân là bắt buộc',
                minlength: 'Trường tên tổ chức/cá nhân phải chứa ít nhất 5 ký tự',
                maxlength: 'Trường tên tổ chức/cá nhân không được vượt quá 100 ký tự',
            },
            phone_number: {
                required: "Trường số điện thoại là bắt buộc",
                digits: "Trường số điện thoại không hợp lệ",
                minlength: "Trường số điện thoại phải ít nhất 10 ký tự",
                maxlength: "Trường số điện thoại không được vượt quá 20 ký tự"
            },
            has_account: {
                required: 'Trường kiểm tra đã có tài khoản ngân hàng hay chưa là bắt buộc',
            },
            account_number: {
                required: 'Trường số tài khoản là bắt buộc',
            },
            branch_name: {
                required: 'Trường tên chi nhánh/phòng giao dịch là bắt buộc',
            }
        },
        highlight: function(input) {
            $(input).addClass('is-invalid');
            $(input).parent('.input-group').addClass('is-invalid');
        },
        unhighlight: function(input) {
            $(input).removeClass('is-invalid');
            $(input).parent('.input-group').removeClass('is-invalid');
        },
        errorPlacement: function(error, input) {
            $(error).show()
            $(input).parents('.form-group').children('.invalid-feedback').append(error);
        }
    });

    function openBankBox(doneCallback = null) {
        <?php if (isset($data_device) && $data_device['bank_id'] == 19): ?>
            bankBox_handleChooseBank(event, 'ABBANK', 'individual');
        <?php else: ?>
            bankBoxModal.show();
        <?php endif; ?>

        if (doneCallback) {
            BANK_BOX.doneCallback = doneCallback;
        }
    }
    
    function bankBox_openWebview(url, w, h, options = { closeCallback: null, interval: 100 }) {
        const left = (screen.width / 2) - (w / 2);
        const top = (screen.height / 2) - (h / 2);
        
        setTimeout(() => {
            BANK_BOX.webview = window.open(url, '', 'toolbar=no, location=no, directories=no, status=no, menubar=no, copyhistory=no, width=' + w + ', height=' + h + ', top=' + top + ', left=' + left);
    
            if (!BANK_BOX.webview) {
                alert("Không thể mở cửa sổ mới, vui lòng đảm bảo đã cho phép SePay mở cửa sổ trên trình duyệt của bạn và thử lại.");
                return false
            }
            
            if (options.closeCallback) {
                BANK_BOX.webviewInterval = setInterval(() => {
                    if (!BANK_BOX.webview || BANK_BOX.webview.closed) {
                        clearInterval(BANK_BOX.webviewInterval);
                        options.closeCallback();
                    }
                }, options.interval);
            }
        })
        
        return true;
    }
</script>
<?php unset($script);
return; ?>
<?php endif; ?>

<?php

$bankFeature = service('bankFeature');
$individualBanks = $bankFeature->bankFilter()->onlyApiSupport()->onlyIndividualSupport()->sort()->toArray();
$enterpriseBanks = $bankFeature->bankFilter()->onlyApiSupport()->onlyEnterpriseSupport()->sort()->toArray();

if ((isset($data_device) && $data_device['bank_id'] != 19) || session()->get('shop_billing')) {
    $individualBanks = array_filter($individualBanks, fn($bank) => $bank->brand_name !== 'ABBANK');
}
$speakerBillingFeature = service('speakerBillingFeature');

?>

<div class="modal fade" tabindex="-1" id="bank-box">
    <div class="modal-dialog">
        <div class="modal-content overflow-hidden">
            <div class="modal-header border-0">
                <?php if (!isset($data_device) || $data_device['bank_id'] != 19): ?>
                    <button type="button" class="btn btn-light btn-prev-step d-none"><i class="bi bi-reply-fill"></i> Trở lại</button>
                <?php endif; ?>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-sm-4 position-relative">
                <div class="loading-screen position-absolute opacity-50 bg-white top-0 start-0 w-100 h-100 d-none align-items-center justify-content-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Đang tải...</span>
                    </div>
                </div>

                <?php if (!isset($data_device) || $data_device['bank_id'] != 19): ?>
                <div class="step step-choose-bank">
                    <div class="text-center mb-4" style="width: 400px; max-width: 100%; margin-left: auto; margin-right: auto;">
                        <img src="<?= base_url('assets/images/logo/sepay-icon.png') ?>" alt="SePay" style="height: 50px;" class="">
                        <p class="text-center text-lg my-3">SePay hỗ trợ liên kết chính thức<br> với 9 ngân hàng</p>
                    </div>

                    <div class="d-flex justify-content-center mb-2">
                        <ul class="nav nav-tabs nav-pills bg-light rounded-lg border-0" id="bankAccountType" role="tablist">
                            <li class="nav-item p-1" role="presentation">
                                <button class="nav-link active" id="invididual-tab" data-bs-toggle="tab" data-bs-target="#invididual-tab-pane" type="button" role="tab" aria-controls="invididual-tab-pane" aria-selected="true">
                                    Cá nhân
                                </button>
                            </li>
                            <li class="nav-item p-1 ps-0" role="presentation">
                                <button class="nav-link" id="enterprise-tab" data-bs-toggle="tab" data-bs-target="#enterprise-tab-pane" type="button" role="tab" aria-controls="enterprise-tab-pane" aria-selected="false">
                                    Doanh nghiệp
                                </button>
                            </li>
                        </ul>
                    </div>

                    <p class="fw-bold">Chọn một ngân hàng</p>

                    <div class="tab-content" id="bankAccountTypeContent">
                        <div class="tab-pane fade show active" id="invididual-tab-pane" role="tabpanel" aria-labelledby="invididual-tab" tabindex="0">
                            <div class="table-responsive">
                                <table class="table table-hover mb-0" style="table-layout: fixed;">
                                    <tbody>
                                        <?php foreach ($individualBanks as $bank): ?>
                                            <tr
                                                class="cursor-pointer"
                                                onclick="bankBox_handleChooseBank(event, '<?= $bank->brand_name ?>', 'individual')">
                                                <td>
                                                    <div class="d-flex align-items-center gap-3" style="width: min-content;">
                                                        <img src="<?= base_url('assets/images/banklogo/' . $bank->icon_path) ?>" alt="<?= $bank->brand_name ?>" style="height: 30px;">
                                                        <span class="font-weight-bold"><?= $bank->brand_name ?></span>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="d-flex align-items-center justify-content-end gap-3">
                                                        <button type="button" class="btn btn-primary btn-connect d-flex align-items-center">
                                                            <span class="spinner-border loader me-2" style="width: 16px; height: 16px; display: none;" role="status"></span>
                                                            Kết nối
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="enterprise-tab-pane" role="tabpanel" aria-labelledby="enterprise-tab" tabindex="0">
                            <div class="table-responsive">
                                <table class="table table-hover mb-0" style="table-layout: fixed;">
                                    <tbody>
                                        <?php foreach ($enterpriseBanks as $bank): ?>
                                            <tr
                                                class="cursor-pointer"
                                                onclick="bankBox_handleChooseBank(event, '<?= $bank->brand_name ?>', 'enterprise')">
                                                <td>
                                                    <div class="cursor-pointer d-flex align-items-center gap-3" style="width: min-content;">
                                                        <img src="<?= base_url('assets/images/banklogo/' . $bank->icon_path) ?>" alt="<?= $bank->brand_name ?>" style="height: 30px;">
                                                        <span class="font-weight-bold"><?= $bank->brand_name ?></span>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="d-flex align-items-center justify-content-end gap-3">
                                                        <button type="button" class="btn btn-primary btn-connect d-flex align-items-center">
                                                            <span class="spinner-border loader me-2" style="width: 16px; height: 16px; display: none;" role="status"></span>
                                                            Kết nối
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <div class="step step-request-connect-form d-none"></div>

                <div class="step step-confirm-connect-form d-none"></div>
                
                <div class="step step-request-create-va-form d-none"></div>
                
                <div class="step step-confirm-create-va-form d-none"></div>

                <div class="step step-done d-none"></div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade bg-black bg-opacity-50" tabindex="-1" id="agreement-bank-box-modal">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Các điều khoản và điều kiện dịch vụ của SePay và ngân hàng <span data-brand-name></span></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" data-bs-dismiss="modal">Tôi đã hiểu</button>
            </div>
        </div>
    </div>
</div>


<div class="modal fade" id="send-request-enteprise-bank-box" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header border-0">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <?= form_open(base_url('bankBox/ajax_send_request_enterprise_bank_account_connect'), "id='form-send-request-enterprise-bank-account-connect'"); ?>
            <div class="modal-body p-4 ">
                <h3 class="text-center">Yêu cầu mở kết nối API ngân hàng <span data-brand-name></span> doanh nghiệp</h3>
                <p class="text-center">SePay là đối tác của <span data-brand-name></span>, chúng tôi sẽ chuyển tiếp thông tin của bạn đến <span data-brand-name></span> để được mở API và nhận được ưu đãi hấp dẫn nhất.</p>
                <div class="mb-3 form-group">
                    <label class="form-label" for="send-request-enterprise-bank-account-connect-company-name">Tên tổ chức/ Cá nhân muốn mở API tại <span data-brand-name></span> <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="send-request-enterprise-bank-account-connect-company-name" name="company_name" minlength="5" maxlength="500" value="" required>
                    <div class="invalid-feedback"></div>
                </div>
                <div class="mb-3 form-group">
                    <label for="send-request-enterprise-bank-account-connect-phone-number" class="form-label">Số điện thoại liên hệ <span class="text-danger">*</span></label>
                    <input type="number" class="form-control" id="send-request-enterprise-bank-account-connect-phone-number" name="phone_number" value="">
                    <small class="text-muted">Điền chính xác số điện thoại để ngân hàng <span data-brand-name></span> liên hệ với quý khách.</small>
                    <div class="invalid-feedback"></div>
                </div>
                <div class="mb-3 form-group">
                    <label class="form-label" for="send-request-enterprise-bank-account-connect-has-account">Bạn đã có tài khoản <span data-brand-name></span> chưa? <span class="text-danger">*</span></label>
                    <select class="form-select active" name="has_account" id="send-request-enterprise-bank-account-connect-has-account" aria-label="" required>
                        <option value="" selected>Chọn</option>
                        <option value="0">Chưa có</option>
                        <option value="1">Đã có</option>
                    </select>
                    <div class="invalid-feedback"></div>
                </div>
                <div class="account-info d-none">
                    <div class="mb-3 form-group">
                        <label class="form-label" for="account_number">Số tài khoản <span data-brand-name></span> <span class="text-danger">(*)</span></label>
                        <input type="text" class="form-control" id="account_number" name="account_number" minlength="1" maxlength="50" value="">
                        <div class="invalid-feedback"></div>
                    </div>
                    <div class="mb-3 form-group">
                        <label class="form-label" for="branch_name">Tên chi nhánh/Phòng GD <span data-brand-name></span> <span class="text-danger">(*)</span></label>
                        <input type="text" class="form-control" id="branch_name" name="branch_name" minlength="1" maxlength="100" value="">
                        <small class="text-muted">Điền tên chi nhánh/phòng giao dịch <span data-brand-name></span> đã mở tài khoản trên</small>
                        <div class="invalid-feedback"></div>
                    </div>
                </div>
                <div class="form-group">
                    <label for="send-request-enterprise-bank-account-connect-notes" class="form-label">Ghi chú thêm từ bạn</label>
                    <textarea class="form-control" id="send-request-enterprise-bank-account-connect-notes" name="notes"></textarea>
                    <div class="invalid-feedback"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-light me-2" data-bs-dismiss="modal">Hủy bỏ</button>
                <button type="submit" class="btn btn-primary btn-save d-flex align-items-center" style="gap: 0.25rem">
                    <span class="spinner-border loader" style="width: 16px; height: 16px; display: none;" role="status"></span>
                    Gửi đi
                </button>
            </div>
            <?= form_close() ?>
        </div>
    </div>
</div>