<?php

use App\Features\BillingFeature;
use App\Features\PaymentGateway\PaymentGatewayFeature;
use App\Models\CompanySubscriptionChangeModel;
use App\Models\CompanySubscriptionModel;
use App\Models\PgMerchantModel;
use App\Models\PgPaymentMethodModel;
use App\Models\ProductModel;
use CodeIgniter\HTTP\IncomingRequest;

$billingFeature = new BillingFeature;

if (!$billingFeature->pgEnabled) return; 

$pgMerchant = model(PgMerchantModel::class)->where('merchant_id', $billingFeature->pgMerchantId)->first();

if (!$pgMerchant) return;

/** @var IncomingRequest */
$request = service('request');
$paymentMethod = trim($request->getGet('payment_method'));

$pgPaymentMethods = model(PgPaymentMethodModel::class)
    ->where('pg_merchant_id', $pgMerchant->id)
    ->where('active', 1)
    ->get()->getResult();

$bankTransferPaymentMethod = current(array_values(array_filter($pgPaymentMethods, fn ($paymentMethod) => $paymentMethod->payment_method === 'BANK_TRANSFER')));
$cardPaymentMethod = current(array_values(array_filter($pgPaymentMethods, fn ($paymentMethod) => $paymentMethod->payment_method === 'CARD')));
$qrPayPaymentMethod = current(array_values(array_filter($pgPaymentMethods, fn ($paymentMethod) => $paymentMethod->payment_method === 'QRPAY')));

$subscription = model(CompanySubscriptionChangeModel::class)->where('company_id', $company->id)->first() 
    ?? model(CompanySubscriptionModel::class)->where('company_id', $company->id)->first();
$product = model(ProductModel::class)->find($subscription->plan_id);

$paymentGatewayFeature = new PaymentGatewayFeature;

$checkoutFields = [
    'merchant' => esc($pgMerchant->merchant_id),
    'order_id' => esc(create_paycode($invoice->id)),
    'order_amount' => esc($invoice->total),
    'operation' => 'PURCHASE',
    'order_invoice_number' => esc($invoice->id),
    'order_tax_amount' => esc($invoice->tax),
    'customer_id' => esc($company->id),
    'currency' => 'VND',
    'success_url' => base_url('company/ordersuccess?' . http_build_query(['invoice_id' => esc($invoice->id)])),
    'error_url' => base_url('company/ordersuccess?' . http_build_query(['invoice_id' => esc($invoice->id), 'response' => 'error'])),
    'cancel_url' => base_url('company/ordersuccess?' . http_build_query(['invoice_id' => esc($invoice->id),])),
];

$discountItems = array_values(array_filter($invoice_items, fn ($discountItem) => in_array($discountItem->type, ['Discount', 'SubscriptionDiff'])));
$discountFields = [];

if ($invoice->credit > 0) {
    $discountItems[] = (object) ['amount' => $invoice->credit, 'description' => 'Tín dụng'];
}

if (count($discountItems)) {
    $discountFields['order_discount_amount'] = 0;
    $discountFields['order_discount_description'] = [];
    
    foreach ($discountItems as $discountItem) {
        $discountFields['order_discount_amount'] += abs($discountItem->amount);
        $discountFields['order_discount_description'][] = $discountItem->description;
    }
    
    $discountFields['order_discount_description'] = implode(', ', $discountFields['order_discount_description']);
}

if (count($discountFields)) {
    $checkoutFields = array_merge($checkoutFields, $discountFields);
}

$onetimeCheckoutFields = array_merge($checkoutFields, [
    'order_description' => sprintf('Thanh toán hóa đơn #%s', $invoice->id)
]);

$agreementCheckoutFields = array_merge($checkoutFields, [
    'order_description' => sprintf('Thanh toán hóa đơn #%s - Kích hoạt gia hạn gói tự động', $invoice->id),
    'agreement_name' => $product->name,
    'agreement_type' => 'RECURRING',
    'agreement_amount_per_payment' => $subscription->recurring_payment,
    'agreement_payment_frequency' => $subscription->billing_cycle === 'monthly' ? 'DAILY' : 'YEARLY',
    'custom_data' => $subscription->plan_id,
]);

$paymentForms = [];

// if ($bankTransferPaymentMethod) {
//     $paymentForms['bankTransferOnetimeCheckout'] = array_merge($onetimeCheckoutFields, ['payment_method' => 'BANK_TRANSFER', 'branch_code' => '001']);
// }

if ($cardPaymentMethod) {
    $paymentForms['cardAgreementCheckout'] = array_merge($agreementCheckoutFields, ['payment_method' => 'CARD']);
    $paymentForms['cardOnetimeCheckout'] = array_merge($onetimeCheckoutFields, ['payment_method' => 'CARD']);
}

if ($qrPayPaymentMethod) {
    $paymentForms['qrPayOnetimeCheckout'] = array_merge($onetimeCheckoutFields, ['payment_method' => 'BANK_TRANSFER']);
}

?>

<?php if (isset($script) && $script): ?>
    <script>
        const autoRenewOption = $('#payment-gateway .option-auto-renew')
        const paymentMethodInput = $('#payment-gateway [name=payment_method]')
        
        window.addEventListener('pageshow', function (event) {
            if (event.persisted || window.performance.getEntriesByType("navigation")[0]?.type === "back_forward") {
                paymentMethodInput.find(':checked').prop('checked', true);
                paymentMethodInput.prop('checked', false);
            }
        });
        
        autoRenewOption.on('change', () => {
            const autoRenew = autoRenewOption.find('input').prop('checked')
            const paymentMethod = paymentMethodInput.filter(':checked').val().trim()
            
            if (paymentMethod === 'Card' && autoRenew) {
                $('#form-cardAgreementCheckout').show()
                $('#form-cardOnetimeCheckout').hide()
            } else {
                $('#form-cardAgreementCheckout').hide()
                $('#form-cardOnetimeCheckout').show()
            }
        })

        paymentMethodInput.on('change', (event) => {
            const paymentMethod = $(event.currentTarget).val().trim()
            
            $('.form-payment').hide()
            $('.btn-fake-payment').remove()
            
            if (paymentMethod === 'Card') {
                autoRenewOption.show()
            } else {
                autoRenewOption.hide()
            }
            
            if (paymentMethod === 'BankTransfer') {
                $('#container-qrcode').show()
                // $('#form-bankTransferOnetimeCheckout').show()
            }
            
            if (paymentMethod === 'Card') {
                $('#container-qrcode').hide()

                const autoRenew = autoRenewOption.find('input').prop('checked')
              
                if (autoRenew) {
                    $('#form-cardAgreementCheckout').show()
                    $('#form-cardOnetimeCheckout').hide()
                } else {
                    $('#form-cardAgreementCheckout').hide()
                    $('#form-cardOnetimeCheckout').show()
                }
            }
            
            if (paymentMethod === 'QRPay') {
                $('#form-qrPayOnetimeCheckout').show()
            }
        })
    </script>
    <?php unset($script); return; ?>
<?php endif; ?>
    

<div id="payment-gateway">
    <p class="mb-2 mt-4">Chọn hình thức thanh toán</p>
    <ul class="list-group mb-2">
        <?php if ($bankTransferPaymentMethod): ?>
        <li class="list-group-item d-flex align-items-start gap-3 px-3">
            <input class="form-check-input" style="width: 16px; margin-top: 2px; height: 16px; flex: none;" type="radio" name="payment_method" value="BankTransfer" id="payment-method-qrcode" checked>
            <label class="form-check-label" for="payment-method-qrcode">
                <p class="mb-0 fw-bold"><i class="bi bi-qr-code-scan"></i> Quét QR Code - Chuyển khoản ngân hàng</p>
            </label>
        </li>
        <?php endif ?>
        
        <?php if ($cardPaymentMethod): ?>
        <li class="list-group-item d-flex align-items-start gap-3 px-3">
            <input class="form-check-input" style="width: 16px; margin-top: 2px; height: 16px; flex: none;" type="radio" name="payment_method" value="Card" id="payment-method-card" style="flex: none;">
            <label class="form-check-label" for="payment-method-card">
                <p class="mb-0 fw-bold"><i class="bi bi-credit-card"></i> Thẻ tín dụng/ghi nợ</p>
            </label>
        </li>
        <?php endif ?>
        
        <?php if ($qrPayPaymentMethod): ?>
        <li class="list-group-item d-flex align-items-start gap-3 px-3">
            <input class="form-check-input" style="width: 16px; margin-top: 2px; height: 16px; flex: none;" type="radio" name="payment_method" value="QRPay" id="payment-method-qrpay" style="flex: none;">
            <label class="form-check-label" for="payment-method-qrpay">
                <p class="mb-0 fw-bold"><i class="bi bi-qr-code-scan"></i> Quét QR Pay - Chấp nhận thanh toán nguồn tiền tín dụng</p>
            </label>
        </li>
        <?php endif ?>
    </ul>
    
    <div class="option-auto-renew my-3" style="display: none;">
        <div class="form-check" id="input-auto-renew">
            <input class="form-check-input" type="checkbox" name="auto_renew" id="auto-renew" checked>
            <label class="form-check-label d-flex flex-column" for="auto-renew">
                Kích hoạt gia hạn gói dịch vụ tự động
                
                <small>Điều này đồng nghĩa khi cung cấp thông tin thẻ tín dụng/ghi nợ, bạn cho phép SePay tính phí thẻ của bạn cho các khoản thanh toán trong tương lai theo các điều khoản.</small>
            </label>
        </div>
    </div>
    
    <?php foreach ($paymentForms as $id => $fields): ?>
    <form action="<?= base_url('pay/v1/checkout/init') ?>" class="form-payment" style="display: none;" method="POST" id="form-<?= $id ?>">
        <?php foreach ($fields as $key => $value): ?>
            <input type="hidden" name="<?= esc($key); ?>" value="<?= esc($value); ?>">
        <?php endforeach; ?>
        <input type="hidden" name="signature" value="<?= $paymentGatewayFeature->signFields($fields, $pgMerchant->secret_key) ?>">
        <button type="submit" class="btn btn-primary btn-lg w-100 d-flex align-items-center gap-2 justify-content-center">Thanh toán ngay</button>
    </form>
    <?php endforeach ?>
</div>