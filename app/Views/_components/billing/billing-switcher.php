<?php
$defaultBillingType = isset($defaultBillingType) ? $defaultBillingType : 'transaction';

$billingTypes = [
    'transaction' => [
        'name' => '<PERSON> giao dịch',
        'icon' => 'bi bi-arrow-left-right',
        'features' => [
            'Hỗ trợ tất cả tính năng của SePay',
            'Tính phí theo giao dịch',
            'Hỗ trợ tất cả ngân hàng'
        ]
    ],
    'promotion' => [
        'name' => 'Theo ưu đãi',
        'icon' => 'bi bi-gift-fill',
        'features' => [
            'Hỗ trợ tất cả tính năng của SePay',
            'Tính phí theo giao dịch',
            'Chỉ hỗ trợ ngân hàng theo gói'
        ]
    ],
];

if ($hide_promotion_products ?? false) {
    unset($billingTypes['promotion']);
}

if (service('speakerBillingFeature')->enabled && (! $subscription_details || can_switch_speaker_plan_from_free_plan())) {
    $billingTypes['speaker'] = [
        'name' => '<PERSON> loa',
        'icon' => 'bi bi-speaker',
        'features' => [
            'Chỉ hỗ trợ tính năng của loa thanh toán',
            'Miễn phí tất cả giao dịch',
            'Hỗ trợ tất cả ngân hàng qua API'
        ]   
    ];
}

$shopBillingFeature = new \App\Features\ShopBilling\ShopBillingFeature();

if ($shopBillingFeature->enabled && ! $subscription_details) {
    $billingTypes['shop'] = [
        'name' => 'Theo điểm bán',
        'icon' => 'bi bi-shop',
        'features' => [
            'Chỉ hỗ trợ tính năng của cửa hàng',
            'Miễn phí tất cả giao dịch',
            'Hỗ trợ tất cả ngân hàng qua API'
        ],
        'badge' => 'Mới'
    ];
}

if (count($billingTypes) <= 1) {
    return;
}
?>

<?php if (isset($style) && $style): ?>
    <style>
        #billing-switcher .form-check:has(input[name="billing_type"]:checked) {
            --bs-border-opacity: 1;
            border-color: rgba(var(--bs-primary-rgb), var(--bs-border-opacity)) !important;
        }

        @media (max-width: 768px) {
            #billing-switcher .row {
                display: none;
            }

            #billing-switcher .mobile-dropdown {
                display: block;
                position: relative;
            }

            #billing-switcher .mobile-dropdown-btn {
                width: 100%;
                padding: 12px 15px;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                background: white;
                text-align: left;
                transition: all 0.3s ease;
            }

            #billing-switcher .mobile-dropdown-btn:hover {
                background: #f8f9fa;
            }

            #billing-switcher .mobile-dropdown-btn i {
                margin-right: 8px;
            }

            #billing-switcher .mobile-dropdown-content {
                display: none;
                position: absolute;
                top: 100%;
                left: 0;
                right: 0;
                background: white;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                margin-top: 5px;
                z-index: 1000;
                box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                opacity: 0;
                transform: translateY(-10px);
                transition: all 0.3s ease;
                pointer-events: none;
            }

            #billing-switcher .mobile-dropdown-content.show {
                display: block;
                opacity: 1;
                transform: translateY(0);
                pointer-events: auto;
            }

            #billing-switcher .mobile-dropdown-item {
                padding: 12px 15px;
                display: flex;
                align-items: center;
                border-bottom: 1px solid #dee2e6;
                cursor: pointer;
                transition: all 0.2s ease;
            }

            #billing-switcher .mobile-dropdown-item:last-child {
                border-bottom: none;
            }

            #billing-switcher .mobile-dropdown-item i {
                margin-right: 8px;
            }

            #billing-switcher .mobile-dropdown-item .badge {
                margin-left: 8px;
            }

            #billing-switcher .mobile-dropdown-item:hover {
                background: #f8f9fa;
            }
        }
        
        @media (min-width: 768px) {
            #billing-switcher-tabs {
                width: auto !important;
            }
        }
        
        .nav-tabs .nav-link {
            color: #000;
            border: none;
            background: transparent;
            border-radius: 0.3rem;
            padding: 0.25rem 1rem;
            color: var(--text-white);
        }

        .nav-tabs .nav-link.active {
            background-color: var(--bs-primary);
            --tw-shadow: 0 1px 3px 0 rgba(0, 0, 0, .1), 0 1px 2px -1px rgba(0, 0, 0, .1);
            --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
            box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
        }
    </style>

    <?php unset($style);
    return; ?>
<?php endif; ?>

<?php if (isset($script) && $script): ?>
    <script>
        $(document).ready(function() {
            $('#billing-switcher input[name="billing_type"]').on('change', () => {
                window.location.href = '?type=' + $('#billing-switcher input[name="billing_type"]:checked').val()
            });

            $('.mobile-dropdown-btn').click(function(e) {
                e.stopPropagation();
                const content = $(this).next('.mobile-dropdown-content');
                if (content.hasClass('show')) {
                    content.removeClass('show');
                } else {
                    $('.mobile-dropdown-content').removeClass('show');
                    content.addClass('show');
                }
            });

            $('.mobile-dropdown-item').click(function() {
                const value = $(this).data('value');
                $(`input[value="${value}"]`).prop('checked', true).trigger('change');
                $(this).closest('.mobile-dropdown-content').removeClass('show');
            });

            $(document).click(function() {
                $('.mobile-dropdown-content').removeClass('show');
            });
        });
    </script>
    <?php unset($script);
    return; ?>
<?php endif; ?>

<div class="mx-auto mt-3" style="max-width: 1200px" id="billing-switcher">
    
    
    <div>
        <div class="mx-auto d-flex">
            <ul id="billing-switcher-tabs" class="nav nav-tabs w-100 mx-md-auto nav-pills bg-white rounded-lg border d-flex flex-column flex-md-row" role="tablist">
            <?php foreach ($billingTypes as $key => $billingType): ?>
                <li class="nav-item p-1" role="presentation">
                    <a href="?type=<?= $key ?>" class="nav-link px-3 py-2 text-uppercase text-lg d-flex align-items-center <?= $defaultBillingType == $key ? 'active' : '' ?>" type="button" role="tab" aria-controls="linked-tab-pane" aria-selected="true">
                        <i class="<?= $billingType['icon'] ?> me-2 fs-3"></i>
                        <?= $billingType['name'] ?>
                        <?php if (isset($billingType['badge'])): ?>
                            <span class="badge bg-danger ms-2"><?= $billingType['badge'] ?></span>
                        <?php endif; ?>
                    </a>
                </li>
            <?php endforeach ?>
            </ul>
        </div>
    </div>
</div>