<?php

namespace App\Features\PaymentGateway\Contexts;

use App\Exceptions\PaymentGatewayException;
use App\Features\PaymentGateway\Interfaces\HasAgreementSupport;
use App\Features\PaymentGateway\Interfaces\RealtimeOrderFulfillmentWithQueryDR;
use App\Features\PaymentGateway\Interfaces\StoreablePaymentCard;
use App\Features\PaymentGateway\MpgsProfile;
use App\Features\PaymentGateway\PaymentGatewayFeature;
use App\Libraries\RabbitMQClient;
use App\Models\PgAgreementModel;
use App\Models\PgCardModel;
use App\Models\PgOrderModel;
use App\Models\PgSessionModel;
use App\Models\PgTransactionModel;
use CodeIgniter\HTTP\IncomingRequest;
use Exception;
use PhpAmqpLib\Message\AMQPMessage;
use PhpAmqpLib\Wire\AMQPTable;
use Throwable;

class MpgsCheckoutContext extends CheckoutContext implements RealtimeOrderFulfillmentWithQueryDR, StoreablePaymentCard, HasAgreementSupport
{
    public ?string $sessionId = null;
    
    public ?MpgsProfile $profile = null;
    
    public ?string $successIndicator = null;
    
    public ?string $orderVerifyId = null;
    
    public function __construct(PaymentGatewayFeature $feature, object $merchant, MpgsProfile $profile)
    {
        parent::__construct($feature, 'CARD');
        
        $this->merchant = $merchant;
        $this->profile = $profile;
    }
    
    public function initOnetimePaymentFields(): array
    {        
        assert($this->isOnetimePayment(), 'Must be onetime payment');
        
        $client = $this->profile->makeRestApiClient();
        
        helper(['general']);
        
        $response = $client->requestInitiateCheckout([
            'interaction' => [
                'operation' => $this->operation,
                'merchant' => [
                    'name' => remove_accents_with_intl($this->merchant->name),
                ],
            ],
            'customer' => [
                'account' => ['id' => $this->customerId]
            ],
            'order' => [
                'amount' => $this->orderAmount,
                'id' => $this->orderId,
                'description' => remove_accents_with_intl($this->orderDescription),
            ]
        ]);
        
        $responseJson = json_decode($response->getBody());
        
        return [
            'session_id' => $responseJson->session->id,
            'success_indicator' => $responseJson->successIndicator,
        ];
    }
    
    public function initAgreementPaymentFields(): array
    {
        assert($this->isAgreementPayment(), 'Must be agreement payment');
        
        $this->loadPgCustomer();
        $this->loadPgOrder();
        $this->loadPgCard();
        
        if ($this->pgCard && !$this->sessionId) {
            $client = $this->profile->makeRestApiClient();
            
            $response = $client->createSession();
            $responseJson = json_decode($response->getBody());
            
            $this->sessionId = $responseJson->session->id;
        }
        
        if (!$this->orderId) {
            $this->orderId = $this->merchant->merchant_id . strtoupper(uniqid());
        }
        
        if (!$this->agreementId) {
            $this->agreementId = $this->orderId;
        }
        
        return [
            'session_id' => $this->sessionId,
            'order_id' => $this->orderId,
            'agreement_id' => $this->agreementId
        ];
    }
    
    public function renderOnetimePaymentCheckoutView(): string
    {
        return view('pay/v1/checkout/mpgs/onetime', [
            'fields' => $this->getPaymentFields(),
            'context' => $this
        ]);
    }
    
    public function renderAgreementPaymentCheckoutView(): string
    {
        $this->loadPgCard();
        
        if (!$this->pgCard) {
            $this->loadPgCards();
        } 
        
        $isFullfillCheckout = $this->isFulfilledCheckout();
        
        $authenticate3ds2 = null;

        $orderAuthenticationStatus = $this->pgSession ? $this->getSessionMeta('order_authentication_status') : null;
        
        if ($this->isInitiateOrderAuthenticationProcess()) {
            $authenticate3ds2 = $this->initiateOrderAuthentication();
            $this->initPgSession([
                'order_authentication_status' => null, // 'INITIATED', 'PENDING', 'SUCCESSFUL', 'FAILED'
            ]);
            $this->requestOrderFulfillment();
        } else if ($orderAuthenticationStatus === 'INITIATED') {
            $authenticate3ds2 = $this->authenticateOrder();
        } else if ($orderAuthenticationStatus === 'SUCCESSFUL') {
            //
        }

        /** @var IncomingRequest $request */
        $request = service('request');
        $step = $request->getGet('step', FILTER_VALIDATE_INT) ?: 1;

        if ($step == 2 && !count($this->pgCards)) {
            $step = 1;
        }
        
        return view('pay/v1/checkout/mpgs/agreement', [
            'pg_merchant' => $this->merchant,
            'order_authentication_status' => $orderAuthenticationStatus,
            'authenticate_3ds2' => $authenticate3ds2,
            'pg_order' => $this->pgOrder,
            'is_fulfilled_checkout' => $isFullfillCheckout,
            'verify_order_id' => sprintf('VERIFY-%s', strtoupper(uniqid())),
            'context' => $this,
            'fields' => $this->getPaymentFields(),
            'step' => $step,
        ]);
    }
    
    protected function additionalPaymentFieldSchema(): array
    {
        return [
            [
                'field_name' => 'session_id',
                'property_name' => 'sessionId'
            ],
            [
                'field_name' => 'transaction_id',
                'property_name' => 'transactionId'
            ],
            [
                'field_name' => 'success_indicator',
                'property_name' => 'successIndicator'
            ],
        ];
    }
    
    public function requestOrderFulfillment($delaySeconds = 0): void
    {
        $rabbitmq = new RabbitMQClient;
        $queuable = $rabbitmq->connect();
        
        if (!$queuable) {
            throw new Exception('Checkout process must be offloaded');
        }
        
        $msg = new AMQPMessage(json_encode($this->getPaymentFields()), ['delivery_mode' => AMQPMessage::DELIVERY_MODE_PERSISTENT]);
        
        if ($delaySeconds) {
            $delayWorker = $delaySeconds .'s_delay_mpgs_order_fulfillment_worker';
            $delayRouteKey = $delaySeconds. 's_mpgs_order_fulfillment_delayed';
            $exchange = 'mpgs_order_fulfillment_exchange';
            $rabbitmq->queueDeclare('mpgs_order_fulfillment');
            
            $rabbitmq->channel->exchange_declare($exchange, 'direct', false, true, false);
            $rabbitmq->channel->queue_bind($rabbitmq->getQueueDeclaration('mpgs_order_fulfillment', 'name'), $exchange, $delayRouteKey);
            
            $rabbitmq->channel->queue_declare($delayWorker, false, true, false, false, false, new AMQPTable([
                'x-dead-letter-exchange' => $exchange,
                'x-dead-letter-routing-key' => $delayRouteKey,
                'x-message-ttl' => $delaySeconds * 1000
            ]));
            
            $rabbitmq->channel->basic_publish($msg, '', $delayWorker);
        } else {
            $rabbitmq->queueDeclare('mpgs_order_fulfillment');
            $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('mpgs_order_fulfillment', 'name'));
        }
    }
    
    public function storePaymentCard(): int
    {
        assert($this->sessionId != '', 'MPGS session ID is required');
        assert(is_object($this->pgOrder), 'PG order must be loaded');
        assert(is_object($this->pgCustomer), 'PG customer must be loaded');
        
        $this->loadPgCards();
        
        foreach ($this->pgCards as $pgCard) {
            $this->cardId = $pgCard->hash_id;
            $this->loadPgCard();
            
            if (!$this->pgCard) continue;
            
            $this->revokePgCard();
        }
        
        $client = $this->profile->makeRestApiClient();
        
        $response = $client->createToken($this->sessionId);
        
        if ($response->getStatusCode() !== 201) {
            throw new PaymentGatewayException(sprintf('Create payment token failed: %s', $response->getBody()), 0, $this->merchant->merchant_id);
        }
        
        $responseJson = json_decode($response->getBody());
        
        $cardDetails = $responseJson->sourceOfFunds->provided->card;
        
        $pgCardModel = model(PgCardModel::class);
        
        return $pgCardModel->insert([
            'company_id' => $this->profile->profile->company_id,
            'pg_merchant_id' => $this->merchant->id,
            'pg_profile_id' => $this->profile->profile->id,
            'pg_order_id' => $this->pgOrder->id,
            'pg_customer_id' => $this->pgOrder->pg_customer_id,
            'payment_token' => $responseJson->token,
            'status' => $responseJson->status,
            'card_number' => $cardDetails->number,
            'card_holder_name' => $cardDetails->nameOnCard,
            'card_expiry' => $this->profile->standardizationCardExpiry($cardDetails->expiry),
            'card_funding_method' => $this->profile->standardizationCardExpiry($cardDetails->fundingMethod),
            'card_brand' => $cardDetails->brand,
            'last_used_at' => $responseJson->usage->lastUsedTime ?? null,
        ]);
    }
    
    public function loadPgCards(): void
    {
        if (!$this->pgCustomer) return;
        
        $this->pgCards = slavable_model(PgCardModel::class, 'PaymentGateway')
            ->where('company_id', $this->merchant->company_id)
            ->where('pg_merchant_id', $this->merchant->id)
            ->where('pg_profile_id', $this->profile->profile->id)
            ->where('pg_customer_id', $this->pgCustomer->id)
            ->where('status', 'VALID')
            ->get()->getResult();
    }
    
    public function loadPgCard(): void
    {
        if (!$this->cardId || !$this->pgCustomer) return;
        
        $this->pgCard = model(PgCardModel::class)
            ->where('company_id', $this->merchant->company_id)
            ->where('pg_merchant_id', $this->merchant->id)
            ->where('pg_profile_id', $this->profile->profile->id)
            ->where('pg_customer_id', $this->pgCustomer->id)
            ->where('hash_id', $this->cardId)
            ->first();
    }
    
    public function loadPgTransaction(): void
    {
        if (!$this->transactionId || !$this->pgOrder) return;
        
        $this->pgTransaction = model(PgTransactionModel::class)
            ->where('company_id', $this->merchant->company_id)
            ->where('pg_merchant_id', $this->merchant->id)
            ->where('pg_profile_id', $this->profile->profile->id)
            ->where('pg_order_id', $this->pgOrder->id)
            ->where('transaction_id', $this->transactionId)
            ->first();
    }
    
    public function determineIfOrderAuthenticationInitiated(): bool
    {
        if (!$this->pgOrder || !$this->pgTransaction) return false;

        $this->loadPgTransaction();
        
        return $this->pgOrder 
            && $this->pgOrder->order_status === 'AUTHENTICATION_INITIATED'
            && $this->pgOrder->authentication_status === 'AUTHENTICATION_AVAILABLE'
            && $this->pgTransaction
            && $this->pgTransaction->transaction_type === 'AUTHENTICATION'
            && $this->pgTransaction->transaction_status === 'AUTHENTICATION_IN_PROGRESS'
            && $this->pgTransaction->authentication_status === 'AUTHENTICATION_AVAILABLE';
    }
    
    public function determineIfOrderAuthenticationFailed(): bool
    {
        if (!$this->pgOrder || !$this->pgTransaction) return false;

        return $this->pgOrder 
            && $this->pgOrder->order_status === 'AUTHENTICATION_UNSUCCESSFUL'
            && $this->pgOrder->authentication_status === 'AUTHENTICATION_FAILED';
    }
    
    public function determineIfOrderAuthenticationSucessful(): bool
    {
        if (!$this->pgOrder || !$this->pgTransaction) return false;

        $this->loadPgTransaction();
        
        return $this->pgOrder 
            && $this->pgOrder->authentication_status == 'AUTHENTICATION_SUCCESSFUL'
            && $this->pgTransaction
            && $this->pgTransaction->transaction_type == 'AUTHENTICATION'
            && $this->pgTransaction->transaction_status == 'APPROVED'
            && $this->pgTransaction->authentication_status == 'AUTHENTICATION_SUCCESSFUL';
    }
    
    public function determineIfOrderCaptured(): bool
    {
        if (!$this->pgOrder) return false;

        return $this->pgOrder && $this->pgOrder->order_status === 'CAPTURED';
    }
    
    public function determineIfOrderCancelled(): bool
    {
        if (!$this->pgOrder) return false;

        return $this->pgOrder && $this->pgOrder->order_status === 'CANCELLED';
    }
    
    public function subscribeToAgreement(): int
    {
        $this->transactionId = sprintf('RECURRING-%s', date('YmdHis'));
        $client = $this->profile->makeRestApiClient();
        
        $response = $client->payToken(
            $this->pgCard->payment_token, 
            $this->orderId, 
            $this->capturableOrderAmount(),
            $this->currency,
            $this->orderDescription,
            $this->orderInvoiceNumber,
            $this->orderDiscountAmount,
            $this->orderDiscountCode,
            $this->orderDiscountDescription,
            $this->orderTaxAmount,
            $this->transactionId, 
            $this->agreementId,
            $this->agreementAmountPerPayment,
            $this->agreementType,
            $this->agreementPaymentFrequency,
            $this->orderAmount == $this->agreementAmountPerPayment ? 'FIXED' : 'VARIABLE',
            $this->getAgreementPaymentFrequencyDays(),
        );
        
        if ($response->getStatusCode() !== 201) {
            throw new PaymentGatewayException(
                sprintf('Failed to subscribe to agreement | Order ID: %s | Response: %s', $this->orderId, $response->getBody()),
                $response->getStatusCode(),
                $this->merchant->merchant_id
            );
        }
        
        $responseJson = json_decode($response->getBody());
        
        if ($responseJson->result !== 'SUCCESS') {
            throw new PaymentGatewayException(
                sprintf('Failed to subscribe to agreement | Order ID: %s | Response: %s', $this->orderId, $response->getBody()),
                0,
                $this->merchant->merchant_id
            );
        }
        
        $retrieveAgreementResponse = $client->retrieveAgreement($this->agreementId);
        
        if ($retrieveAgreementResponse->getStatusCode() !== 200) {
            throw new PaymentGatewayException('Failed to retrieve agreement details');
        }

        $drAgreement = json_decode($retrieveAgreementResponse->getBody());
        
        if ($drAgreement->result !== 'SUCCESS') {
            throw new PaymentGatewayException('Failed to retrieve agreement details');
        }
        
        $pgAgreementModel = model(PgAgreementModel::class);
        
        $existingAgreement = $pgAgreementModel
            ->where('company_id', $this->profile->profile->company_id)
            ->where('pg_merchant_id', $this->merchant->id)
            ->where('agreement_id', $drAgreement->agreement->id)
            ->first();

        if ($existingAgreement) {
            throw new PaymentGatewayException(
                sprintf('Duplicate agreement when subscribe | Order ID: %s | Agreement ID: %s', $this->orderId, $drAgreement->agreement->id),
                409,
                $this->merchant->merchant_id
            );
        }
        
        $agreementId = $pgAgreementModel->insert([
            'company_id' => $this->profile->profile->company_id,
            'pg_merchant_id' => $this->merchant->id,
            'pg_customer_id' => $this->pgOrder->pg_customer_id,
            'pg_profile_id' => $this->profile->profile->id,
            'pg_order_id' => $this->pgOrder->id,
            'pg_card_id' => $this->pgCard->id,
            'agreement_name' => $this->agreementName,
            'agreement_id' => $drAgreement->agreement->id,
            'type' => $drAgreement->agreement->type,
            'status' => $drAgreement->agreement->status,
            'active' => 1,
            'auto_renew' => 1,
            'amount_variability' => $drAgreement->agreement->amountVariability ?? null,
            'start_date' => $drAgreement->agreement->startDate ?? null,
            'expiry_date' => $drAgreement->agreement->expiryDate ?? null,
            'amount_per_payment' => $this->agreementAmountPerPayment,
            'min_amount_per_payment' => $drAgreement->agreement->minimumAmountPerPayment ?? null,
            'max_amount_per_payment' => $drAgreement->agreement->maximumAmountPerPayment ?? null,
            'next_due_date' => $drAgreement->agreement->paymentFrequency === 'MONTHLY' 
                ? date('Y-m-d', strtotime('+1 month')) 
                : ($drAgreement->agreement->paymentFrequency === 'YEARLY' 
                    ? date('Y-m-d', strtotime('+1 year')) 
                    : ($drAgreement->agreement->paymentFrequency === 'DAILY' 
                        ? date('Y-m-d', strtotime('+1 day')) 
                        : null)),
            'payment_frequency' => $drAgreement->agreement->paymentFrequency ?? null,
            'minimum_days_between_payments' => $drAgreement->agreement->minimumDaysBetweenPayments ?? null,
        ]);
        
        if (!$agreementId) {
            throw new Exception('PG agremeent insert failed');
        }
        
        $pgAgreementModel
            ->where('company_id', $this->profile->profile->company_id)
            ->where('pg_merchant_id', $this->merchant->id)
            ->where('pg_customer_id', $this->pgOrder->pg_customer_id)
            ->where('id !=', $agreementId)
            ->set(['auto_renew' => 0, 'active' => 0])
            ->update();
        
        $pgOrderModel = model(PgOrderModel::class);
        $pgOrderModel->where('id', $this->pgOrder->id)->set(['pg_agreement_id' => $agreementId])->update();

        if ($this->mustBeVoidTransaction()) {
            $this->voidTransaction();
        }
        
        return $agreementId;
    }
    
    public function voidTransaction(): void
    {
        assert(!is_null($this->orderId), 'Order ID is required');
        assert(!is_null($this->transactionId), 'Order ID is required');
        
        $client = $this->profile->makeRestApiClient();
 
        $response = $client->voidTransaction($this->orderId, 'VOID-' . $this->transactionId, $this->transactionId);
        
        if ($response->getStatusCode() != 201) {
            throw new PaymentGatewayException(
                sprintf('Failed to void transaction | Order ID: %s | Target transaction ID: %s | Response: %s', 
                    $this->orderId, 
                    $this->transactionId, 
                    $response->getBody()
                ),
                $response->getStatusCode(),
                $this->merchant->merchant_id
            );
        }
    }
    
    public function initPgSession(array $meta = [])
    {
        /** @var IncomingRequest $request */
        $request = service('request');
        $traceId = str_replace('.', '', $this->merchant->merchant_id . '-' . strtoupper(uniqid('', true)));
        
        $this->traceId = $traceId;
        
        $pgSessionId = model(PgSessionModel::class)->insert([
            'queue_name' => 'mpgs_order_fulfillment',
            'trace_id' => $traceId,
            'company_id' => $this->merchant->company_id,
            'pg_merchant_id' => $this->merchant->id,
            'pg_profile_id' => $this->profile->profile->id,
            'fields' => json_encode($this->getPaymentFields()),
            'user_agent' => (string) $request->getUserAgent(),
            'ip_address' => $request->getIPAddress(),
            'expires_at' => date('Y-m-d H:i:s', strtotime('+10 minutes')),
            'meta' => count($meta) ? json_encode($meta) : null
        ]);
        
        if (!$pgSessionId) {
            throw new PaymentGatewayFeature('Failed to init PG session', 0, $this->merchant->merchant_id);
        }
    }
    
    public function determineIfPaymentCardStored(): bool
    {
        if (!is_object($this->pgOrder)) return false;
        
        return model(PgCardModel::class)->where('pg_order_id', $this->pgOrder->id)->where('status', 'VALID')->countAllResults() > 0;
    }
    
    public function revokePgCard(): bool
    {
        assert(is_object($this->pgCard), 'PG card must be loaded');
        
        $client = $this->profile->makeRestApiClient();
        
        if ($this->pgCard->status !== 'INVALID') {
            $client->deleteToken($this->pgCard->payment_token);
            
            return model(PgCardModel::class)->where('id', $this->pgCard->id)->set(['status' => 'INVALID'])->update();
        }
        
        return true;
    }
    
    /**
     * @return object{methodPostData: string, methodUrl: string}
     * @see https://test-gateway.mastercard.com/api/documentation/apiDocumentation/rest-json/version/100/operation/Authentication%3a%20%20Initiate%20Authentication.html?locale=en_US#responseFields
     */
    protected function initiateOrderAuthentication(): object
    {
        assert(!is_null($this->orderId), 'Order ID must be set');
        assert(!is_null($this->transactionId), 'Transaction ID must be set');
        assert(is_object($this->pgCard), 'PG card must be loaded');
        
        $client = $this->profile->makeRestApiClient();

        $response = $client->initAuthenticationToken($this->pgCard->payment_token, $this->orderId, $this->transactionId);
        
        if ($response->getStatusCode() !== 201) {
            throw new Exception('Initiate order authentication failed');
        }
        
        $responseJson = json_decode($response->getBody());
                
        return $responseJson->authentication->redirect->customizedHtml->{'3ds2'};
    }
    
    /**
     * @return string
     * @see https://test-gateway.mastercard.com/api/documentation/apiDocumentation/rest-json/version/100/operation/Authentication:%20%20Authenticate%20Payer.html?locale=en_US#x_response_authentication-redirect-html
     */
    protected function authenticateOrder(): string
    {
        assert(!is_null($this->orderId), 'Order ID must be set');
        assert(!is_null($this->transactionId), 'Transaction ID must be set');
        assert(is_object($this->pgCard), 'PG card must be loaded');
        
        $client = $this->profile->makeRestApiClient();

        $response = $client->authenticatePayerToken(
            $this->feature->baseUrl(sprintf('checkout/requestFulfill?%s', http_build_query($this->getPaymentFields(true)))),
            $this->pgCard->payment_token, 
            $this->orderId,
            $this->capturableOrderAmount(),
            $this->currency,
            $this->orderDescription,
            $this->orderInvoiceNumber,
            $this->orderDiscountAmount,
            $this->orderDiscountCode,
            $this->orderDiscountDescription,
            $this->orderTaxAmount,
            $this->transactionId, 
            $this->agreementId,
            $this->agreementAmountPerPayment,
            $this->agreementType,
            $this->agreementPaymentFrequency,
            $this->orderAmount == $this->agreementAmountPerPayment ? 'FIXED' : 'VARIABLE',
            date_create_from_format('my', $this->pgCard->card_expiry)->format('Y-m-01'),
            $this->getAgreementPaymentFrequencyDays(),
        );
        
        if ($response->getStatusCode() !== 201) {
            throw new Exception('Authenticate order failed', $response->getStatusCode());
        }
        
        $responseJson = json_decode($response->getBody());
        
        return $responseJson->authentication->redirect->html;
    }
    
    public function isInitiateOrderAuthenticationProcess(): bool
    {
        return is_object($this->pgCard) 
            && !is_null($this->transactionId) 
            && $this->sessionId 
            && is_null($this->pgSession);
    }
}