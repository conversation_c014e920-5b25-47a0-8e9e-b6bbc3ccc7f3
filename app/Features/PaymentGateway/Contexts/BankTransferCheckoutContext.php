<?php

namespace App\Features\PaymentGateway\Contexts;

use App\Features\PaymentGateway\BankAccountProfile;
use App\Features\PaymentGateway\PaymentGatewayFeature;
use App\Features\PaymentGateway\Contexts\CheckoutContext;
use App\Models\BankAccountModel;
use App\Models\BankSubAccountModel;
use App\Models\MbbEnterpriseAccountModel;
use App\Models\PgBranchModel;
use App\Models\PgBranchProfileModel;
use Exception;

class BankTransferCheckoutContext extends CheckoutContext
{
    use HasVietQR;
    
    /**
     * @var array<BankAccountProfile>
     */
    public array $profiles = [];
    
    public ?string $branchCode = null;
    
    public ?object $pgBranch = null;
    
    public array $pgBranchProfiles = [];
    
    public function __construct(PaymentGatewayFeature $feature, object $merchant, array $profiles)
    {
        parent::__construct($feature, 'BANK_TRANSFER');

        $this->merchant = $merchant;
        $this->profiles = $profiles;
    }

    public function initOnetimePaymentFields(): array
    {        
        assert($this->isOnetimePayment(), 'Must be onetime payment');

        return [];
    }

    public function initRecurringPaymentFields(): array
    {
        throw new Exception('Recurring payments are not supported for bank transfer checkout.');
    }

    public function renderOnetimePaymentCheckoutView(): string
    {
        $this->loadPgBranch();
        $this->loadPgBranchProfiles();
        
        if (!count($this->pgBranchProfiles)) {
            throw new Exception('No bank accounts available for this branch.');
        }
        
        return view('pay/v1/checkout/bank-transfer/onetime', [
            'fields' => $this->getPaymentFields(),
            'context' => $this
        ]);
    }

    public function renderRecurringPaymentCheckoutView(): string
    {
        throw new Exception('Recurring payments are not supported for bank transfer checkout.');
    }

    public function initAgreementPaymentFields(): array
    {
        throw new Exception('Agreement payments are not supported for bank transfer checkout.');
    }

    public function renderAgreementPaymentCheckoutView(): string
    {
        throw new Exception('Agreement payments are not supported for bank transfer checkout.');
    }

    public function initPgSession(array $meta = []): void
    {
        throw new Exception('PG Session initialization is not supported for bank transfer checkout.');
    }
    
    public function loadPgBranch(): void
    {
        if (!$this->branchCode) return;

        $this->pgBranch = model(PgBranchModel::class)
            ->where('pg_merchant_id', $this->merchant->id)
            ->where('company_id', $this->merchant->company_id)
            ->where('code', $this->branchCode)
            ->where('active', 1)
            ->first();
    }
    
    public function loadPgBranchProfiles(): void
    {
        if (!$this->pgBranch) return;

        $this->pgBranchProfiles = model(PgBranchProfileModel::class)
            ->where('tb_autopay_pg_branch_profile.pg_merchant_id', $this->merchant->id)
            ->where('tb_autopay_pg_branch_profile.company_id', $this->merchant->company_id)
            ->where('tb_autopay_pg_branch_profile.pg_branch_id', $this->pgBranch->id)
            ->whereIn('tb_autopay_pg_branch_profile.pg_profile_id', array_map(function ($profile) {
                return $profile->profile->id;
            }, $this->profiles))
            ->get()
            ->getResult();
            
        foreach ($this->pgBranchProfiles as $pgBranchProfile) {
            $pgBranchProfile->bank_account = model(BankAccountModel::class)
                ->select(['tb_autopay_bank_account.*', 'tb_autopay_bank.icon_path as bank_icon_path', 'tb_autopay_bank.logo_path as bank_logo_path', 'tb_autopay_bank.full_name as bank_full_name', 'tb_autopay_bank.short_name as bank_short_name', 'tb_autopay_bank.brand_name as bank_brand_name'])
                ->join('tb_autopay_bank', 'tb_autopay_bank.id = tb_autopay_bank_account.bank_id')
                ->where('tb_autopay_bank_account.id', $pgBranchProfile->bank_account_id)
                ->where('tb_autopay_bank_account.active', 1)
                ->first();
                
            if (!$pgBranchProfile->bank_account) {
                throw new Exception('Bank account not found for profile ID: ' . $pgBranchProfile->id);
            }
            
            switch ($pgBranchProfile->bank_account->bank_brand_name) {
                case 'MBB':
                    $pgBranchProfile->bank_account->enterprise = model(MbbEnterpriseAccountModel::class)
                        ->where('bank_account_id', $pgBranchProfile->bank_account->id)
                        ->where('active', 1)
                        ->first();
                    break;
                default:
                    $pgBranchProfile->bank_account->enterprise = null;
                    break;
            }
                
            if ($pgBranchProfile->bank_sub_account_id) {
                $pgBranchProfile->va = model(BankSubAccountModel::class)
                    ->where('id', $pgBranchProfile->bank_sub_account_id)
                    ->where('active', 1)
                    ->first();
            }
            
            $pgBranchProfile->qrcode = $this->generateVietQR(
                $pgBranchProfile->bank_account->id,
                $pgBranchProfile->bank_sub_account_id,
                $this->orderId,
                $this->orderAmount
            );
        }
    }
    
    protected function additionalPaymentFieldSchema(): array
    {
        return [
            [
                'field_name' => 'branch_code',
                'property_name' => 'branchCode'
            ],
        ];
    }
}