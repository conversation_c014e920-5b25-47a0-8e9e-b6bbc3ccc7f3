<?php

namespace App\Features\PaymentGateway\Contexts;

use App\Models\BankAccountModel;
use App\Models\BankModel;
use App\Models\BankSubAccountModel;

trait HasVietQR
{
    public function generateVietQR(int $bankAccountId, ?int $bankSubAccountId = null, ?string $orderId = null, ?int $amount = null): array
    {
        $bankModel = model(BankModel::class);
        $bankAccountModel = model(BankAccountModel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);
        
        $bankAccount = $bankAccountModel->find($bankAccountId);
        $bankSubAccount = $bankSubAccountId ? $bankSubAccountModel->find($bankSubAccountId) : null;
        $bank = $bankModel->find($bankAccount->bank_id);
        
        $accountNumber = $bankSubAccount && $bankSubAccount->acc_type === 'Real' ? $bankSubAccount->sub_account : $bankAccount->account_number;
        $holderName = $bankSubAccount && $bankSubAccount->acc_type === 'Real' ? $bankSubAccount->sub_holder_name : $bankAccount->account_holder_name;
        
        $remark = '';
        
        if (in_array($bank->brand_name, ['VietinBank', 'ABBANK'])) {
            $remark .= 'SEVQR';
        }
        
        if ($bankSubAccount && $bankSubAccount->acc_type === 'Virtual') {
            $remark .=  ' TKP' . $bankSubAccount->sub_account;
        }
        
        if ($orderId) {
            $remark .= ' ' . $orderId;
        }
        
        return [
            'url' => sprintf('https://qr.sepay.vn/img?bank=%s&acc=%s&template=&des=%s&amount=%s', $bank->bin, $accountNumber, $remark, $amount),
            'account_number' => $accountNumber,
            'holder_name' => $holderName,
            'remark' => $remark,
            'amount' => $amount,
        ];
    }
}