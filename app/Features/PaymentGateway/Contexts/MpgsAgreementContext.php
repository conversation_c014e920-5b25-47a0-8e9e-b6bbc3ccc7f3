<?php

namespace App\Features\PaymentGateway\Contexts;

use App\Features\PaymentGateway\Interfaces\RealtimeOrderFulfillmentWithQueryDR;
use App\Features\PaymentGateway\MpgsProfile;
use App\Features\PaymentGateway\PaymentGatewayFeature;
use App\Models\PgAgreementModel;
use App\Models\PgCardModel;
use Exception;

class MpgsAgreementContext extends AgreementContext
{
    public ?MpgsProfile $profile = null;
    
    public ?string $orderId = null;
    
    public function __construct(PaymentGatewayFeature $feature, object $merchant, MpgsProfile $profile)
    {
        parent::__construct($feature);
        
        $this->pgMerchant = $merchant;
        $this->profile = $profile;
    }
    
    public function cancelAgreement(): bool
    {
        assert(is_object($this->pgAgreement), 'PG agreement must be loaded');

        $pgAgreementModel = model(PgAgreementModel::class);
        $pgCardModel = model(PgCardModel::class);
        
        $pgCard = $pgCardModel->where('id', $this->pgAgreement->pg_card_id)->first();
        
        $profile = new MpgsProfile($pgCard->pg_profile_id);
        
        $client = $profile->makeRestApiClient();
        
        if ($pgCard->status !== 'INVALID') {
            $response = $client->deleteToken($pgCard->payment_token);
            
            $pgCardModel->where('id', $pgCard->id)->set(['status' => 'INVALID'])->update();
        }
        
        return $pgAgreementModel->where('id', $this->pgAgreement->id)->set([
            'auto_renew' => 0,
            'active' => 0,
        ])->update();
    }
    
    public function renewAgreement(array $data = []): bool
    {
        assert(is_object($this->pgAgreement), 'PG agreement must be loaded');
        assert(is_object($this->pgCustomer), 'PG customer must be loaded');
        assert(is_object($this->pgCard), 'PG card must be loaded');
        
        $paymentGatewayFeature = new PaymentGatewayFeature;
        $paymentGatewayFeature->withCheckoutContext($this->pgMerchant->merchant_id, 'CARD');
        
        /** @var MpgsCheckoutContext|null */
        $checkoutContext = $paymentGatewayFeature->checkoutContext();
        
        $fields = [
            'merchant' => $this->pgMerchant->merchant_id,
            'order_id' => $this->orderId,
            'order_description' => sprintf('Gia hạn gói đăng ký %s', $this->pgAgreement->agreement_id),
            'order_currency' => $this->pgAgreement->order_currency,
            'agreement_id' => $this->pgAgreement->agreement_id,
            'customer_id' => $this->pgCustomer->customer_id,
            'operation' => 'PURCHASE',
        ];
        
        if (isset($data['order_invoice_number'])) {
            $fields['order_invoice_number'] = $data['order_invoice_number'];
        }
        
        $fields['signature'] = $checkoutContext->signFields($fields);
        
        $checkoutContext->loadPaymentFields($fields);
        
        if (!$this->pgAgreement->active) {
            throw new Exception('Agreement is inactive', 400);
        }
        
        if (!$this->pgAgreement->auto_renew) {
            throw new Exception('Agreement can not auto renew', 400);
        }
        
        if ($this->pgCard->status === 'INVALID') {
            throw new Exception('Payment card invalid', 400);
        }
        
        if ((strtotime($this->pgAgreement->next_due_date) - time()) / (60 * 60 * 24) > $this->pgAgreement->minimum_days_between_payments) {
            throw new Exception('Nex due day is still far away', 419);
        }
        
        $client = $this->profile->makeRestApiClient();
        
        $response = $client->payTokenSubsequence(
            $this->pgCard->payment_token,
            $this->orderId,
            $this->pgAgreement->amount_per_payment,
            $fields['order_description'],
            $fields['order_invoice_number'],
            $this->pgAgreement->order_currency,
            sprintf('RECURRING-%s', date('YmdHis')),
            $this->agreementId
        );
        
        if ($response->getStatusCode() != 201) {
            throw new Exception('Pay token subsequence failed');
        }
        
        $checkoutContext->initPgSession();
        $checkoutContext->requestOrderFulfillment();
        
        return true;
    }
    
    protected function additionalFieldSchema(): array
    {
        return [
            [
                'field_name' => 'order_id',
                'property_name' => 'orderId'
            ]
        ];
    }
}
