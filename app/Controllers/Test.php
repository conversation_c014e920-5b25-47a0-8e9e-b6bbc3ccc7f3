<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Models\OnboardingSubmissionModel;
use App\Models\PgMerchantModel;
use App\Models\CompanyModel;

class Test extends BaseController
{
    public function index()
    {
        $data = [
            'page_title' => 'Duyệt Onboarding',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'pending_submissions' => $this->getPendingSubmissions(),
            'approved_submissions' => $this->getApprovedSubmissions()
        ];

        return view('admin/onboarding-approval/index', $data);
    }

    private function getPendingSubmissions()
    {
        return model(OnboardingSubmissionModel::class)
            ->select('tb_autopay_pg_onboarding_submissions.*, tb_autopay_company.full_name, tb_autopay_company.short_name')
            ->join('tb_autopay_company', 'tb_autopay_company.id = tb_autopay_pg_onboarding_submissions.company_id')
            ->where('tb_autopay_pg_onboarding_submissions.status', 'submitted')
            ->orderBy('tb_autopay_pg_onboarding_submissions.submitted_at', 'ASC')
            ->findAll();
    }

    private function getApprovedSubmissions()
    {
        return model(OnboardingSubmissionModel::class)
            ->select('tb_autopay_pg_onboarding_submissions.*, tb_autopay_company.full_name, tb_autopay_company.short_name')
            ->join('tb_autopay_company', 'tb_autopay_company.id = tb_autopay_pg_onboarding_submissions.company_id')
            ->whereIn('tb_autopay_pg_onboarding_submissions.status', ['approved', 'rejected'])
            ->orderBy('tb_autopay_pg_onboarding_submissions.reviewed_at', 'DESC')
            ->findAll();
    }

    public function approve()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON(['status' => false, 'message' => 'Invalid request']);
        }

        $data = $this->request->getPost();
        
        if (!$this->validate([
            'company_id' => 'required|numeric',
            'payment_method' => 'required|string'
        ])) {
            return $this->response->setJSON([
                'status' => false,
                'errors' => $this->validator->getErrors()
            ]);
        }

        try {
            $result = $this->approveOnboarding($data['company_id'], $data['payment_method']);
            return $this->response->setJSON($result);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Có lỗi xảy ra: ' . $e->getMessage()
            ]);
        }
    }

    public function reject()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON(['status' => false, 'message' => 'Invalid request']);
        }

        $data = $this->request->getPost();
        
        if (!$this->validate([
            'company_id' => 'required|numeric',
            'payment_method' => 'required|string',
            'rejection_reason' => 'required|string|min_length[10]'
        ])) {
            return $this->response->setJSON([
                'status' => false,
                'errors' => $this->validator->getErrors()
            ]);
        }

        try {
            $result = $this->rejectOnboarding($data['company_id'], $data['payment_method'], $data['rejection_reason']);
            return $this->response->setJSON($result);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Có lỗi xảy ra: ' . $e->getMessage()
            ]);
        }
    }

    private function approveOnboarding($companyId, $paymentMethod)
    {
        $db = \Config\Database::connect();
        $db->transStart();

        try {
            // 1. Cập nhật status thành 'approved'
            model(OnboardingSubmissionModel::class)->updateStatus(
                $companyId, 
                $paymentMethod, 
                'approved'
            );

            // 2. Kiểm tra xem company đã có merchant chưa
            $existingMerchant = model(PgMerchantModel::class)
                ->where('company_id', $companyId)
                ->first();

            if (!$existingMerchant) {
                // 3. Tạo merchant mới
                $merchantData = [
                    'merchant_id' => $this->generateMerchantId($companyId),
                    'name' => $this->getCompanyName($companyId),
                    'active' => 1,
                    'company_id' => $companyId
                ];

                $merchantId = model(PgMerchantModel::class)->insert($merchantData);

                // 4. Cập nhật company với merchant_id
                model(CompanyModel::class)->update($companyId, [
                    'merchant_id' => $merchantId
                ]);
            }

            $db->transComplete();

            if ($db->transStatus() === false) {
                throw new \Exception('Database transaction failed');
            }

            return [
                'status' => true,
                'message' => 'Đã duyệt onboarding thành công và tạo merchant'
            ];

        } catch (\Exception $e) {
            $db->transRollback();
            throw $e;
        }
    }

    private function rejectOnboarding($companyId, $paymentMethod, $rejectionReason)
    {
        // Cập nhật status thành 'rejected' với lý do
        model(OnboardingSubmissionModel::class)->updateStatus(
            $companyId, 
            $paymentMethod, 
            'rejected'
        );

        // Có thể lưu rejection_reason vào database nếu cần
        // model(OnboardingSubmissionModel::class)->update($submissionId, [
        //     'rejection_reason' => $rejectionReason
        // ]);

        return [
            'status' => true,
            'message' => 'Đã từ chối onboarding'
        ];
    }

    private function generateMerchantId($companyId)
    {
        $company = model(CompanyModel::class)->find($companyId);
        $timestamp = time();
        $random = strtoupper(substr(md5($company->short_name . $timestamp), 0, 6));
        
        return 'MERCH_' . $company->short_name . '_' . $random;
    }

    private function getCompanyName($companyId)
    {
        $company = model(CompanyModel::class)->find($companyId);
        return $company->full_name;
    }

    public function getSubmissionDetails()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON(['status' => false, 'message' => 'Invalid request']);
        }

        $companyId = $this->request->getGet('company_id');
        $paymentMethod = $this->request->getGet('payment_method');

        if (!$this->validate([
            'company_id' => 'required|numeric',
            'payment_method' => 'required|string'
        ])) {
            return $this->response->setJSON([
                'status' => false,
                'errors' => $this->validator->getErrors()
            ]);
        }

        $submission = model(OnboardingSubmissionModel::class)
            ->where([
                'company_id' => $companyId,
                'payment_method' => $paymentMethod
            ])
            ->first();

        if (!$submission) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Không tìm thấy submission'
            ]);
        }

        $company = model(CompanyModel::class)->find($companyId);
        $onboardData = json_decode($submission->onboard_data, true);

        return $this->response->setJSON([
            'status' => true,
            'submission' => $submission,
            'company' => $company,
            'onboard_data' => $onboardData
        ]);
    }
}
