<?php

namespace App\Controllers\Pg;

use App\Controllers\BaseController;
use App\Models\PgProfileModel;
use App\Models\PgPaymentMethodModel;
use App\Models\PgMerchantModel;
use App\Models\BankAccountModel;
use App\Models\PgMpgsProfileModel;

class Profile extends BaseController
{
    public function create()
    {
        if (! $this->request->isAJAX()) {
            return $this->response->setJSON(['status' => false, 'message' => 'Invalid request']);
        }

        $data = $this->request->getPost();

        if (! $this->validate([
            'payment_method' => 'required|in_list[BANK_TRANSFER,CARD,NAPAS_QR]',
            'profile_type' => 'required|in_list[BANK_ACCOUNT,NAPAS,MPGS]',
            'is_default' => 'permit_empty|in_list[0,1]'
        ])) {
            return $this->response->setJSON([
                'status' => false,
                'errors' => $this->validator->getErrors()
            ]);
        }

        $merchant = model(PgMerchantModel::class)
            ->where('company_id', $this->company_details->id)
            ->first();

        if (! $merchant) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Merchant chưa được tạo'
            ]);
        }

        $paymentMethod = model(PgPaymentMethodModel::class)
            ->where([
                'pg_merchant_id' => $merchant->id,
                'payment_method' => $data['payment_method'],
                'active' => 1
            ])
            ->first();

        if (! $paymentMethod) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Phương thức thanh toán chưa được kích hoạt'
            ]);
        }

        $result = $this->createProfile($merchant->id, $paymentMethod->id, $data);

        return $this->response->setJSON($result);
    }

    private function createProfile($merchantId, $paymentMethodId, $data)
    {
        $profileData = [
            'pg_merchant_id' => $merchantId,
            'pg_payment_method_id' => $paymentMethodId,
            'type' => $data['profile_type'],
            'default' => $data['is_default'] ?? 0,
            'active' => 1
        ];

        switch ($data['profile_type']) {
            case 'BANK_ACCOUNT':
                return $this->createBankAccountProfile($profileData, $data);
            case 'NAPAS':
                return $this->createNapasProfile($profileData, $data);
            case 'MPGS':
                return $this->createMpgsProfile($profileData, $data);
            default:
                return ['status' => false, 'message' => 'Loại profile không hợp lệ'];
        }
    }

    private function createBankAccountProfile($profileData, $data)
    {
        if (empty($data['bank_account_id'])) {
            return ['status' => false, 'message' => 'Bank account ID là bắt buộc'];
        }

        $bankAccount = model(BankAccountModel::class)
            ->where([
                'id' => $data['bank_account_id'],
                'company_id' => $this->company_details->id,
                'active' => 1
            ])
            ->first();

        if (! $bankAccount) {
            return ['status' => false, 'message' => 'Bank account không hợp lệ'];
        }

        $profileData['bank_account_id'] = $data['bank_account_id'];
        $profileData['config_data'] = json_encode([
            'bank_name' => $bankAccount->brand_name,
            'account_number' => $bankAccount->account_number,
            'account_holder' => $bankAccount->account_holder_name,
            'bank_id' => $bankAccount->bank_id
        ]);

        $profileId = model(PgProfileModel::class)->insert($profileData);

        return [
            'status' => true,
            'message' => 'Đã tạo profile Bank Account thành công',
            'profile_id' => $profileId
        ];
    }

    private function createNapasProfile($profileData, $data)
    {
        if (empty($data['napas_merchant_id'])) {
            return ['status' => false, 'message' => 'NAPAS Merchant ID là bắt buộc'];
        }

        $profileData['napas_profile_id'] = $this->createOrLinkNapasProfile($data);
        $profileData['config_data'] = json_encode([
            'merchant_id' => $data['napas_merchant_id'],
            'merchant_name' => $data['napas_merchant_name'] ?? '',
            'terminal_id' => $data['napas_terminal_id'] ?? ''
        ]);

        $profileId = model(PgProfileModel::class)->insert($profileData);

        return [
            'status' => true,
            'message' => 'Đã tạo profile NAPAS thành công',
            'profile_id' => $profileId
        ];
    }

    private function createMpgsProfile($profileData, $data)
    {
        if (empty($data['mpgs_merchant_id'])) {
            return ['status' => false, 'message' => 'MPGS Merchant ID là bắt buộc'];
        }

        $profileData['mpgs_profile_id'] = $this->createOrLinkMpgsProfile($data);
        $profileData['config_data'] = json_encode([
            'merchant_id' => $data['mpgs_merchant_id'],
            'api_username' => $data['mpgs_api_username'] ?? '',
            'api_password' => $data['mpgs_api_password'] ?? ''
        ]);

        $profileId = model(PgProfileModel::class)->insert($profileData);

        return [
            'status' => true,
            'message' => 'Đã tạo profile MPGS thành công',
            'profile_id' => $profileId
        ];
    }

    private function createOrLinkNapasProfile($data)
    {
        // Tạo hoặc link với NAPAS profile
        // Có thể tạo bảng tb_autopay_napas_profile sau
        return 1; // Placeholder
    }

    private function createOrLinkMpgsProfile($data)
    {
        $mpgsProfile = model(PgMpgsProfileModel::class)
            ->where('merchant_id', $data['mpgs_merchant_id'])
            ->first();

        if ($mpgsProfile) {
            return $mpgsProfile->id;
        }

        $mpgsData = [
            'version' => '1.0',
            'merchant_id' => $data['mpgs_merchant_id'],
            'api_username' => $data['mpgs_api_username'] ?? '',
            'api_password' => $data['mpgs_api_password'] ?? ''
        ];

        return model(PgMpgsProfileModel::class)->insert($mpgsData);
    }

    public function update()
    {
        if (! $this->request->isAJAX()) {
            return $this->response->setJSON(['status' => false, 'message' => 'Invalid request']);
        }

        $data = $this->request->getPost();

        if (! $this->validate([
            'profile_id' => 'required|numeric',
            'is_default' => 'permit_empty|in_list[0,1]',
            'active' => 'permit_empty|in_list[0,1]'
        ])) {
            return $this->response->setJSON([
                'status' => false,
                'errors' => $this->validator->getErrors()
            ]);
        }

        $profile = model(PgProfileModel::class)
            ->select('pg_profile.*, pg_payment_method.payment_method')
            ->join(
                'tb_autopay_pg_payment_method pg_payment_method',
                'pg_payment_method.id = pg_profile.pg_payment_method_id'
            )
            ->where([
                'pg_profile.id' => $data['profile_id'],
                'pg_profile.pg_merchant_id' => $this->getMerchantId()
            ])
            ->first();

        if (! $profile) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Profile không tồn tại'
            ]);
        }

        $updateData = [];
        if (isset($data['is_default'])) $updateData['default'] = $data['is_default'];
        if (isset($data['active'])) $updateData['active'] = $data['active'];

        if (empty($updateData)) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Không có dữ liệu để cập nhật'
            ]);
        }

        model(PgProfileModel::class)->update($data['profile_id'], $updateData);

        return $this->response->setJSON([
            'status' => true,
            'message' => 'Cập nhật profile thành công'
        ]);
    }

    public function delete()
    {
        if (! $this->request->isAJAX()) {
            return $this->response->setJSON(['status' => false, 'message' => 'Invalid request']);
        }

        $profileId = $this->request->getPost('profile_id');

        if (! $this->validate(['profile_id' => 'required|numeric'])) {
            return $this->response->setJSON([
                'status' => false,
                'errors' => $this->validator->getErrors()
            ]);
        }

        $profile = model(PgProfileModel::class)
            ->where([
                'id' => $profileId,
                'pg_merchant_id' => $this->getMerchantId()
            ])
            ->first();

        if (! $profile) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Profile không tồn tại'
            ]);
        }

        model(PgProfileModel::class)->delete($profileId);

        return $this->response->setJSON([
            'status' => true,
            'message' => 'Đã xóa profile thành công'
        ]);
    }

    private function getMerchantId()
    {
        $merchant = model(PgMerchantModel::class)
            ->where('company_id', $this->company_details->id)
            ->first();

        return $merchant ? $merchant->id : null;
    }
}
