<?php

namespace App\Controllers;

use App\Actions\PayCodeDetector;
use App\Models\UserModel;
use App\Models\CounterModel;
use App\Models\InvoiceModel;
use App\Models\UserLogModel;
use App\Models\WebhooksModel;
use App\Models\SmsParserModel;
use App\Models\BankAccountModel;
use App\Models\WebhooksLogModel;
use App\Models\TransactionsModel;
use App\Models\CompanySubscriptionModel;
use App\Models\NotificationTelegramModel;
use App\Models\CompanySubscriptionChangeModel;
use App\Models\NotificationTelegramQueueModel;
use App\Models\ShopModel;
use App\Models\OtherIntergationModel;
use App\Models\OtherTypeIntergationModel;

class Home extends BaseController
{
    
    public function delay_report() {
        if(!has_permission('Transactions', 'can_view_all'))
            show_404();

        $data = [
            'page_title' => 'Delay Report',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];

        $transactionsModel = slavable_model(TransactionsModel::class, 'Home');


        $data['recent_transactions'] = $transactionsModel->getRecentTransactions($this->user_session['company_id'],FALSE, date('Y-m-d 00:00:00', strtotime("5 days ago")));

        //$data['recent_transactions'] = $transactionsModel->getRecentTransactions($this->user_session['company_id'],FALSE, date('2023-06-10 00:00:00'));

        $session = session();
        //$session->setFlashdata('alert-success', 'hi');
       $session->keepFlashdata('alert-success' ,'haha'); 
        //set_alert('success', 'test kaka', true);

        echo view('templates/autopay/header',$data);
        echo view('dashboard/delayreport',$data);
        echo view('templates/autopay/footer',$data);
    }

    public function changelog() {
        $data = [
            'page_title' => 'Change Log',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];
        echo theme_view('templates/autopay/header',$data);
        echo theme_view('dashboard/changelog',$data);
        echo theme_view('templates/autopay/footer',$data);
    }

    public function other_intergation($type = '')
    {
        $data = [
            'page_title'       => 'Tích hợp',
            'user_details'     => $this->user_details,
            'company_details'  => $this->company_details,
            'user_session'     => $this->user_session
        ];

        $otherIntergationModel = model(OtherIntergationModel::class);
        $otherTypeIntergationModel = model(OtherTypeIntergationModel::class);

        $data['other_types'] = $otherTypeIntergationModel->orderBy('position', 'ASC')->findAll();

        if(!is_string($type)){
            return show_404();
        }

        if (!empty($type)) {
            $typeInfo = $otherTypeIntergationModel->where('slug', $type)->first();

            if (!$typeInfo) {
                return show_404();
            }

            $data['integations'] = $otherIntergationModel->where('id_other_type_intergation', $typeInfo->id)
                ->where('active', true)
                ->orderBy('position', 'ASC')
                ->findAll();

            $data['current_type'] = $typeInfo->id;
        } else {
            $data['integations'] = $otherIntergationModel
            ->where('active', true)
            ->orderBy('position', 'ASC')->findAll();
            $data['current_type'] = null;
        }

        echo view('templates/autopay/header', $data);
        echo view('dashboard/otherintergation', $data);
        echo view('templates/autopay/footer', $data);
    }

    public function ajax_get_other_intergation($id = '')
    {
        if(!is_numeric($id)) 
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Không tìm thấy loại tích hợp này"));

        $otherIntergationModel = model(OtherIntergationModel::class);

        $other = $otherIntergationModel->select(
            'tb_autopay_other_intergation.*, tb_autopay_other_type_intergation.name as name_type'
        )
        ->join('tb_autopay_other_type_intergation', 'tb_autopay_other_type_intergation.id = tb_autopay_other_intergation.id_other_type_intergation')
        ->where('tb_autopay_other_intergation.id', $id)
        ->first();

        if (!$other) {
            return $this->response->setJSON(['status' => false, 'message' => 'Không tìm thấy tích hợp']);
        }

        return $this->response->setJSON(['status' => true, 'data' => $other]);
    }


    public function index() {
        $data = [
            'page_title' => 'Tổng quan',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];

        $requestData = $this->request->getGet(['period', 'start_date', 'end_date']);

        if (! $this->validateData($requestData, [
            'period' => 'permit_empty|string|in_list[today,week,month,year,quarter,custom]',
            'start_date' => 'permit_empty|string|valid_date[Y-m-d]',
            'end_date' => 'permit_empty|string|valid_date[Y-m-d]',
        ])) {
            return redirect()->to(base_url());
        }
        
        if(in_array($this->company_details->role,['Admin','SuperAdmin'])) {

            $bankAccountModel = model(BankAccountModel::class);

            $data['count_bank_account'] = $bankAccountModel->where(['company_id' => $this->user_session['company_id']])->countAllResults();

            $data['period'] = $requestData['period'];

            if($data['period'] == 'custom') {
                $input_start = $requestData['start_date'];
                $input_end = $requestData['end_date'];

                if(!$input_end || !$input_start)
                    $data['period'] = 'month';
                else {
                    $start_date_obj = date_create_from_format('Y-m-d', $input_start);
                    $end_date_obj = date_create_from_format('Y-m-d', $input_end);

                    if(is_object($start_date_obj) && is_object($end_date_obj)) {
                        $start_date = $start_date_obj->format("Y-m-d 00:00:00");
                        $end_date = $end_date_obj->format("Y-m-d 23:59:59");
                        $data['period_text'] = '';
                        $data['period_days'] = round( (strtotime($end_date) - strtotime($start_date)) / (60 * 60 * 24) );
                        if(strtotime($start_date) > strtotime($end_date))
                            $data['period'] = 'month';
                    
                    } else
                        $data['period'] = 'month';
                
                }

            }
        

            if(!in_array($data['period'], ['today','month','year','quarter','week','custom']))
                $data['period'] = 'month';

            if($data['period'] == 'month') {
                $start_date = date("Y-m-01 00:00:00");
                $data['period_text'] = 'tháng này';
            $end_date = date('Y-m-d 23:59:59');
            } else if($data['period'] == 'today') {
                $start_date = date("Y-m-d 00:00:00");
                $data['period_text'] = 'hôm nay';
                $end_date = date('Y-m-d 23:59:59');
            } else if($data['period'] == 'week') {
                $start_date = date('Y-m-d 00:00:00', strtotime('monday this week'));
                $data['period_text'] = 'tuần này';
            $end_date = date('Y-m-d 23:59:59');
            } else if($data['period'] == 'quarter') {
                $start_date = firstDayOf('quarter')->format("Y-m-d 00:00:00");
                $data['period_text'] = 'quý này';
            $end_date = date('Y-m-d 23:59:59');
            } else if($data['period'] == 'year') {
                $start_date = date("Y-01-01 00:00:00");
                $data['period_text'] = 'năm này';
                
                $end_date = date('Y-m-d 23:59:59');
            }
            

            $data['period_days'] = round( (strtotime($end_date) - strtotime($start_date)) / (60 * 60 * 24) );

            $data['start_date'] = $start_date;
            $data['end_date'] = $end_date;

            $data['end_date_period_ago'] = date("Y-m-d 23:59:59",  strtotime($start_date . ' - 1 day'));
            $data['start_date_period_ago'] = date("Y-m-d 00:00:00",  strtotime($start_date . ' - '.$data['period_days'].' day'));

    
            $transactionsModel = slavable_model(TransactionsModel::class, 'Home');
            $webHooksModel = model(WebHooksModel::class);
            $webHooksLogModel = model(WebHooksLogModel::class);
            $notificationTelegramModel = model(NotificationTelegramModel::class);
            $notificationTelegramQueueModel = model(NotificationTelegramQueueModel::class);
            $counterModel = model(CounterModel::class);
            $bankAccountCashflowModel = model(BankAccountCashflowModel::class);
            $bankSubAccountCashflowModel = model(BankSubAccountCashflowModel::class);
            $bankModel = model(BankModel::class);

            $data['recent_transactions'] = [];

            $results = $transactionsModel->getRecentTransactions($this->user_session['company_id'],4);

            foreach($results as $result) {
                $bank_details = $bankModel->where(['id' => $result->bank_id])->get()->getRow();
                if(is_object($bank_details))
                    array_push($data['recent_transactions'], ['transaction_details' => $result, 'bank_details' => $bank_details]);
            }

            //$data['recent_transactions'] = $transactionsModel->getRecentTransactions($this->user_session['company_id'],4);
            $data['bank_accounts'] = $bankAccountModel->select("tb_autopay_bank_account.id,tb_autopay_bank.short_name, tb_autopay_bank.brand_name, tb_autopay_bank.logo_path,tb_autopay_bank.icon_path, tb_autopay_bank_account.account_number, tb_autopay_bank_account.label, tb_autopay_bank_account.account_holder_name,tb_autopay_bank_account.accumulated,tb_autopay_bank_account.last_transaction")->join("tb_autopay_bank","tb_autopay_bank.id=tb_autopay_bank_account.bank_id")->where(['tb_autopay_bank_account.company_id' => $this->user_session['company_id']])->orderBy('tb_autopay_bank_account.id', 'asc')->findAll();

            $data['accumulated'] = [];

            $data['total_balanced'] = 0;

            foreach($data['bank_accounts'] as $bank_account) {

                if($bank_account->accumulated > 0) {
                    $data['accumulated'][$bank_account->id] = $bank_account->accumulated;
                    $data['total_balanced'] = $data['total_balanced'] + $bank_account->accumulated;

                }
                    
                //$data['income_today'][$bank_account->id] = $transactionsModel->sum_income_by_account_number($this->user_session['company_id'],$bank_account->id,"in")->sum;
                $result = $bankAccountCashflowModel->select("sum(total_amount_in) as `income_today`")->where(['bank_account_id'=> $bank_account->id, 'date' => date('Y-m-d')])->get()->getRow();

                $data['income_today'][$bank_account->id] = $result->income_today;

    
            }

            /*
            $data['expense_all'] =  $transactionsModel->sum_income_by_account_number($this->user_session['company_id'],FALSE,"out",$start_date, $end_date)->sum;
            $data['income_all'] =  $transactionsModel->sum_income_by_account_number($this->user_session['company_id'],FALSE,"in",$start_date, $end_date)->sum;

            $data['balance_all'] = $data['income_all'] - $data['expense_all'];
            */

            $result = $bankAccountCashflowModel->select("sum(total_amount_in) as `total_amount_in`, sum(total_amount_out) as `total_amount_out`")->where(['company_id'=> $this->user_session['company_id'], 'date>=' => date('Y-m-d', strtotime($start_date)), 'date<=' => date('Y-m-d', strtotime($end_date))])->get()->getRow();

 
            $data['expense_all'] = $result->total_amount_out;
            $data['income_all'] = $result->total_amount_in;
            $data['balance_all'] = $data['income_all'] - $data['expense_all'];

            /*
            $expense_all_period_ago =  $transactionsModel->sum_income_by_account_number($this->user_session['company_id'],FALSE,"out",$data['start_date_period_ago'], $data['end_date_period_ago'])->sum;
            $income_all_period_ago =  $transactionsModel->sum_income_by_account_number($this->user_session['company_id'],FALSE,"in",$data['start_date_period_ago'], $data['end_date_period_ago'])->sum;

            $balance_all_period_ago = $income_all_period_ago - $expense_all_period_ago;
            */
 
            $result = $bankAccountCashflowModel->select("sum(total_amount_in) as `total_amount_in`, sum(total_amount_out) as `total_amount_out`")->where(['company_id'=> $this->user_session['company_id'], 'date>=' => date('Y-m-d', strtotime($data['start_date_period_ago'])), 'date<=' => date('Y-m-d', strtotime($data['end_date_period_ago']))])->get()->getRow();
            $expense_all_period_ago = $result->total_amount_out;
            $income_all_period_ago = $result->total_amount_in;


            $balance_all_period_ago = $income_all_period_ago - $expense_all_period_ago;


            $data['compare_income_period_ago'] = 0;
            $data['compare_expense_period_ago'] = 0;
            $data['compare_balance_period_ago'] = 0;
            if($income_all_period_ago > 0)
                $data['compare_income_period_ago'] = round((($data['income_all'] - $income_all_period_ago)/$income_all_period_ago)*100);

            if($expense_all_period_ago > 0)
                $data['compare_expense_period_ago'] = round((($data['expense_all'] - $expense_all_period_ago)/$expense_all_period_ago)*100);

            if($balance_all_period_ago != 0)
                $data['compare_balance_period_ago'] = round((($data['balance_all'] - $balance_all_period_ago)/$balance_all_period_ago)*100);

        
            /*$data['count_transactions'] = $transactionsModel->join("tb_autopay_bank_account","tb_autopay_bank_account.account_number=tb_autopay_sms_parsed.account_number")->where(['tb_autopay_bank_account.company_id' => $this->user_session['company_id'], 'transaction_date>=' =>$start_date, 'transaction_date<=' => $end_date])->countAllResults();*/
            $result = $counterModel->select("sum(transaction) as `sum_transaction`,sum(transaction_in) as `sum_transaction_in`,sum(transaction_out) as `sum_transaction_out`,sum(chat) as `sum_chat`,sum(webhook) as `sum_webhook`,sum(webhooks_pay_success) as `sum_webhooks_pay_success`,sum(webhook_success) as `sum_webhook_success`,sum(webhook_failed) as `sum_webhook_failed`")->where(['company_id' => $this->user_session['company_id'], 'date>=' => $start_date, 'date<=' => $end_date])->get()->getRow();

            if(is_object($result)) {
                $data['count_transactions'] = $result->sum_transaction;
                $data['count_transactions_in'] = $result->sum_transaction_in;
                $data['count_transactions_out'] = $result->sum_transaction_out;
                $data['telegram_sents']  = $result->sum_chat;
                $data['webhooks_sents'] = $result->sum_webhook;
                $data['webhooks_sents_success'] = $result->sum_webhook_success;
                $data['webhooks_sents_failed'] = $result->sum_webhook_failed;

            }
            else {
                $data['count_transactions'] = 0;
                $data['count_transactions_in'] = 0;
                $data['count_transactions_out'] = 0;
                $data['telegram_sents']  = 0;
                $data['webhooks_sents'] = 0;
                $data['webhooks_sents_success'] = 0;
                $data['webhooks_sents_failed'] = 0;

            }
    
            if($this->channel_partner)
            {
                $shopModel = model(ShopModel::class);
                $data['count_shop'] = $shopModel->where(['company_id' => $this->user_session['company_id']])->countAllResults();
                $result_shop = $this->getCompanyUserShopStatistics($start_date,$end_date);
                $data['revenue'] = $result_shop->revenue;
                $data['count_transactions_shop'] = $result_shop->transaction_count;
                $data['telegram_sents_shop'] = $result_shop->telegram_sents;
                $data['top_sub_accounts_income_shop'] = $this->getRecentTransactions(4,  true, $start_date, $end_date);
                $data['recent_transactions_shop'] = $this->getRecentTransactions(4);

                $builder = slavable_model(TransactionsModel::class, 'Home')
                ->select([
                    'SUM(tb_autopay_sms_parsed.amount_in) as sum_in',
                    'SUM(tb_autopay_sms_parsed.amount_out) as sum_out',
                    'MONTH(tb_autopay_sms_parsed.transaction_date) as transaction_month'
                ])
                ->join('tb_autopay_bank_account', 'tb_autopay_bank_account.id = tb_autopay_sms_parsed.bank_account_id')
                ->join('tb_autopay_bank_sub_account', 'tb_autopay_bank_sub_account.sub_account = tb_autopay_sms_parsed.sub_account')
                ->join('tb_autopay_bank_shop_link', "tb_autopay_bank_shop_link.bank_account_id = tb_autopay_sms_parsed.bank_account_id AND tb_autopay_bank_shop_link.bank_sub_account_id = tb_autopay_bank_sub_account.id")
                ->join('tb_autopay_shop', 'tb_autopay_bank_shop_link.shop_id = tb_autopay_shop.id')
                ->where('tb_autopay_bank_account.company_id', $this->user_session['company_id'])
                ->where('tb_autopay_sms_parsed.parser_status', 'Success');

                if ($start_date && $end_date) {
                    $builder->where([
                        'tb_autopay_sms_parsed.transaction_date >=' => $start_date,
                        'tb_autopay_sms_parsed.transaction_date <=' => $end_date
                    ]);
                }
                $transactions = $builder->get()->getResult();
                
                for ($i = 1; $i <= 12; $i++) {
                    $data['monthly_cashflow_shop'][$i]['in'] = 0;
                    $data['monthly_cashflow_shop'][$i]['out'] = 0;
                    $data['monthly_cashflow_shop'][$i]['balance'] = 0;
                    foreach ($transactions as $transaction) {
                        if (isset($transaction->transaction_month) && $transaction->transaction_month == $i) {
                            $data['monthly_cashflow_shop'][$i]['in'] = $transaction->sum_in;
                            $data['monthly_cashflow_shop'][$i]['out'] = $transaction->sum_out;
                            $data['monthly_cashflow_shop'][$i]['balance'] = $transaction->sum_in - $transaction->sum_out;
                        }
                    }
                }
            }
            
           // $data['top_sub_accounts_income'] = $transactionsModel->select("sum(tb_autopay_sms_parsed.amount_in) as `income`, tb_autopay_sms_parsed.sub_account, count(tb_autopay_sms_parsed.id) as `total`")->join("tb_autopay_bank_account","tb_autopay_sms_parsed.bank_account_id=tb_autopay_bank_account.id")->where(['tb_autopay_bank_account.company_id' => $this->user_session['company_id'],'tb_autopay_sms_parsed.amount_in>' =>0, "sub_account!=" => NULL, "tb_autopay_sms_parsed.transaction_date>=" => $start_date, "tb_autopay_sms_parsed.transaction_date<=" => $end_date, 'tb_autopay_sms_parsed.deleted_at' => NULL])->groupBy("tb_autopay_sms_parsed.sub_account")->orderBy('income','DESC')->limit(5)->get()->getResult();

            $data['top_sub_accounts_income'] = $bankSubAccountCashflowModel->select("sum(total_amount_in) as `income`,sub_account")->where(['company_id'=> $this->user_session['company_id'], 'date>=' => date('Y-m-d', strtotime($start_date)), 'date<=' => date('Y-m-d', strtotime($end_date))])->groupBy("sub_account")->orderBy('income','DESC')->limit(5)->get()->getResult();


            /*
            $transactions = $transactionsModel->select("tb_autopay_sms_parsed.transaction_date, tb_autopay_sms_parsed.datecreated")->join("tb_autopay_bank_account","tb_autopay_bank_account.id=tb_autopay_sms_parsed.bank_account_id")->where(['tb_autopay_bank_account.company_id' => $this->user_session['company_id'], 'transaction_date>=' =>$start_date, 'transaction_date<=' => $end_date, 'tb_autopay_sms_parsed.deleted_at' => NULL])->get()->getResult();

            $total_delay = 0;
            $i = 0;
            foreach($transactions as $transaction) {
                $i = $i + 1;
                $delay = strtotime($transaction->datecreated) - strtotime($transaction->transaction_date);
                $total_delay = $total_delay + $delay;
            } 
            if($i > 0)
                $data['avg_delay'] = $total_delay/$i;
            else
                $data['avg_delay'] = 0;

            */
            
            $data['avg_delay'] = 0;


            // chartjs cash flow in this year
            /*
            $transactions = $transactionsModel->select("month(transaction_date) as `m_data`,sum(amount_in) as `sum_in`, sum(amount_out) as `sum_out`")->join("tb_autopay_bank_account","tb_autopay_bank_account.id=tb_autopay_sms_parsed.bank_account_id")->where(['tb_autopay_bank_account.company_id' => $this->user_session['company_id'], 'year(transaction_date)>=' =>date("Y"), 'tb_autopay_sms_parsed.deleted_at' => NULL])->groupBy('m_data')->get()->getResult();
            
            $data['monthly_cashflow'] = [];
            for($i = 1; $i<=12; $i++) {
                $data['monthly_cashflow'][$i]['in'] = 0;
                $data['monthly_cashflow'][$i]['out'] = 0;
                $data['monthly_cashflow'][$i]['balance'] = 0;
                foreach($transactions as $transaction) {
                    
                    if(isset($transaction->m_data) && $transaction->m_data == $i) {
                        $data['monthly_cashflow'][$i]['in'] = $transaction->sum_in;
                        $data['monthly_cashflow'][$i]['out'] = $transaction->sum_out;
                        $data['monthly_cashflow'][$i]['balance'] = $transaction->sum_in - $transaction->sum_out;
                    }

                }
            } 
            */

            // chartjs cash flow in this year
            $transactions = $bankAccountCashflowModel->select("month(date) as `m_data`, sum(total_amount_in) as `sum_in`,  sum(total_amount_out) as `sum_out`")->where(['company_id' => $this->user_session['company_id'], 'year(date)' =>date("Y")])->groupBy('m_data')->get()->getResult(); 
            for($i = 1; $i<=12; $i++) {
                $data['monthly_cashflow'][$i]['in'] = 0;
                $data['monthly_cashflow'][$i]['out'] = 0;
                $data['monthly_cashflow'][$i]['balance'] = 0;
                foreach($transactions as $transaction) {
                    
                    if(isset($transaction->m_data) && $transaction->m_data == $i) {
                        $data['monthly_cashflow'][$i]['in'] = $transaction->sum_in;
                        $data['monthly_cashflow'][$i]['out'] = $transaction->sum_out;
                        $data['monthly_cashflow'][$i]['balance'] = $transaction->sum_in - $transaction->sum_out;
                    }

                }
            } 

            $invoiceModel = model(InvoiceModel::class);
            $data['unpaid_invoice'] = $invoiceModel
                ->select(['id', 'type'])
                ->where(['company_id' => $this->user_session['company_id'], 'status' => 'Unpaid'])
                ->orderBy('id', 'asc')
                ->first();

            $companySubscriptionChangeModel = model(CompanySubscriptionChangeModel::class);
            $data['subscription_change_invoice_details'] = $companySubscriptionChangeModel
                ->join('tb_autopay_order', 'tb_autopay_order.id=tb_autopay_company_subscription_change.order_id')
                ->join('tb_autopay_product', 'tb_autopay_product.id=tb_autopay_company_subscription_change.plan_id')
                ->where(['tb_autopay_company_subscription_change.company_id' => $this->user_session['company_id']])->get()->getRow();

            $session = session();
            $hideUnpaidInvoiceAlertId = $session->get('hide_unpaid_invoice_alert');
            $data['hide_unpaid_invoice_alert'] = false;

            if ($data['unpaid_invoice']) {
                $data['hide_unpaid_invoice_alert'] = $data['unpaid_invoice']->id == $hideUnpaidInvoiceAlertId;
            }

            $template_name = 'admin_v2';
        } else
            $template_name = 'user_v2';
        echo theme_view('templates/autopay/header',$data);
        echo theme_view('dashboard/' . $template_name,$data);
        echo theme_view('templates/autopay/footer',$data);
    } 


    protected function getCompanyUserShopStatistics($startDate = null, $endDate = null)
    {
        $builder = slavable_model(TransactionsModel::class, 'Home')
            ->select([
                'SUM(tb_autopay_sms_parsed.amount_in) as revenue',
                'COUNT(tb_autopay_sms_parsed.id) as transaction_count',
                'SUM(tb_autopay_sms_parsed.chat_push_message) as telegram_sents'
            ])
            ->join('tb_autopay_bank_account', 'tb_autopay_bank_account.id = tb_autopay_sms_parsed.bank_account_id')
            ->join('tb_autopay_bank_sub_account', 'tb_autopay_bank_sub_account.sub_account = tb_autopay_sms_parsed.sub_account')
            ->join('tb_autopay_bank_shop_link', "tb_autopay_bank_shop_link.bank_account_id = tb_autopay_sms_parsed.bank_account_id AND tb_autopay_bank_shop_link.bank_sub_account_id = tb_autopay_bank_sub_account.id")
            ->join('tb_autopay_shop', 'tb_autopay_bank_shop_link.shop_id = tb_autopay_shop.id')
            ->where('tb_autopay_bank_account.company_id', $this->user_session['company_id'])
            ->where('tb_autopay_sms_parsed.parser_status', 'Success');

        if ($startDate && $endDate) {
            $builder->where([
                'tb_autopay_sms_parsed.transaction_date >=' => $startDate,
                'tb_autopay_sms_parsed.transaction_date <=' => $endDate
            ]);
        }

        return $builder->get()->getRow();
    }

    protected function getRecentTransactions($limit = 10, $top = null,$startDate = null, $endDate = null)
    {
        $builder = slavable_model(TransactionsModel::class, 'Home')
            ->select([
                'tb_autopay_shop.name',
                'tb_autopay_sms_parsed.id',
                'tb_autopay_sms_parsed.sub_account',
                'tb_autopay_bank_sub_account.label as bank_sub_account_label',
                'tb_autopay_sms_parsed.amount_in',
                'tb_autopay_sms_parsed.amount_out',
                'tb_autopay_sms_parsed.currency',
                'tb_autopay_sms_parsed.transaction_date',
                'tb_autopay_bank_shop_link.shop_id',
                'tb_autopay_sms_parsed.bank_account_id',
                'tb_autopay_bank_shop_link.bank_sub_account_id',
                'tb_autopay_sms_parsed.transaction_content',
                'tb_autopay_bank_account.account_number',
                'tb_autopay_sms_parsed.chat_push_message'
            ])
            ->join('tb_autopay_bank_account', 'tb_autopay_bank_account.id = tb_autopay_sms_parsed.bank_account_id')
            ->join('tb_autopay_bank_sub_account', 'tb_autopay_bank_sub_account.sub_account = tb_autopay_sms_parsed.sub_account')
            ->join('tb_autopay_bank_shop_link', 'tb_autopay_bank_shop_link.bank_account_id = tb_autopay_sms_parsed.bank_account_id AND tb_autopay_bank_shop_link.bank_sub_account_id = tb_autopay_bank_sub_account.id')
            ->join('tb_autopay_shop', 'tb_autopay_shop.id = tb_autopay_bank_shop_link.shop_id')
            ->where('tb_autopay_bank_account.company_id', $this->user_session['company_id'])
            ->where('tb_autopay_sms_parsed.parser_status', 'Success');

            if ($startDate && $endDate) {
                $builder->where([
                    'tb_autopay_sms_parsed.transaction_date >=' => $startDate,
                    'tb_autopay_sms_parsed.transaction_date <=' => $endDate
                ]);
            }

            if($top){
                $builder->where('tb_autopay_sms_parsed.amount_in >', 0);
                $builder->orderBy('tb_autopay_sms_parsed.amount_in', 'DESC');
            }

            $builder->orderBy('tb_autopay_sms_parsed.transaction_date', 'DESC');

        return $builder->get($limit)->getResult();
    }

}
