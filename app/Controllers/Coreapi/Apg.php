<?php

namespace App\Controllers\Coreapi;

use App\Config\Invoice;
use App\Models\InvoiceModel;
use CodeIgniter\API\ResponseTrait;
use CodeIgniter\Controller;
use Config\NapasConfig;
use Config\Services;

class Apg extends Controller
{
    use ResponseTrait;

    /** @var NapasConfig */
    protected $napasConfig;

    public function __construct()
    {
        $this->napasConfig = config(NapasConfig::class);
    }
    
    public function token()
    {
        if ($this->request->getMethod(true) !== 'POST') {
            return $this->respond([
                'message' => 'failure',
                'description' => 'Method not allowed'
            ], 405);
        }
        
        if (stripos($this->request->getHeaderLine('Content-Type'), 'application/x-www-form-urlencoded') === false) {
            return $this->respond([
                'message' => 'failure',
                'description' => 'Content-Type must be application/x-www-form-urlencoded'
            ], 415);
        }
        
        $clientId = $this->request->getPost('client_id');
        $clientSecret = $this->request->getPost('client_secret');
        
        /** @var NapasConfig $napasConfig */
        $napasConfig = config(\Config\NapasConfig::class);

        if (!$clientId || !$clientSecret || $clientId !== $napasConfig->notifyAuthClientId || $clientSecret !== $napasConfig->notifyAuthClientSecret) {
            return $this->respond([
                'message' => 'failure',
                'description' => 'Invalid client ID/client secret'
            ], 400);
        }
        
        $ttl = $this->napasConfig->notifyTokenTtl;

        $payload = [
            'iss' => base_url(),
            'aud' => base_url('coreapi/apg'),
            'iat' => time(),
            'exp' => time() + $ttl,
            'client_id' => $clientId
        ];

        $header = [
            'alg' => 'HS256',
            'typ' => 'JWT'
        ];

        $base64UrlHeader = rtrim(strtr(base64_encode(json_encode($header)), '+/', '-_'), '=');
        $base64UrlPayload = rtrim(strtr(base64_encode(json_encode($payload)), '+/', '-_'), '=');

        $signature = hash_hmac('sha256', $base64UrlHeader . "." . $base64UrlPayload, $clientSecret, true);
        $base64UrlSignature = rtrim(strtr(base64_encode($signature), '+/', '-_'), '=');

        $jwt = $base64UrlHeader . "." . $base64UrlPayload . "." . $base64UrlSignature;

        return $this->respond([
            'token_type' => 'Bearer',
            'access_token' => $jwt,
            'expires_in' => $ttl
        ]);
    }
    
    public function notification()
    {
        if ($this->request->getMethod(true) !== 'POST') {
            return $this->respond([
                'code' => '405',
                'message' => 'Method not allowed'
            ], 405);
        }

        log_message('error', '[NAPAS_NOTIFICATION] BODY: ' . json_encode($this->request->getJSON()));
        
        if (!$this->authorizeToken()) {
            return $this->respond([
                'code' => '401',
                'message' => 'Token invalid or expired'
            ], 401);
        }
        
        if (!$this->validateSignature()) {
            return $this->respond([
                'code' => '300',
                'message' => 'Signature is invalid'
            ], 400);
        }

        $header = $this->request->getJsonVar('header', true);
        $payload = $this->request->getJsonVar('payload', true);
        
        $headerRules = [
            'messageIdentifier' => 'required|string',
            'senderReference' => 'required|string',
            'creationDateTime' => 'required|valid_date[Y-m-d\TH:i:sP]',
            'senderId' => 'required|numeric|exact_length[6]',
            'receiverId' => 'required|numeric|exact_length[6]',
            'signature' => 'required|string',
        ];

        $rules = [
            'status' => 'required|alpha',
            'amount' => 'required|numeric',
            'transDateTime' => 'required|valid_date[Y-m-d\TH:i:sP]',
            'id' => 'required|string',
            'issueBank' => 'required|numeric|exact_length[6]',
            'beneficiaryBank' => 'required|numeric|exact_length[6]',
            'realMerchantAccount' => 'required|string',
            'mcc' => 'required|numeric|exact_length[4]',
            'sourceAccount' => 'required|string',
            'systemTrace' => 'required|string',
            'localTime' => 'required|string|exact_length[6]',
            'localDate' => 'required|string|exact_length[4]',
            'terminalId' => 'required|string',
            'refId' => 'required|string',
            'caseId' => 'required|string',
            'creationDateTime' => 'required|valid_date[Y-m-d\TH:i:sP]',
        ];
        
        $headerValidation = \Config\Services::validation();
        $headerValidation->setRules($headerRules);
        
        if (!$headerValidation->run($header)) {
            return $this->respond([
                'code' => '400',
                'message' => 'Invalid header format'
            ], 400);
        }

        $validation = \Config\Services::validation();
        $validation->setRules($rules);

        if (!$validation->run($payload)) {
            return $this->respond([
                'code' => '400',
                'message' => 'Invalid payload format'
            ], 400);
        }

        if ($payload['status'] !== 'ACSP') {
            return $this->respond([
                'code' => '400',
                'message' => 'Transaction failed with status: ' . $payload['status']
            ], 400);
        }

        $orderId = ltrim(substr($payload['id'], -7), '0');

        $invoiceModel = model(InvoiceModel::class);
        $invoice = $invoiceModel->where('id', $orderId)->where('status', 'Unpaid')->where('total', $payload['amount'])->first();

        if (!$invoice) {
            return $this->respond([
                'code' => '404',
                'message' => 'Order not found'
            ], 404);
        }

        log_message('error', '[NAPAS] Successfully processed notification');

        if (getenv('SEPAY_ENV') == 'STAGING') {
            Services::curlrequest()->post(getenv('CI_ENVIRONMENT') == 'development' ? 'http://localhost:8081/api/payment/add' : 'https://ad.staging.sepay.vn/api/payment/add', [
                'json' => [
                    'gateway' => 'MBBank',
                    'transactionDate' => date('Y-m-d H:i:s', strtotime($payload['transDateTime'])),
                    'accountNumber' => '*************',
                    'code' => ltrim(substr($payload['id'], -7), '0'),
                    'content' => 'Chuyen khoan tu Napas',
                    'transferType' => 'in',
                    'transferAmount' => $payload['amount'],
                    'description' => ltrim(substr($payload['id'], -7), '0'),
                ]
            ]);
        }

        return $this->respond([
            'code' => 'success',
            'message' => 'Message is successful'
        ]);
    }
    
    public function reconciliation()
    {
        if ($this->request->getMethod(true) !== 'POST') {
            return $this->respond([
                'code' => '405',
                'message' => 'Method not allowed'
            ], 405);
        }
   
        log_message('error', '[NAPAS_RECONCILIATION] BODY: ' . json_encode($this->request->getJSON()));

        if (!$this->authorizeToken()) {
            return $this->respond([
                'code' => '401',
                'message' => 'Token invalid or expired'
            ], 401);
        }
        
        if (!$this->validateSignature()) {
            return $this->respond([
                'code' => '300',
                'message' => 'Signature is invalid'
            ], 400);
        }
        
        return $this->respond([
            'code' => 'success',
            'message' => 'Message is successfully'
        ], 200);
    }
    
    protected function authorizeToken(): bool
    {
        $authHeader = $this->request->getHeaderLine('Authorization');
        if (!$authHeader || strpos($authHeader, 'Bearer ') !== 0) {
            return false;
        }

        $jwt = substr($authHeader, 7);
        $parts = explode('.', $jwt);

        if (count($parts) !== 3) {
            return false;
        }

        [$base64UrlHeader, $base64UrlPayload, $base64UrlSignature] = $parts;

        $payload = json_decode(base64_decode(strtr($base64UrlPayload, '-_', '+/')), true);

        if (!$payload || !isset($payload['exp'], $payload['client_id']) || time() > $payload['exp']) {
            return false;
        }

        if ($payload['client_id'] !== $this->napasConfig->notifyAuthClientId) {
            return false;
        }

        $signature = hash_hmac('sha256', $base64UrlHeader . "." . $base64UrlPayload, $this->napasConfig->notifyAuthClientSecret, true);
        $expectedSignature = rtrim(strtr(base64_encode($signature), '+/', '-_'), '=');

        return hash_equals($expectedSignature, $base64UrlSignature);
    }
    
    protected function validateSignature(): bool
    {
        $signatureHeader = $this->request->getJsonVar('header')->signature ?? null;
        $publicKey = file_get_contents($this->napasConfig->notifyNapasCertPath);
        $data = json_encode($this->request->getJsonVar('payload'), JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);

        if (!$signatureHeader || !$publicKey || !$data) {
            return false;
        }
        
        try {
            $rsa = openssl_pkey_get_public($publicKey);

            if (!$rsa) {
                return false;
            }

            $isValid = openssl_verify($data, base64_decode($signatureHeader), $rsa, OPENSSL_ALGO_SHA256);

            return $isValid === 1;
        } catch (\Exception $e) {
            return false;
        }
    }
}
