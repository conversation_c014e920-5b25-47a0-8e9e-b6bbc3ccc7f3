<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Models\PgMerchantModel;
use App\Models\PgPaymentMethodModel;
use App\Models\PgProfileModel;
use App\Models\BankAccountModel;
use App\Models\OnboardingSubmissionModel;
use App\Models\NapasVietQrProfileModel;
use App\Enums\PgPaymentMethod;
use App\Enums\PgProfileType;

class Paymentmethods extends BaseController
{
    public function index()
    {
        $merchant = model(PgMerchantModel::class)
            ->where('company_id', $this->company_details->id)
            ->first();

        $data = [
            'page_title' => 'Phương thức thanh toán',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'merchant' => $merchant,
            'payment_methods' => $this->getPaymentMethods($merchant->id ?? null),
            'bank_accounts' => $this->getBankAccounts(),
            'onboarding_status' => $this->getOnboardingStatus(),
            'features' => $this->getPaymentFeatures(),
            'payment_method_exists' => $this->getPaymentMethodExistence($merchant->id ?? null)
        ];

        return view('payment-methods/index', $data);
    }

    public function banktransfer()
    {
        $merchant = model(PgMerchantModel::class)
            ->where('company_id', $this->company_details->id)
            ->first();

        if (! $merchant) {
            return redirect()->to('paymentmethods')->with('error', 'Merchant chưa được tạo');
        }

        $paymentMethod = model(PgPaymentMethodModel::class)
            ->where([
                'pg_merchant_id' => $merchant->id,
                'payment_method' => PgPaymentMethod::BANK_TRANSFER
            ])
            ->where('company_id', $this->company_details->id)
            ->first();

        if (! $paymentMethod) {
            return redirect()->to('paymentmethods')->with('error', 'Phương thức thanh toán chuyển khoản ngân hàng chưa được tạo. Vui lòng kích hoạt trước.');
        }

        $data = [
            'page_title' => 'Chuyển khoản ngân hàng',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'merchant' => $merchant,
            'payment_method' => $paymentMethod,
            'bankProfiles' => $this->getBankProfiles($merchant->id, $paymentMethod->id ?? null),
            'available_bank_accounts' => $this->getAvailableBankAccounts()
        ];

        return view('payment-methods/bank-transfer', $data);
    }

    public function setDefaultBankProfile()
    {
        if (! $this->request->isAJAX()) {
            return $this->response->setJSON(['status' => false, 'message' => 'Invalid request']);
        }

        $data = $this->request->getPost();

        if (! $this->validate([
            'profile_id' => 'required|numeric',
        ])) {
            return $this->response->setJSON([
                'status' => false,
                'errors' => $this->validator->getErrors()
            ]);
        }

        $merchant = model(PgMerchantModel::class)
            ->where('company_id', $this->company_details->id)
            ->first();

        if (! $merchant) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Merchant chưa được tạo',
            ]);
        }

        $profile = model(PgProfileModel::class)
            ->where([
                'id' => $data['profile_id'],
                'pg_merchant_id' => $merchant->id,
                'active' => 1,
                'company_id' => $this->company_details->id,
            ])
            ->whereIn('type', [PgProfileType::BANK_ACCOUNT, PgProfileType::NAPAS_VIETQR])
            ->first();

        if (! $profile) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Tài khoản thụ hưởng không tồn tại',
            ]);
        }

        model(PgProfileModel::class)
            ->where([
                'pg_merchant_id' => $merchant->id,
                'pg_payment_method_id' => $profile->pg_payment_method_id,
                'company_id' => $this->company_details->id,
            ])
            ->whereIn('type', [PgProfileType::BANK_ACCOUNT, PgProfileType::NAPAS_VIETQR])
            ->set(['default' => 0])
            ->update();

        model(PgProfileModel::class)->update($profile->id, ['default' => true]);

        return $this->response->setJSON([
            'status' => true,
            'message' => 'Đã cập nhật tài khoản thụ hưởng mặc định thành công',
        ]);
    }

    public function addBankProfile()
    {
        if (! $this->request->isAJAX()) {
            return $this->response->setJSON(['status' => false, 'message' => 'Invalid request']);
        }

        $data = $this->request->getPost();

        if (! $this->validate([
            'bank_account_id' => 'required|numeric',
            'is_default' => 'permit_empty|in_list[0,1]'
        ])) {
            return $this->response->setJSON([
                'status' => false,
                'errors' => $this->validator->getErrors()
            ]);
        }

        $merchant = model(PgMerchantModel::class)
            ->where('company_id', $this->company_details->id)
            ->first();

        if (! $merchant) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Merchant chưa được tạo'
            ]);
        }

        $paymentMethod = model(PgPaymentMethodModel::class)
            ->where([
                'pg_merchant_id' => $merchant->id,
                'payment_method' => PgPaymentMethod::BANK_TRANSFER,
            ])
            ->where('company_id', $this->company_details->id)
            ->first();

        if (! $paymentMethod) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Phương thức thanh toán chuyển khoản chưa được kích hoạt',
            ]);
        }

        $bankAccount = model(BankAccountModel::class)
            ->where([
                'id' => $data['bank_account_id'],
                'company_id' => $this->company_details->id,
                'active' => 1,
            ])
            ->first();

        if (! $bankAccount) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Tài khoản ngân hàng không hợp lệ',
            ]);
        }

        $existingProfile = model(PgProfileModel::class)
            ->where([
                'pg_merchant_id' => $merchant->id,
                'pg_payment_method_id' => $paymentMethod->id,
                'bank_account_id' => $data['bank_account_id'],
                'type' => PgProfileType::BANK_ACCOUNT,
                'company_id' => $this->company_details->id,
            ])
            ->first();

        if ($existingProfile) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Tài khoản ngân hàng này đã được thêm vào tài khoản thụ hưởng'
            ]);
        }

        $isDefault = isset($data['is_default']) && $data['is_default'] == '1';

        if ($isDefault) {
            model(PgProfileModel::class)
                ->where([
                    'pg_merchant_id' => $merchant->id,
                    'pg_payment_method_id' => $paymentMethod->id,
                    'company_id' => $this->company_details->id,
                ])
                ->whereIn('type', [PgProfileType::BANK_ACCOUNT, PgProfileType::NAPAS_VIETQR])
                ->set(['default' => 0])
                ->update();
        }

        $profileId = model(PgProfileModel::class)->insert([
            'pg_merchant_id' => $merchant->id,
            'pg_payment_method_id' => $paymentMethod->id,
            'type' => PgProfileType::BANK_ACCOUNT,
            'bank_account_id' => $data['bank_account_id'],
            'default' => (bool) $isDefault,
            'active' => true,
            'company_id' => $this->company_details->id,
        ]);

        return $this->response->setJSON([
            'status' => true,
            'message' => 'Đã thêm tài khoản thụ hưởng thành công',
            'profile_id' => $profileId
        ]);
    }

    public function removeBankProfile()
    {
        if (! $this->request->isAJAX()) {
            return $this->response->setJSON(['status' => false, 'message' => 'Invalid request']);
        }

        $data = $this->request->getPost();

        if (! $this->validate([
            'profile_id' => 'required|numeric'
        ])) {
            return $this->response->setJSON([
                'status' => false,
                'errors' => $this->validator->getErrors()
            ]);
        }

        $merchant = model(PgMerchantModel::class)
            ->where('company_id', $this->company_details->id)
            ->first();

        if (! $merchant) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Merchant chưa được tạo'
            ]);
        }

        $profile = model(PgProfileModel::class)
            ->where([
                'id' => $data['profile_id'],
                'pg_merchant_id' => $merchant->id,
                'company_id' => $this->company_details->id,
            ])
            ->whereIn('type', [PgProfileType::BANK_ACCOUNT, PgProfileType::NAPAS_VIETQR])
            ->first();

        if (! $profile) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Tài khoản thụ hưởng không tồn tại'
            ]);
        }

        $profileCount = model(PgProfileModel::class)
            ->where([
                'pg_merchant_id' => $merchant->id,
                'pg_payment_method_id' => $profile->pg_payment_method_id,
                'active' => 1,
            ])
            ->whereIn('type', [PgProfileType::BANK_ACCOUNT, PgProfileType::NAPAS_VIETQR])
            ->countAllResults();

        if ($profileCount <= 1) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Không thể xóa tài khoản thụ hưởng cuối cùng. Phải có ít nhất một tài khoản thụ hưởng.',
            ]);
        }

        if ($profile->default) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Không thể xóa tài khoản thụ hưởng mặc định. Vui lòng chọn tài khoản thụ hưởng khác làm mặc định trước khi xóa.',
            ]);
        }

        if ($profile->type === PgProfileType::NAPAS_VIETQR) {
            $napasRecord = model(NapasVietQrProfileModel::class)
                ->where('pg_profile_id', $profile->id)
                ->first();

            if ($napasRecord) {
                return $this->response->setJSON([
                    'status' => false,
                    'message' => 'Không thể xóa profile NAPAS VietQR đang có dữ liệu liên kết. Vui lòng liên hệ quản trị viên.',
                ]);
            }
        }

        model(PgProfileModel::class)->delete($profile->id);

        return $this->response->setJSON([
            'status' => true,
            'message' => 'Đã xóa tài khoản thụ hưởng thành công'
        ]);
    }

    public function toggleFeature()
    {
        if (! $this->request->isAJAX()) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Yêu cầu không hợp lệ',
            ]);
        }

        $data = $this->request->getPost();

        if (! $this->validate([
            'feature_id' => 'required',
            'action' => 'required|in_list[enable,disable]',
        ])) {
            return $this->response->setJSON([
                'status' => false,
                'errors' => $this->validator->getErrors()
            ]);
        }

        $validFeatures = ['napas_qr', 'card'];

        if (!in_array($data['feature_id'], $validFeatures)) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Tính năng không hợp lệ'
            ]);
        }

        if ($data['action'] === 'enable') {
            model(OnboardingSubmissionModel::class)->createOnboardingRecord($this->company_details->id, $data['feature_id']);

            return $this->response->setJSON([
                'status' => true,
                'message' => 'Đang chuyển hướng đến quá trình đăng ký...',
                'redirect' => base_url("onboarding?feature={$data['feature_id']}&step=1")
            ]);
        } else if ($data['action'] === 'disable') {
            model(OnboardingSubmissionModel::class)->updateStatus($this->company_details->id, $data['feature_id'], 'rejected');

            return $this->response->setJSON([
                'status' => true,
                'message' => 'Đã tạm ngưng tính năng thành công'
            ]);
        }

        return $this->response->setJSON([
            'status' => false,
            'message' => 'Hành động không hợp lệ'
        ]);
    }

    public function togglePaymentMethod()
    {
        if (! $this->request->isAJAX()) {
            return $this->response->setJSON(['status' => false, 'message' => 'Invalid request']);
        }

        $data = $this->request->getPost();

        if (! $this->validate([
            'payment_method' => 'required|in_list[' . implode(',', [PgPaymentMethod::BANK_TRANSFER, PgPaymentMethod::CARD]) . ']',
            'action' => 'required|in_list[enable,disable]'
        ])) {
            return $this->response->setJSON([
                'status' => false,
                'errors' => $this->validator->getErrors()
            ]);
        }

        $merchant = model(PgMerchantModel::class)
            ->where('company_id', $this->company_details->id)
            ->first();

        if (! $merchant) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Merchant chưa được tạo',
            ]);
        }

        $existing = model(PgPaymentMethodModel::class)
            ->where([
                'pg_merchant_id' => $merchant->id,
                'payment_method' => $data['payment_method'],
            ])
            ->first();

        if (! $existing) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Phương thức thanh toán không tồn tại'
            ]);
        }

        if ($data['action'] === 'enable') {
            model(PgPaymentMethodModel::class)->update($existing->id, ['active' => 1]);

            return $this->response->setJSON([
                'status' => true,
                'message' => 'Đã kích hoạt phương thức thanh toán'
            ]);
        } else {
            model(PgPaymentMethodModel::class)->update($existing->id, ['active' => 0]);

            return $this->response->setJSON([
                'status' => true,
                'message' => 'Đã tạm ngưng phương thức thanh toán'
            ]);
        }
    }

    private function getPaymentMethods($merchantId)
    {
        if (! $merchantId) return [];

        return model(PgPaymentMethodModel::class)
            ->where('pg_merchant_id', $merchantId)
            ->findAll();
    }

    private function getBankAccounts()
    {
        return model(BankAccountModel::class)
            ->join('tb_autopay_bank', 'tb_autopay_bank.id = tb_autopay_bank_account.bank_id')
            ->where('tb_autopay_bank_account.company_id', $this->company_details->id)
            ->where('tb_autopay_bank_account.active', 1)
            ->findAll();
    }

    private function getOnboardingStatus()
    {
        $methods = ['card', 'napas_qr'];
        $status = [];

        foreach ($methods as $method) {
            $submission = model(OnboardingSubmissionModel::class)
                ->getByCompanyAndMethod($this->company_details->id, $method);

            $status[$method] = $submission ? $submission->status : 'inactive';
        }

        return $status;
    }

    private function getPaymentFeatures()
    {
        $features = [
            [
                'id' => 'bank_qr',
                'payment_method' => 'bank_qr',
                'name' => 'Chuyển khoản ngân hàng qua QR',
                'description' => 'Nhận thanh toán trực tiếp vào tài khoản ngân hàng thông qua mã QR. Sử dụng ngay không cần đăng ký.',
                'icon' => 'bi-bank',
                'features' => [
                    'Chuyển khoản trực tiếp vào TK ngân hàng',
                    'Không cần đăng ký phức tạp',
                    'Sử dụng ngay lập tức',
                ],
                'is_default_enabled' => true,
            ],
            [
                'id' => 'napas_qr',
                'payment_method' => 'napas_qr',
                'name' => 'QR theo đơn hàng NAPAS',
                'description' => 'Thanh toán bằng QR code chuẩn quốc gia NAPAS theo từng đơn hàng cụ thể. Hỗ trợ tất cả ngân hàng trong nước.',
                'icon' => 'bi bi-qr-code',
                'features' => [
                    'QR chuẩn quốc gia NAPAS',
                    'Theo dõi từng đơn hàng',
                    'Phí theo % giao dịch',
                    'Hỗ trợ tất cả ngân hàng nội địa'
                ]
            ],
            [
                'id' => 'card',
                'payment_method' => 'card',
                'name' => 'Thanh toán bằng thẻ',
                'description' => 'Chấp nhận thanh toán bằng thẻ tín dụng/ghi nợ quốc tế và trong nước. Được cung cấp bởi VPBank.',
                'icon' => 'bi-credit-card',
                'features' => [
                    'Thẻ Visa, Mastercard, JCB',
                    'Thẻ ATM nội địa',
                    'Phí theo % giao dịch',
                    'Bảo mật 3D Secure',
                ]
            ],
        ];

        foreach ($features as &$feature) {
            if (isset($feature['is_default_enabled']) && $feature['is_default_enabled']) {
                $feature['status'] = 'available';
                $feature['onboard_completed'] = true;
                $feature['current_step'] = null;
            } else {
                $submission = model(OnboardingSubmissionModel::class)->getByCompanyAndMethod($this->company_details->id, $feature['payment_method']);

                if ($submission) {
                    $feature['status'] = $submission->status;
                    $feature['onboard_completed'] = $submission->submitted_at !== null;
                    $feature['current_step'] = $submission->current_step ?: 1;
                } else {
                    $feature['status'] = 'inactive';
                    $feature['onboard_completed'] = false;
                }
            }
        }

        return $features;
    }

    private function getBankProfiles($merchantId, $paymentMethodId)
    {
        if (! $paymentMethodId) return [];

        $bankAccountProfiles = model(PgProfileModel::class)
            ->select('tb_autopay_pg_profile.*, tb_autopay_bank_account.account_number, tb_autopay_bank_account.account_holder_name, bank.brand_name, bank.logo_path, bank.icon_path')
            ->join('tb_autopay_bank_account', 'tb_autopay_bank_account.id = tb_autopay_pg_profile.bank_account_id')
            ->join('tb_autopay_bank bank', 'bank.id = tb_autopay_bank_account.bank_id')
            ->where([
                'tb_autopay_pg_profile.pg_merchant_id' => $merchantId,
                'tb_autopay_pg_profile.pg_payment_method_id' => $paymentMethodId,
                'tb_autopay_pg_profile.type' => PgProfileType::BANK_ACCOUNT,
                'tb_autopay_pg_profile.active' => 1,
                'tb_autopay_bank_account.active' => 1,
                'tb_autopay_pg_profile.company_id' => $this->company_details->id,
            ])
            ->findAll();

        $napasProfiles = model(PgProfileModel::class)
            ->select('tb_autopay_pg_profile.*, napas.acquiring_account_holder_name as account_holder_name, napas.vac as account_number, "NAPAS VietQR" as brand_name, "" as logo_path, "" as icon_path')
            ->join('tb_autopay_napas_vietqr_profile napas', 'napas.pg_profile_id = tb_autopay_pg_profile.id')
            ->where([
                'tb_autopay_pg_profile.pg_merchant_id' => $merchantId,
                'tb_autopay_pg_profile.pg_payment_method_id' => $paymentMethodId,
                'tb_autopay_pg_profile.type' => PgProfileType::NAPAS_VIETQR,
                'tb_autopay_pg_profile.active' => 1,
                'tb_autopay_pg_profile.company_id' => $this->company_details->id,
            ])
            ->findAll();

        $allProfiles = array_merge($bankAccountProfiles, $napasProfiles);

        usort($allProfiles, function ($a, $b) {
            if ($a->default != $b->default) {
                return $b->default - $a->default;
            }
            return $a->id - $b->id;
        });

        return $allProfiles;
    }

    private function getAvailableBankAccounts()
    {
        return model(BankAccountModel::class)
            ->select('tb_autopay_bank_account.*, bank.brand_name, bank.logo_path, bank.icon_path')
            ->join('tb_autopay_bank bank', 'bank.id = tb_autopay_bank_account.bank_id')
            ->where([
                'tb_autopay_bank_account.company_id' => $this->company_details->id,
                'tb_autopay_bank_account.active' => 1,
            ])
            ->orderBy('tb_autopay_bank_account.id', 'DESC')
            ->findAll();
    }

    private function getPaymentMethodExistence($merchantId)
    {
        if (! $merchantId) return [];

        $paymentMethods = model(PgPaymentMethodModel::class)
            ->where('pg_merchant_id', $merchantId)
            ->where('company_id', $this->company_details->id)
            ->findAll();

        $existence = [];

        foreach ($paymentMethods as $method) {
            $existence[$method->payment_method] = true;
        }

        return $existence;
    }
}
