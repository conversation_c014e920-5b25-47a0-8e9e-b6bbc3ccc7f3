<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Models\PgMerchantModel;
use App\Models\PgPaymentMethodModel;
use App\Models\PgProfileModel;
use App\Models\BankAccountModel;
use App\Models\OnboardingSubmissionModel;

class Paymentmethods extends BaseController
{
    public function index()
    {
        $merchant = model(PgMerchantModel::class)
            ->where('company_id', $this->company_details->id)
            ->first();

        $data = [
            'page_title' => 'Phương thức thanh toán',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'merchant' => $merchant,
            'payment_methods' => $this->getPaymentMethods($merchant->id ?? null),
            'bank_accounts' => $this->getBankAccounts(),
            'onboarding_status' => $this->getOnboardingStatus(),
            'features' => $this->getPaymentFeatures()
        ];

        return view('payment-methods/index', $data);
    }

    public function toggleFeature()
    {
        if (! $this->request->isAJAX()) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Yêu cầu không hợp lệ',
            ]);
        }

        $data = $this->request->getPost();

        if (! $this->validate([
            'feature_id' => 'required',
            'action' => 'required|in_list[enable,disable]',
        ])) {
            return $this->response->setJSON([
                'status' => false,
                'errors' => $this->validator->getErrors()
            ]);
        }

        $validFeatures = ['napas_qr', 'card'];

        if (!in_array($data['feature_id'], $validFeatures)) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Tính năng không hợp lệ'
            ]);
        }

        if ($data['action'] === 'enable') {
            model(OnboardingSubmissionModel::class)->createOnboardingRecord($this->company_details->id, $data['feature_id']);

            return $this->response->setJSON([
                'status' => true,
                'message' => 'Đang chuyển hướng đến quá trình đăng ký...',
                'redirect' => base_url("onboarding?feature={$data['feature_id']}&step=1")
            ]);
        } else if ($data['action'] === 'disable') {
            model(OnboardingSubmissionModel::class)->updateStatus($this->company_details->id, $data['feature_id'], 'rejected');

            return $this->response->setJSON([
                'status' => true,
                'message' => 'Đã tạm ngưng tính năng thành công'
            ]);
        }

        return $this->response->setJSON([
            'status' => false,
            'message' => 'Hành động không hợp lệ'
        ]);
    }

    public function togglePaymentMethod()
    {
        if (! $this->request->isAJAX()) {
            return $this->response->setJSON(['status' => false, 'message' => 'Invalid request']);
        }

        $data = $this->request->getPost();

        if (! $this->validate([
            'payment_method' => 'required|in_list[BANK_TRANSFER,CARD]',
            'action' => 'required|in_list[enable,disable]'
        ])) {
            return $this->response->setJSON([
                'status' => false,
                'errors' => $this->validator->getErrors()
            ]);
        }

        $merchant = model(PgMerchantModel::class)
            ->where('company_id', $this->company_details->id)
            ->first();

        if (! $merchant) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Merchant chưa được tạo',
            ]);
        }

        $existing = model(PgPaymentMethodModel::class)
            ->where([
                'pg_merchant_id' => $merchant->id,
                'payment_method' => $data['payment_method'],
            ])
            ->first();

        if (! $existing) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Phương thức thanh toán không tồn tại'
            ]);
        }

        if ($data['action'] === 'enable') {
            model(PgPaymentMethodModel::class)->update($existing->id, ['active' => 1]);

            return $this->response->setJSON([
                'status' => true,
                'message' => 'Đã kích hoạt phương thức thanh toán'
            ]);
        } else {
            model(PgPaymentMethodModel::class)->update($existing->id, ['active' => 0]);

            return $this->response->setJSON([
                'status' => true,
                'message' => 'Đã tạm ngưng phương thức thanh toán'
            ]);
        }
    }

    private function getPaymentMethods($merchantId)
    {
        if (! $merchantId) return [];

        return model(PgPaymentMethodModel::class)
            ->where('pg_merchant_id', $merchantId)
            ->findAll();
    }

    private function getBankAccounts()
    {
        return model(BankAccountModel::class)
            ->join('tb_autopay_bank', 'tb_autopay_bank.id = tb_autopay_bank_account.bank_id')
            ->where('tb_autopay_bank_account.company_id', $this->company_details->id)
            ->where('tb_autopay_bank_account.active', 1)
            ->findAll();
    }

    private function getOnboardingStatus()
    {
        $methods = ['card', 'napas_qr'];
        $status = [];

        foreach ($methods as $method) {
            $submission = model(OnboardingSubmissionModel::class)
                ->getByCompanyAndMethod($this->company_details->id, $method);

            $status[$method] = $submission ? $submission->status : 'inactive';
        }

        return $status;
    }

    private function getPaymentFeatures()
    {
        $features = [
            [
                'id' => 'bank_qr',
                'payment_method' => 'bank_qr',
                'name' => 'Chuyển khoản ngân hàng qua QR',
                'description' => 'Nhận thanh toán trực tiếp vào tài khoản ngân hàng thông qua mã QR. Sử dụng ngay không cần đăng ký.',
                'icon' => 'bi-bank',
                'features' => [
                    'Chuyển khoản trực tiếp vào TK ngân hàng',
                    'Không cần đăng ký phức tạp',
                    'Sử dụng ngay lập tức',
                ],
                'is_default_enabled' => true,
            ],
            [
                'id' => 'napas_qr',
                'payment_method' => 'napas_qr',
                'name' => 'QR theo đơn hàng NAPAS',
                'description' => 'Thanh toán bằng QR code chuẩn quốc gia NAPAS theo từng đơn hàng cụ thể. Hỗ trợ tất cả ngân hàng trong nước.',
                'icon' => 'bi bi-qr-code',
                'features' => [
                    'QR chuẩn quốc gia NAPAS',
                    'Theo dõi từng đơn hàng',
                    'Phí theo % giao dịch',
                    'Hỗ trợ tất cả ngân hàng nội địa'
                ]
            ],
            [
                'id' => 'card',
                'payment_method' => 'card',
                'name' => 'Thanh toán bằng thẻ',
                'description' => 'Chấp nhận thanh toán bằng thẻ tín dụng/ghi nợ quốc tế và trong nước. Được cung cấp bởi VPBank.',
                'icon' => 'bi-credit-card',
                'features' => [
                    'Thẻ Visa, Mastercard, JCB',
                    'Thẻ ATM nội địa',
                    'Phí theo % giao dịch',
                    'Bảo mật 3D Secure',
                ]
            ],
        ];

        foreach ($features as &$feature) {
            if (isset($feature['is_default_enabled']) && $feature['is_default_enabled']) {
                $feature['status'] = 'available';
                $feature['onboard_completed'] = true;
                $feature['current_step'] = null;
            } else {
                $submission = model(OnboardingSubmissionModel::class)->getByCompanyAndMethod($this->company_details->id, $feature['payment_method']);

                if ($submission) {
                    $feature['status'] = $submission->status;
                    $feature['onboard_completed'] = $submission->submitted_at !== null;
                    $feature['current_step'] = $submission->current_step ?: 1;
                } else {
                    $feature['status'] = 'inactive';
                    $feature['onboard_completed'] = false;
                }
            }
        }

        return $features;
    }
}
