<?php

namespace App\Libraries;

use CodeIgniter\HTTP\CURLRequest;
use Config\Ghtk;
use Config\Services;
use Exception;
use App\Libraries\ProxyCURLRequest;

class GhtkClient
{
    protected const BASE_URL = 'https://services.giaohangtietkiem.vn';

    protected const ENDPOINTS = [
        'AUTHENTICATED' => '/services/authenticated',
        'CREATE_ORDER' => '/services/shipment/order',
        'CALCULATE_FEE' => '/services/shipment/fee',
        'PRINT_LABEL' => '/services/label/',
        'CANCEL_ORDER' => '/services/shipment/cancel/',
        'TRACK_ORDER' => '/services/shipment/tracking',
    ];

    protected CURLRequest $client;

    protected Ghtk $config;

    public function __construct()
    {
        $this->client = ProxyCURLRequest::make()->setProxy('ghtk');
        $this->config = config(Ghtk::class);
    }

    public function authenticate()
    {
        return $this->request('GET', self::ENDPOINTS['AUTHENTICATED']);
    }

    public function createOrder(array $orderData)
    {
        return $this->request('POST', self::ENDPOINTS['CREATE_ORDER'], ['order' => $orderData]);
    }

    public function calculateFee(array $data)
    {
        return $this->request('POST', self::ENDPOINTS['CALCULATE_FEE'], $data);
    }

    public function printLabel(string $orderId)
    {
        return $this->request('GET', self::ENDPOINTS['PRINT_LABEL'] . $orderId);
    }

    public function cancelOrder(string $orderId)
    {
        return $this->request('POST', self::ENDPOINTS['CANCEL_ORDER'] . $orderId);
    }

    public function trackOrder(string $orderId)
    {
        return $this->request('GET', self::ENDPOINTS['TRACK_ORDER'], ['label_id' => $orderId]);
    }

    protected function request(string $method, string $endpoint, array $data = [], string $contentType = 'json')
    {
        $url = self::BASE_URL . $endpoint;

        $headers = [
            'Token' => $this->config->apiToken,
        ];

        if (! empty($this->config->partnerCode)) {
            $headers['X-Client-Source'] = $this->config->partnerCode;
        }

        $options = ['headers' => $headers];

        if ($contentType === 'json') {
            $headers['Content-Type'] = 'application/json';
            $options['json'] = $data;
        } else {
            $headers['Content-Type'] = 'application/x-www-form-urlencoded';
            $options['form_params'] = $data;
        }

        try {
            $response = $this->client->request($method, $url, $options);
            $body = $response->getBody();

            return json_decode($body, true) ?: [
                'success' => false,
                'message' => 'Invalid response format',
                'log_id' => uniqid('err_')
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage(),
                'error_code' => '10101',
                'log_id' => uniqid('err_')
            ];
        }
    }
}
