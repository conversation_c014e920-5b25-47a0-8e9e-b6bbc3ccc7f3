<?php

namespace App\Libraries;

use CodeIgniter\HTTP\Response;

class MPGSRestApiClient
{
    protected string $baseUrl = 'https://test-gateway.mastercard.com/api/rest';
    
    protected string $version = '100';
    
    protected string $merchantId;
    
    protected string $username;
    
    protected string $password;
    
    protected int $timeout = 30;

    public function __construct(string $merchantId, string $username, string $password)
    {
        $this->merchantId = $merchantId;
        $this->username = $username;
        $this->password = $password;
    }
    /**
     * @param array<int,mixed> $data
     */
    public function requestInitiateCheckout(array $data): Response
    {
        $data['apiOperation'] = 'INITIATE_CHECKOUT';
        $data['interaction']['displayControl']['billingAddress'] = 'HIDE';
        $data['order']['currency'] = 'VND';
        
        return $this->makeRequest('POST', sprintf('/merchant/%s/session', $this->getMerchantId()), $data);
    }
    
    public function retrieveSession(string $sessionId): Response
    {
        return $this->makeRequest('GET', sprintf('/merchant/%s/session/%s', $this->getMerchantId(), $sessionId));
    }
    
    public function createToken(string $sessionId): Response
    {
        return $this->makeRequest('POST', sprintf('/merchant/%s/token', $this->getMerchantId()), [
            'session' => ['id' => $sessionId]
        ]);
    }
    
    public function createSession(): Response
    {
        return $this->makeRequest('POST', sprintf('/merchant/%s/session', $this->getMerchantId()));
    }
    
    public function initAuthenticationToken(
        string $paymentToken, 
        string $orderId,
        string $transactionId
    ): Response
    {
        $data = [
            "authentication" => [
                "acceptVersions" => "3DS1,3DS2",
                "channel" => "PAYER_BROWSER",
                "purpose" => "PAYMENT_TRANSACTION"
            ],
            "order" => [
                "currency" => "VND"
            ],
            "sourceOfFunds" => [
                "token" => $paymentToken,
                "type" => "SCHEME_TOKEN"
            ],
            "apiOperation" => "INITIATE_AUTHENTICATION"
        ];
        
        return $this->makeRequest('PUT', sprintf('/merchant/%s/order/%s/transaction/%s', $this->getMerchantId(), $orderId, $transactionId), $data);
    }
    
    /**
     * @see
     */
    public function authenticatePayerToken(
        string $redirectResponseUrl,
        string $paymentToken, 
        string $orderId,
        int $orderAmount,
        string $orderCurrency,
        ?string $orderDescription = null,
        ?string $orderInvoiceNumber = null,
        ?int $orderDiscountAmount = null,
        ?string $orderDiscountCode = null,
        ?string $orderDiscountDescription = null,
        ?int $orderTaxAmount = null,
        string $transactionId, 
        string $agreementId,
        int $agreementAmountPerPayment,
        string $agreementType,
        string $agreementPaymentFrequency,
        string $agreementAmountVariability,
        string $agreementExpiryDate,
        ?int $agreementMinimumDaysBetweenPayments = null
    ): Response
    {
        helper(['general']);
        
        $data = [
            'authentication' => [
                'redirectResponseUrl' => $redirectResponseUrl,
            ],
            'device' => [
                'browser' => 'MOZILLA',
                'browserDetails' => [
                    '3DSecureChallengeWindowSize' => 'FULL_SCREEN',
                    'acceptHeaders' => 'application/json',
                    'colorDepth' => 24,
                    'javaEnabled' => true,
                    'language' => 'en-US',
                    'screenHeight' => 640,
                    'screenWidth' => 480,
                    'timeZone' => 273
                ],
                'ipAddress' => '127.0.0.1'
            ],
            'order' => [
                'currency' => $orderCurrency,
                'amount' => $orderAmount,
                'description' => $orderDescription ? remove_accents_with_intl($orderDescription) : null,
                'invoiceNumber' => $orderInvoiceNumber,
                'itemAmount' => $agreementAmountPerPayment,
                'discount' => [
                    'amount' => $orderDiscountAmount,
                    'code' => $orderDiscountCode,
                    'description' => $orderDiscountDescription ? remove_accents_with_intl($orderDiscountDescription) : null,
                ],
                'taxAmount' => $orderTaxAmount,
            ],
            'sourceOfFunds' => [
                'token' => $paymentToken,
            ],
            'agreement' => [
                'id' => $agreementId,
                'type' => $agreementType,
                'amountVariability' => $agreementAmountVariability,
                'paymentFrequency' => $agreementPaymentFrequency,
                'startDate' => date('Y-m-d'),
                'expiryDate' => $agreementExpiryDate,
                'minimumDaysBetweenPayments' => $agreementMinimumDaysBetweenPayments,
                'minimumAmountPerPayment' => $agreementAmountVariability === 'VARIABLE' ? $orderAmount : null,
                'maximumAmountPerPayment' => $agreementAmountVariability === 'VARIABLE' ? $agreementAmountPerPayment : null
            ],
            'apiOperation' => 'AUTHENTICATE_PAYER'
        ];
        
        return $this->makeRequest('PUT', sprintf('/merchant/%s/order/%s/transaction/%s', $this->getMerchantId(), $orderId, $transactionId), $data);
    }
    
    public function payToken(
        string $paymentToken, 
        string $orderId,
        int $orderAmount,
        string $orderCurrency,
        ?string $orderDescription = null,
        ?string $orderInvoiceNumber = null,
        ?int $orderDiscountAmount = null,
        ?string $orderDiscountCode = null,
        ?string $orderDiscountDescription = null,
        ?int $orderTaxAmount = null,
        string $transactionId,
        string $agreementId,
        int $agreementAmountPerPayment,
        string $agreementType,
        string $agreementPaymentFrequency,
        string $agreementAmountVariability,
        ?int $agreementMinimumDaysBetweenPayments = null): Response
    {
        $data = [
            'order' => [
                'currency' => $orderCurrency,
                'amount' => $orderAmount,
                'reference' => $orderId,
                'description' => $orderDescription ? remove_accents_with_intl($orderDescription) : null,
                'invoiceNumber' => $orderInvoiceNumber,
                'itemAmount' => $agreementAmountPerPayment,
                'discount' => [
                    'amount' => $orderDiscountAmount,
                    'code' => $orderDiscountCode,
                    'description' => $orderDiscountDescription ? remove_accents_with_intl($orderDiscountDescription) : null,
                ],
                'taxAmount' => $orderTaxAmount,
            ],
            'sourceOfFunds' => [
                'token' => $paymentToken,
                'type' => 'SCHEME_TOKEN',
                'provided' => [
                    'card' => [
                        'storedOnFile' => 'TO_BE_STORED'
                    ]
                ]
            ],
            'agreement' => [
                'id' => $agreementId,
                'type' => $agreementType,
                'amountVariability' => $agreementAmountVariability,
                'paymentFrequency' => $agreementPaymentFrequency,
                'startDate' => date('Y-m-d'),
                'expiryDate' => date('Y-m-d', strtotime('+3 year')),
                'minimumDaysBetweenPayments' => $agreementMinimumDaysBetweenPayments,
                'minimumAmountPerPayment' => $agreementAmountVariability === 'VARIABLE' ? $orderAmount : null,
                'maximumAmountPerPayment' => $agreementAmountVariability === 'VARIABLE' ? $agreementAmountPerPayment : null
            ],
            'transaction' => [
                'reference' => $orderId,
                'source' => 'INTERNET'
            ],
            'apiOperation' => 'PAY'
        ];
        
        return $this->makeRequest('PUT', sprintf('/merchant/%s/order/%s/transaction/%s', $this->getMerchantId(), $orderId, $transactionId), $data);
    }
    
    public function payTokenSubsequence(
        string $paymentToken,
        string $orderId,
        string $orderAmount,
        ?string $orderDescription = null,
        ?string $orderInvoiceNumber = null,
        string $orderCurrency,
        string $transactionId,
        string $agreementId
    ): Response
    {
        helper(['general']);
        
        $data = [
            'apiOperation' => 'PAY',
            'order' => [
                'amount' => $orderAmount,
                'currency' => $orderCurrency,
                'reference' => $orderId,
                'description' => $orderDescription ? remove_accents_with_intl($orderDescription) : null,
                'invoiceNumber' => $orderInvoiceNumber,
            ],
            'sourceOfFunds' => [
                'token' => $paymentToken,
                'type' => 'SCHEME_TOKEN',
                'provided' => [
                    'card' => [
                        'storedOnFile' => 'STORED'
                    ]
                ]
            ],
            'agreement' => [
                'id' => $agreementId
            ],
            'transaction' => [
                'reference' => $orderId,
                'source' => 'MERCHANT'
            ]
        ];
        
        return $this->makeRequest('PUT', sprintf('/merchant/%s/order/%s/transaction/%s', $this->getMerchantId(), $orderId, $transactionId), $data);
    }
    
    public function retrieveOrder(string $orderId): Response
    {
        return $this->makeRequest('GET', sprintf('/merchant/%s/order/%s', $this->getMerchantId(), $orderId));
    }
    
    public function retrieveTransaction(string $orderId, string $transactionId): Response
    {
        return $this->makeRequest('GET', sprintf('/merchant/%s/order/%s/transaction', $this->getMerchantId(), $orderId, $transactionId));
    }
    
    public function retrieveAgreement(string $agreementId): Response
    {
        return $this->makeRequest('GET', sprintf('/merchant/%s/agreement/%s', $this->getMerchantId(), $agreementId));
    }
    
    public function deleteToken(string $paymentToken): Response
    {
        return $this->makeRequest('DELETE', sprintf('/merchant/%s/token/%s', $this->getMerchantId(), $paymentToken));
    }
    
    public function retrieveToken(string $paymentToken): Response
    {
        return $this->makeRequest('GET', sprintf('/merchant/%s/token/%s', $this->getMerchantId(), $paymentToken));
    }
    
    public function voidTransaction(string $orderId, string $transactionId, string $targetTransactionId): Response
    {
        $data = [
            'apiOperation' => 'VOID',
            'transaction' => [
                'targetTransactionId' => $targetTransactionId,
            ]
        ];
        
        return $this->makeRequest('PUT', sprintf('/merchant/%s/order/%s/transaction/%s', $this->getMerchantId(), $orderId, $transactionId), $data);
    }
    
    protected function getMerchantId(): string
    {
        return $this->merchantId;
    }
    
    /**
     * @param array<string,mixed> $data
     */
    protected function makeRequest(string $method, string $uri, ?array $data = null): Response
    {
        $method = strtoupper($method);
        
        $headers = [
            'Content-Type' => 'application/json'
        ];
        
        $rawBody = $data ? json_encode($data) : '';
        
        $options = [
            'headers' => $headers,
            'auth' => [$this->username, $this->password],
            'http_errors' => false,
            'body' => $rawBody,
            'timeout' => $this->timeout
        ];
        
        /** @var Response $response */
        $response = $this->makeNewHttpClient()->request($method, $this->endpoint($uri), $options);
        
        log_message('error', '[MPGS_REST_API]: ' . $response->getStatusCode() . ' ' . $method . ' ' . $this->endpoint($uri) . ' | Raw body: ' . $rawBody . ' | Response: ' . $response->getBody());
        
        return $response;
    }
    
    protected function makeNewHttpClient(): ProxyCURLRequest
    {
        return ProxyCURLRequest::make()->setProxy('mpgs');
    }
    
    protected function endpoint(string $path): string
    {
        return sprintf('%s/version/%s/%s', trim($this->baseUrl, '/'), $this->version, trim($path, '/'));
    }
}
