<?php

namespace App\Libraries;

use Config\NapasConfig;
use CodeIgniter\HTTP\Response;

class NapasClient
{
    protected string $clientBaseUrl;
    protected string $clientUsername;
    protected string $clientPassword;
    protected int $clientTtl;
    protected string $clientNapasCertPath;
    protected string $clientSePayPrivateKeyPath;
    protected string $clientSepayId;
    protected string $clientNapasId;

    public function __construct()
    {
        /** @var NapasConfig $config */
        $config = config(NapasConfig::class);

        $this->clientBaseUrl = $config->clientBaseUrl;
        $this->clientUsername = $config->clientUsername;
        $this->clientPassword = $config->clientPassword;
        $this->clientTtl = $config->clientTtl;
        $this->clientNapasCertPath = $config->clientNapasCertPath;
        $this->clientSePayPrivateKeyPath = $config->clientSePayPrivateKeyPath;
        $this->clientSepayId = $config->clientSepayId;
        $this->clientNapasId = $config->clientNapasId;
    }

    public function makeNewClient(): ProxyCURLRequest
    {
        $client = ProxyCURLRequest::make()->setProxy('napas');

        if (getenv('CI_ENVIRONMENT') == 'development') {
            $client->setAdditionalCURLOptions([
                CURLOPT_PROXYTYPE => CURLPROXY_SOCKS5,
            ]);
        }

        return $client->setAdditionalCURLOptions([
            CURLOPT_SSLCERT => $this->clientNapasCertPath,
            CURLOPT_SSLKEY => $this->clientSePayPrivateKeyPath,
        ]);
    }

    private function getAccessToken(): string
    {
        $client = $this->makeNewClient();

        /** @var Response */
        $response = $client->post(rtrim($this->clientBaseUrl, '/') . '/apg/oauth2/token', [
            'headers' => [
                'Content-Type' => 'application/x-www-form-urlencoded'
            ],
            'form_params' => [
                'client_id' => $this->clientUsername,
                'client_secret' => $this->clientPassword,
                'grant_type' => 'client_credentials'
            ],
            'http_errors' => false,
            'timeout' => $this->clientTtl,
        ]);
        
        $body = json_decode($response->getBody(), true);

        if ($response->getStatusCode() !== 200 || !isset($body['access_token'])) {
            throw new \RuntimeException('[NAPAS_CLIENT] Failed to retrieve access token: ' . $response->getBody());
        }

        return $body['access_token'];
    }

    public function investigation(string $id)
    {
        $client = $this->makeNewClient();

        $headers = [
            'Content-Type' => 'application/json',
            'Authorization' => 'Bearer ' . $this->getAccessToken()
        ];

        $referenceNumber = date('YmdHis') . $this->clientSepayId . '02' . time() . str_pad(rand(0, 99), 2, '0', STR_PAD_LEFT);
        $payload = [
            'caseId' => $referenceNumber,
            'creationDateTime' => date('c'),
            'id' => $id,
        ];

        $data = [
            'header' => [
                'messageIdentifier' => 'investrequest',
                'creationDateTime' => date('c'),
                'senderReference' => $referenceNumber,
                'senderId' => $this->clientSepayId,
                'receiverId' => $this->clientNapasId,
                'signature' => $this->generateSignature(json_encode($payload, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE))
            ],
            'payload' => $payload
        ];

        /** @var Response */
        $response = $client->post(rtrim($this->clientBaseUrl, '/') . '/apg/investigation', [
            'headers' => $headers,
            'json' => $data,
            'http_errors' => false,
            'timeout' => $this->clientTtl,
        ]);

        return $response;
    }

    protected function generateSignature(string $rawText): string
    {
        $privateKey = openssl_pkey_get_private(file_get_contents($this->clientSePayPrivateKeyPath));

        if (!$privateKey) {
            throw new \RuntimeException('[NAPAS_CLIENT] Failed to load private key');
        }

        $signature = '';
        $isSuccess = openssl_sign($rawText, $signature, $privateKey, OPENSSL_ALGO_SHA256);

        if (!$isSuccess) {
            throw new \RuntimeException('[NAPAS_CLIENT] Failed to generate signature');
        }

        return base64_encode($signature);
    }
}